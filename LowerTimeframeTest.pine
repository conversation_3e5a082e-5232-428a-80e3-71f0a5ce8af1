//@version=5
indicator("Lower Timeframe Function Test", overlay=true)

// --- Function to be Tested ---
getLowerTimeframe(strategy, htf) =>
    switch strategy
        "W > D > 4H"    => htf == "W" ? "D" : htf == "D" ? "240" : na
        "D > 4H > 1H"    => htf == "D" ? "240" : htf == "240" ? "60" : na
        "4H > 1H > 15m"  => htf == "240" ? "60" : htf == "60" ? "15" : na
        => na // Default for "Off"

// --- Test Execution ---
if barstate.islast
    // Test Case 1
    strategy1 = "W > D > 4H"
    htf1 = "D"
    result1 = getLowerTimeframe(strategy1, htf1)
    label.new(bar_index, high, "Test 1: " + strategy1 + " | " + htf1 + " -> " + str.tostring(result1), yloc=yloc.abovebar)

    // Test Case 2
    strategy2 = "D > 4H > 1H"
    htf2 = "D"
    result2 = getLowerTimeframe(strategy2, htf2)
    label.new(bar_index, high, "Test 2: " + strategy2 + " | " + htf2 + " -> " + str.tostring(result2), yloc=yloc.price)

    // Test Case 3
    strategy3 = "4H > 1H > 15m"
    htf3 = "60"
    result3 = getLowerTimeframe(strategy3, htf3)
    label.new(bar_index, low, "Test 3: " + strategy3 + " | " + htf3 + " -> " + str.tostring(result3), yloc=yloc.belowbar)
    
    // Test Case 4 (Edge case)
    strategy4 = "W > D > 4H"
    htf4 = "240" // Invalid input for this strategy
    result4 = getLowerTimeframe(strategy4, htf4)
    label.new(bar_index, low, "Test 4: " + strategy4 + " | " + htf4 + " -> " + str.tostring(result4), yloc=yloc.price, textcolor=color.red)
