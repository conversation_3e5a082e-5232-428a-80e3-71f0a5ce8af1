// @version=6
indicator("PAH", overlay = true, max_lines_count = 500, max_labels_count = 500, max_boxes_count = 500, max_polylines_count = 100)
import gigi_duru/PAHelperFramework/64
var _firstRun = true
var float _RoundingInterval = 0
var string __SnapMethod = "Round"
var bool __isInSession = false
var bool __InSessionNotificationFilter = false

_roundingMethod = __SnapMethod == "Round" ? 0 : close < close[1] ? -1 : 1

_candleLow = PAHelperFramework.roundToInterval(low, _RoundingInterval, 1)
_candleHigh = PAHelperFramework.roundToInterval(high, _RoundingInterval, -1)
_candleOpen = PAHelperFramework.roundToInterval(open, _RoundingInterval, _roundingMethod)
_candleClose = PAHelperFramework.roundToInterval(close, _RoundingInterval, _roundingMethod)

_rawLow = low
_rawHigh = high
_rawClose = close
_rawOpen = close[1]

_time = time
_timeClose = time_close

_isBullish = _candleClose[1] < _candleClose

var _Template = input.string(title = "Template", defval = "-", options = ["-", "Smooth candles", "Snapped candles", "Bias", "Levels", "All levels", "Heatmap", "NTAs", "LTAs", "Opening hours"], inline = "Template", display = display.status_line)
var _Theme = input.string("Dark theme", title = "", options = ["Dark theme", "Light theme"], inline = "Template", display = display.none)
var _IndicatorDescription = input.string(title = "Info", defval = "", inline = "Template", tooltip = "For a quick setup and efficiency, a template can be applied. Please note that the selected template will override several settings below, but also allows complementary settings.\n\nAdditional information about the configuration can be set, which TradingView will display on the indicator's line", display = display.status_line)

var _MinTick = syminfo.mintick

var _atrSize = ta.atr(128) / _MinTick

var _ShowLines = input.bool(true, title = "Show support & resistance level lines (markup)", group = "Support & resistance levels", inline = "ShowMarkup", display = display.none)
_ShowLines := PAHelperFramework.overrideOrDefault("ShowLines", _ShowLines, _IndicatorDescription)
var _ShowDirection = input.string("Simple", title = "", options = ["Simple", "+ Type (S/R)"], group = "Support & resistance levels", tooltip = "Enable/disable levels detection (support/resistance) & markup for the specified timeframes, and optionally, display whether they're originally a support (S) or a resistance (R)", inline = "ShowMarkup", display = display.none)
_ShowDirection := PAHelperFramework.overrideOrDefault("ShowDirection", _ShowDirection, _IndicatorDescription)

var _EnableTf1 = input.bool(true, title = "TF1", group = "Support & resistance levels", inline = "tf15", display = display.none)
_EnableTf1 := PAHelperFramework.overrideOrDefault("EnableTf1", _EnableTf1, _IndicatorDescription)
var _Tf1 = input.timeframe("M", title = "", inline = "tf15", group = "Support & resistance levels", display = display.none)
_Tf1 := PAHelperFramework.overrideOrDefault("Tf1", _Tf1, _IndicatorDescription)

var _EnableTf2 = input.bool(true, title = "TF2", group = "Support & resistance levels", inline = "tf26", display = display.none)
_EnableTf2 := PAHelperFramework.overrideOrDefault("EnableTf2", _EnableTf2, _IndicatorDescription)
var _Tf2 = input.timeframe("W", title = "", inline = "tf26", group = "Support & resistance levels", display = display.none)
_Tf2 := PAHelperFramework.overrideOrDefault("Tf2", _Tf2, _IndicatorDescription)

var _EnableTf3 = input.bool(true, title = "TF3", group = "Support & resistance levels", inline = "tf37", display = display.none)
_EnableTf3 := PAHelperFramework.overrideOrDefault("EnableTf3", _EnableTf3, _IndicatorDescription)
var _Tf3 = input.timeframe("D", title = "", inline = "tf37", group = "Support & resistance levels", display = display.none) 
_Tf3 := PAHelperFramework.overrideOrDefault("Tf3", _Tf3, _IndicatorDescription)

var _EnableTf4 = input.bool(true, title = "TF4", group = "Support & resistance levels", inline = "tf48", display = display.none)
_EnableTf4 := PAHelperFramework.overrideOrDefault("EnableTf4", _EnableTf4, _IndicatorDescription)
var _Tf4 = input.timeframe("240", title = "", inline = "tf48", group = "Support & resistance levels", display = display.none)
_Tf4 := PAHelperFramework.overrideOrDefault("Tf4", _Tf4, _IndicatorDescription)

var _EnableTf5 = input.bool(true, title = "TF5", group = "Support & resistance levels", inline = "tf15", display = display.none)
_EnableTf5 := PAHelperFramework.overrideOrDefault("EnableTf5", _EnableTf5, _IndicatorDescription)
var _Tf5 = input.timeframe("60", title = "", inline = "tf15", group = "Support & resistance levels", display = display.none)
_Tf5 := PAHelperFramework.overrideOrDefault("Tf5", _Tf5, _IndicatorDescription)

var _EnableTf6 = input.bool(true, title = "TF6", group = "Support & resistance levels", inline = "tf26", display = display.none)
_EnableTf6 := PAHelperFramework.overrideOrDefault("EnableTf6", _EnableTf6, _IndicatorDescription)
var _Tf6 = input.timeframe("30", title = "", inline = "tf26", group = "Support & resistance levels", display = display.none)
_Tf6 := PAHelperFramework.overrideOrDefault("Tf6", _Tf6, _IndicatorDescription)

var _EnableTf7 = input.bool(true, title = "TF7", group = "Support & resistance levels", inline = "tf37", display = display.none)
_EnableTf7 := PAHelperFramework.overrideOrDefault("EnableTf7", _EnableTf7, _IndicatorDescription)
var _Tf7 = input.timeframe("15", title = "", inline = "tf37", group = "Support & resistance levels", display = display.none)
_Tf7 := PAHelperFramework.overrideOrDefault("Tf7", _Tf7, _IndicatorDescription)

var _EnableTf8 = input.bool(true, title = "TF8", group = "Support & resistance levels", inline = "tf48", display = display.none)
_EnableTf8 := PAHelperFramework.overrideOrDefault("EnableTf8", _EnableTf8, _IndicatorDescription)
var _Tf8 = input.timeframe("5", title = "", inline = "tf48", group = "Support & resistance levels", display = display.none)
_Tf8 := PAHelperFramework.overrideOrDefault("Tf8", _Tf8, _IndicatorDescription)

if(str.endswith(_IndicatorDescription, " "))
    if(syminfo.ticker == "DE30EUR")
        _Tf1 := "W"
        _Tf2 := "D"
        _Tf3 := "240"
        _Tf4 := "60"
        _Tf5 := "30"
        _Tf6 := "15"
        _Tf7 := "5"
        _Tf8 := "2"

newTimeframeInfo(timeframe, enabled) =>
    PAHelperFramework.TimeframeInfo.new(Timeframe = timeframe, Enabled = enabled,  IsClosing = false, EnableLevelClosureNotifications = false, Levels = array.new<PAHelperFramework.LevelInfo>(), Lines = array.new<line>(), Labels = array.new<label>(), Open = array.new_float(), High = array.new_float(), Low = array.new_float(), Close = array.new_float(), RawOpen = array.new_float(), RawHigh = array.new_float(), RawLow = array.new_float(), RawClose = array.new_float(), TimeOpen = array.new_int(), TimeClose = array.new_int(), Duration = timeframe.in_seconds(timeframe) * 1000)

var _Timeframes = map.new<string, PAHelperFramework.TimeframeInfo>()
if(_Timeframes.size() == 0)
    _Timeframes.put(_Tf1, newTimeframeInfo(_Tf1, _EnableTf1))
    _Timeframes.put(_Tf2, newTimeframeInfo(_Tf2, _EnableTf2))
    _Timeframes.put(_Tf3, newTimeframeInfo(_Tf3, _EnableTf3))
    _Timeframes.put(_Tf4, newTimeframeInfo(_Tf4, _EnableTf4))
    _Timeframes.put(_Tf5, newTimeframeInfo(_Tf5, _EnableTf5))
    _Timeframes.put(_Tf6, newTimeframeInfo(_Tf6, _EnableTf6))
    _Timeframes.put(_Tf7, newTimeframeInfo(_Tf7, _EnableTf7))
    _Timeframes.put(_Tf8, newTimeframeInfo(_Tf8, _EnableTf8))

var _Timeframe1 = _Timeframes.get(_Tf1)
var _Timeframe2 = _Timeframes.get(_Tf2)
var _Timeframe3 = _Timeframes.get(_Tf3)
var _Timeframe4 = _Timeframes.get(_Tf4)
var _Timeframe5 = _Timeframes.get(_Tf5)
var _Timeframe6 = _Timeframes.get(_Tf6)
var _Timeframe7 = _Timeframes.get(_Tf7)
var _Timeframe8 = _Timeframes.get(_Tf8)

var _TimeZone = "UTC+0"

var _ShowLevelPrices = input.bool(false, title = "Show level prices", tooltip = "Show the exact price next to each level", group = "Markup Elements", display = display.none)
_ShowLevelPrices := PAHelperFramework.overrideOrDefault("ShowLevelPrices", _ShowLevelPrices, _IndicatorDescription)
var _ShowPsychologicalLevels = input.bool(false, title = "Psychological levels interval", inline = "PsychoLevels", group = "Markup Elements", tooltip = "Mark up key round numbers situated at specific price intervals.\n\nPredefined in templates:\nLevels (value: Auto)\nAll levels (value: Auto)", display = display.none)
_ShowPsychologicalLevels := PAHelperFramework.overrideOrDefault("ShowPsychologicalLevels", _ShowPsychologicalLevels, _IndicatorDescription)
var _RawPsychologicalLevelsInterval = input.string("Auto", options = ["Auto", "0.001", "0.005", "0.01", "0.05", "0.1", "0.5", "1", "5", "10", "50", "100", "500"], title = "", inline = "PsychoLevels", group = "Markup Elements", display = display.none)
_RawPsychologicalLevelsInterval := PAHelperFramework.overrideOrDefault("RawPsychologicalLevelsInterval", _RawPsychologicalLevelsInterval, _IndicatorDescription)

var _HardcodedPsychoLevelIntervals = array.from(
      PAHelperFramework.HardcodedTickerValue.new("DOGE", 0.005)
     ,PAHelperFramework.HardcodedTickerValue.new("XRP", 0.01)
     ,PAHelperFramework.HardcodedTickerValue.new("CL1,OIL,USOIL,LTC", 1)
     ,PAHelperFramework.HardcodedTickerValue.new("SOL,XAU,GOLD,GC1,MGC1", 5)
     ,PAHelperFramework.HardcodedTickerValue.new("SPX,SP500,ES1,MES1,US500", 50)
     ,PAHelperFramework.HardcodedTickerValue.new("ETH", 100)
     ,PAHelperFramework.HardcodedTickerValue.new("NAS,US100,NQ,MNQ1,GC1,MGC1,US30,DJI,YM1,MYM1", 500)
     ,PAHelperFramework.HardcodedTickerValue.new("BTC,MBT1", 5000)
     )

var _PsychologicalLevelsColor = input.color(color.rgb(255, 249, 196, 70), title = "", inline = "PsychoLevels", group = "Markup Elements", display = display.none)
_PsychologicalLevelsColor := PAHelperFramework.overrideOrDefault("PsychologicalLevelsColor", _PsychologicalLevelsColor, _IndicatorDescription)

var _EnableLevelsHeatmap = input.bool(false, title = "Levels heatmap", inline = "Heatmap", group = "Markup Elements", tooltip = "Display the density of levels throughout the chart by showing a shadow over each level. The more shadows intersect over a certain area, the stronger that area is.\n\nAdditionally, you can choose the color of the heatmap shadows based on their level timeframes (TF 1-2, 3-4, 5-6, 7-8)\n\nPredefined in templates:\nHeatmap", display = display.none)
_EnableLevelsHeatmap := PAHelperFramework.overrideOrDefault("EnableLevelsHeatmap", _EnableLevelsHeatmap, _IndicatorDescription)
var _HeatmapColor1 = input.color(color.rgb(255, 245, 243, 95), title = "", inline = "Heatmap", group = "Markup Elements", display = display.none)
_HeatmapColor1 := PAHelperFramework.overrideOrDefault("HeatmapColor1", _HeatmapColor1, _IndicatorDescription)
var _HeatmapColor2 = input.color(color.rgb(248, 233, 230, 95), title = "", inline = "Heatmap", group = "Markup Elements", display = display.none)
_HeatmapColor2 := PAHelperFramework.overrideOrDefault("HeatmapColor2", _HeatmapColor2, _IndicatorDescription)
var _HeatmapColor3 = input.color(color.rgb(255, 227, 221, 95), title = "", inline = "Heatmap", group = "Markup Elements", display = display.none)
_HeatmapColor3 := PAHelperFramework.overrideOrDefault("HeatmapColor3", _HeatmapColor3, _IndicatorDescription)
var _HeatmapColor4 = input.color(color.rgb(236, 225, 225, 95), title = "", inline = "Heatmap", group = "Markup Elements", display = display.none)
_HeatmapColor4 := PAHelperFramework.overrideOrDefault("HeatmapColor4", _HeatmapColor4, _IndicatorDescription)

var _PersistHeatmap = input.string(defval = "Simple", title = "", options = ["Simple", "Persisted"], inline = "Heatmap", group = "Markup Elements", display = display.none)
_PersistHeatmap := PAHelperFramework.overrideOrDefault("PersistHeatmap", _PersistHeatmap, _IndicatorDescription)

var _DecongestionSize = input.string("Auto", title = "Decongestion", options = ["Auto",  "Off", "Wide spaced", "Dense", "All distinct levels"], tooltip = "Hide levels which are too close to each other and only show the higher timeframe ones in the respective area, in order to keep the chart clean. Optionally you can see all the timeframes that share a support/resistance level on the same price area, but concatenated and grouped into the same line\n\nPredefined in templates:\nLevels\nAll levels\n- option 'Group all timeframes that share the same level'", group = "Markup Options", inline = "Decongestion", display = display.none)
_DecongestionSize := PAHelperFramework.overrideOrDefault("DecongestionSize", _DecongestionSize, _IndicatorDescription)
var _GroupLevelTimeframes = input.string("Show the highest timeframe", options = ["Show the highest timeframe", "Group 2 timeframes", "Group 3 timeframes", "Group all timeframes that share the same level"], title = "", inline = "Decongestion", group = "Markup Options", display = display.none)
_GroupLevelTimeframes := PAHelperFramework.overrideOrDefault("GroupLevelTimeframes", _GroupLevelTimeframes, _IndicatorDescription)

var _LevelInvalidationCrossesCount = input.int(2, minval = 1, maxval = 9, title = "Remove levels after X passthroughs", tooltip = "A support/resistance is removed after X closed candles passed through it (a passthrough is a candle body crossing the level on its respective timeframe, and not just wicking it, although wicking it counts as ½ of a passthrough). Display note: when a level is 1 passthrough away from being invalidated, it will be displayed as a dotted line instead of a solid line\n\nPredefined in templates:\nNTAs (value: 3)\nLTAs (value: 3)", group = "Markup Options", display = display.none)
_LevelInvalidationCrossesCount := PAHelperFramework.overrideOrDefault("LevelInvalidationCrossesCount", _LevelInvalidationCrossesCount, _IndicatorDescription)

var _EnableLTAs = input.bool(false, title ="Show LTAs", group = "LTA (Low Traffic Areas)", tooltip = "LTA (low traffic areas) are the areas between support/resistance levels, or the areas that don't have any level in between, nothing obstructing the price move\nA text can be displayed on the LTAs, identifying their top/bottom levels, always or selectively when the levels are not visible due to decongestion.\n\nPredefined in templates:\nNTAs\nLTAs", inline = "EnableLTA", display = display.none)
_EnableLTAs := PAHelperFramework.overrideOrDefault("EnableLTAs", _EnableLTAs, _IndicatorDescription)
var _LtaFillColor = input.color(color.rgb(102, 65, 151, 89), title = "", inline = "EnableLTA", group = "LTA (Low Traffic Areas)", display = display.none)
_LtaFillColor := PAHelperFramework.overrideOrDefault("LtaFillColor", _LtaFillColor, _IndicatorDescription)
var _LtaType = input.string("S/R", title ="", options = ["S/R", "Candles"], inline = "EnableLTA", group = "LTA (Low Traffic Areas)", display = display.none)
_LtaType := PAHelperFramework.overrideOrDefault("LtaType", _LtaType, _IndicatorDescription)
var _LtaCaptions = input.string("With info", title ="", options = ["No info", "With info"], inline = "EnableLTA", group = "LTA (Low Traffic Areas)", display = display.none)
_LtaCaptions := PAHelperFramework.overrideOrDefault("LtaCaptions", _LtaCaptions, _IndicatorDescription)

var _EnableLtaLaxFilter = input.bool(false, title ="LTA passthroughs", group = "LTA (Low Traffic Areas)", inline = "lta-lax", tooltip = "Filter the LTAs by how many passthroughs their top/bottom levels have on their respective timeframe (e.g. specify 0 to only mark only the LTAs whose start/end levels were never crossed, 1 for LTA start/end levels that were crossed once, and so on)\n\nPredefined in templates:\nNTAs (value: min 0, max 0 - showing only LTAs whose levels were never passed through)", display = display.none)
_EnableLtaLaxFilter := PAHelperFramework.overrideOrDefault("EnableLtaLaxFilter", _EnableLtaLaxFilter, _IndicatorDescription)
var _LtaLaxMin = input.int(0,  minval = 0, title = "Min", inline = "lta-lax", group = "LTA (Low Traffic Areas)", display = display.none)
_LtaLaxMin := PAHelperFramework.overrideOrDefault("LtaLaxMin", _LtaLaxMin, _IndicatorDescription)
var _LtaLaxMax = input.int(2,  minval = 0, title = "Max", inline = "lta-lax", group = "LTA (Low Traffic Areas)", display = display.none)
_LtaLaxMax := PAHelperFramework.overrideOrDefault("LtaLaxMax", _LtaLaxMax, _IndicatorDescription)

var _EnableLtaCandlesFilter = input.bool(false, title ="LTA candles", group = "LTA (Low Traffic Areas)", inline = "lta-candles", tooltip = "Filter the LTAs by number of candles: how many candles in the same direction should the LTA engulf\n\nPredefined in templates:\nLTAs (value: filter is on)", display = display.none)
_EnableLtaCandlesFilter := PAHelperFramework.overrideOrDefault("EnableLtaCandlesFilter", _EnableLtaCandlesFilter, _IndicatorDescription)
var _MinLtaCandles = input.int(2,  minval = 1, title = "Min", inline = "lta-candles", group = "LTA (Low Traffic Areas)", display = display.none)
_MinLtaCandles := PAHelperFramework.overrideOrDefault("MinLtaCandles", _MinLtaCandles, _IndicatorDescription)
var _MaxLtaCandles = input.int(10,  minval = 1, title = "Max",  inline = "lta-candles", group = "LTA (Low Traffic Areas)", display = display.none)
_MaxLtaCandles := PAHelperFramework.overrideOrDefault("MaxLtaCandles", _MaxLtaCandles, _IndicatorDescription)

var _EnableLtaSizeFilter = input.bool(false, title ="LTA size (pts)", group = "LTA (Low Traffic Areas)", inline = "lta-size", tooltip = "Filter the LTAs by their sizes (pts). By default the minimum LTA size is the ATR", display = display.none)
_EnableLtaSizeFilter := PAHelperFramework.overrideOrDefault("EnableLtaSizeFilter", _EnableLtaSizeFilter, _IndicatorDescription)
var _MinLtaSize = input.int(100,  minval = 1, title = "Min", inline = "lta-size", group = "LTA (Low Traffic Areas)", display = display.none)
_MinLtaSize := PAHelperFramework.overrideOrDefault("MinLtaSize", _MinLtaSize, _IndicatorDescription)
var _MaxLtaSize = input.int(500,  minval = 1, title = "Max",  inline = "lta-size", group = "LTA (Low Traffic Areas)", display = display.none)
_MaxLtaSize := PAHelperFramework.overrideOrDefault("MaxLtaSize", _MaxLtaSize, _IndicatorDescription)

var _RemoveOldLTAs = input.bool(false, title = "Hide old LTAs", tooltip = "Hide mitigated LTAs. An LTA is mitigated when a new resistance/support level forms within its zone, and thus, invalidating it as an LTA", group = "LTA (Low Traffic Areas)", display = display.none)
_RemoveOldLTAs := PAHelperFramework.overrideOrDefault("RemoveOldLTAs", _RemoveOldLTAs, _IndicatorDescription)
var _LTAAlert = input.bool(false, title = "Alert on LTAs", group = "LTA (Low Traffic Areas)", tooltip = "Raise an alert at the moment a new LTA is being discovered on the chart, or when an existing LTA is mitigated (i.e., when a new resistance/support level forms within its zone, and thus, invalidating it as an LTA)", inline = "LTAAlert", display = display.none)
_LTAAlert := PAHelperFramework.overrideOrDefault("LTAAlert", _LTAAlert, _IndicatorDescription)
var _LTAAlertType = input.string("Created & mitigated", title = "", options = ["Created", "Mitigated", "Created & mitigated"], group = "LTA (Low Traffic Areas)", inline = "LTAAlert", display = display.none)
_LTAAlertType := PAHelperFramework.overrideOrDefault("LTAAlertType", _LTAAlertType, _IndicatorDescription)
var _ShowNotificationFlags = false

var _InitTime = timenow
var _FirstBarTime = _time
int _barsDiff = last_bar_index - bar_index

isRecent(int barsDiffLimit) =>
    _barsDiff < barsDiffLimit

_isRecentBar = str.contains(_IndicatorDescription, "All") ? true : isRecent(500)

var _Styling = PAHelperFramework.Styling.new(
     AlertTextColor = _Theme == "Dark theme" ? color.white : color.black,
     HeatmapColor1 = _Theme == "Dark theme" ? _HeatmapColor1 : color.rgb(255 - color.r(_HeatmapColor1), 255 - color.g(_HeatmapColor1), 255 - color.b(_HeatmapColor1), color.t(_HeatmapColor1)) ,
     HeatmapColor2 = _Theme == "Dark theme" ? _HeatmapColor2 : color.rgb(255 - color.r(_HeatmapColor2), 255 - color.g(_HeatmapColor2), 255 - color.b(_HeatmapColor2), color.t(_HeatmapColor2)),
     HeatmapColor3 = _Theme == "Dark theme" ? _HeatmapColor3 : color.rgb(255 - color.r(_HeatmapColor3), 255 - color.g(_HeatmapColor3), 255 - color.b(_HeatmapColor3), color.t(_HeatmapColor3)),
     HeatmapColor4 = _Theme == "Dark theme" ? _HeatmapColor4 : color.rgb(255 - color.r(_HeatmapColor4), 255 - color.g(_HeatmapColor4), 255 - color.b(_HeatmapColor4), color.t(_HeatmapColor4)),
     CurrentCandleBullish = _Theme == "Dark theme" ? color.rgb(255, 224, 178, 80) : color.rgb(0, 31, 77, 80),
     CurrentCandleBearish = _Theme == "Dark theme" ? color.rgb(255, 224, 178, 80) : color.rgb(0, 31, 77, 80)
 )
        
if(_Template == "Opening hours" or _Template == "Bias" or _Template == "Smooth candles" or _Template == "Snapped candles" or _Template == "LTAs" or str.startswith(_IndicatorDescription, "Notifications"))
    _ShowLines := false
    _DecongestionSize := "Off"

if(_Template == "Opening hours" or _Template == "Smooth candles" or _Template == "LTAs" or _Template == "Snapped candles" or str.startswith(_IndicatorDescription, "Notifications"))
    _Timeframe1.Enabled := false
    _Timeframe2.Enabled := false
    _Timeframe3.Enabled := false
    _Timeframe4.Enabled := false
    _Timeframe5.Enabled := false
    _Timeframe6.Enabled := false
    _Timeframe7.Enabled := false
    _Timeframe8.Enabled := false

if(_Template == "All levels" or _Template == "Levels")
    _ShowPsychologicalLevels := true
    _GroupLevelTimeframes := "Group all timeframes that share the same level"
    _DecongestionSize := _DecongestionSize == "Auto" ? "All distinct levels" : _DecongestionSize

if(_Template == "All levels" and str.startswith(_IndicatorDescription, "+ prices"))
    _ShowLevelPrices := true

if(_Template == "Heatmap")
    _ShowLines := false
    _EnableLevelsHeatmap := true

if(_Template == "NTAs")
    _EnableLTAs := true
    _ShowLines := false
    _DecongestionSize := "Off"
    _LevelInvalidationCrossesCount := 3
    if(not _EnableLtaLaxFilter)
        _EnableLtaLaxFilter := true
        _LtaLaxMin := 0
        _LtaLaxMax := 0

if(_Template == "LTAs")
    _EnableLTAs := true
    for tfKey in _Timeframes.keys()
        if(tfKey == timeframe.period)
            tf = _Timeframes.get(tfKey)
            tf.Enabled := true
            break

    _EnableLtaCandlesFilter := true
    _LtaCaptions := "No info"

    _LtaFillColor := _LtaFillColor == color.rgb(102, 65, 151, 89) ? 
                     color.rgb(75, 151, 65, 89) :
                      _LtaFillColor

if(_Template == "Snapped candles" or str.startswith(_IndicatorDescription, "Notifications"))
    _ShowLines := false
    _DecongestionSize := "Off"
    
if(_Template == "Snapped candles" and str.endswith(_IndicatorDescription, " "))
    _EnableLTAs := true
    _LtaType := "Candles"
    _LtaFillColor := _LtaFillColor == color.rgb(102, 65, 151, 89) ? 
                     color.rgb(179, 157, 219, 89) :
                      _LtaFillColor

float _levelLimitsPts = _atrSize * 7

float _levelDecongestionPts = 
  _DecongestionSize == "Wide spaced" ? _atrSize :
  _DecongestionSize == "Auto" ? _atrSize / 2 :
  _DecongestionSize == "Dense" ? _atrSize / 4 :
  _DecongestionSize == "All distinct levels" ? math.max(_atrSize / 12, _MinTick * 3) :
  0

var _PerformSorting = _DecongestionSize != "Off" or (_EnableLTAs and _LtaType == "S/R") or _GroupLevelTimeframes != "Show the highest timeframe" or _EnableLevelsHeatmap

var int _CurrentTFDuration = timeframe.in_seconds() * 1000
var PAHelperFramework.TimeframeInfo _SmallestTimeframe = _Timeframe1

if(_firstRun)
    for timeFrame in _Timeframes.values()
        timeFrame.TimeOpen.push(time(timeFrame.Timeframe))
        timeFrame.TimeClose.push(time_close(timeFrame.Timeframe))
        timeFrame.Enabled := timeFrame.Enabled and _CurrentTFDuration <= timeFrame.Duration
        if(_Template == "Levels")
            timeFrame.Enabled := timeFrame.Enabled and PAHelperFramework.getTimeframeRatioFactor(timeFrame) >= 13
        timeFrame.Timeframe := timeFrame.Enabled ? timeFrame.Timeframe : na
        if(timeFrame.Enabled and timeFrame.Duration >= _CurrentTFDuration and _SmallestTimeframe.Duration > timeFrame.Duration)
            _SmallestTimeframe := timeFrame

    _Timeframe1.LineColor := color.rgb(156, 39, 176)
    _Timeframe2.LineColor := color.rgb(136, 14, 79)
    _Timeframe3.LineColor := color.rgb(103, 58, 183)
    _Timeframe4.LineColor := color.rgb(41, 98, 255)
    _Timeframe5.LineColor := color.rgb(0, 188, 212)
    _Timeframe6.LineColor := color.rgb(8, 153, 129)
    _Timeframe7.LineColor := color.rgb(255, 152, 0)
    _Timeframe8.LineColor := color.rgb(149, 117, 205)
    
    _Timeframe1.LabelTextColor := color.white
    _Timeframe2.LabelTextColor := color.white
    _Timeframe3.LabelTextColor := color.white
    _Timeframe4.LabelTextColor := color.white
    _Timeframe5.LabelTextColor := _Theme == "Dark theme" ? color.white : color.rgb(69, 73, 87)
    _Timeframe6.LabelTextColor := _Theme == "Dark theme" ? color.white : color.rgb(69, 73, 87)
    _Timeframe7.LabelTextColor := _Theme == "Dark theme" ? color.white : color.rgb(69, 73, 87)
    _Timeframe8.LabelTextColor := _Theme == "Dark theme" ? color.white : color.rgb(69, 73, 87)

    _Timeframe1.LineWidth := 6
    _Timeframe2.LineWidth := 5
    _Timeframe3.LineWidth := 4
    _Timeframe4.LineWidth := 3
    _Timeframe5.LineWidth := 2
    _Timeframe6.LineWidth := 1
    _Timeframe7.LineWidth := 1
    _Timeframe8.LineWidth := 1

    _Timeframe1.LabelStyle := label.style_text_outline
    _Timeframe2.LabelStyle := label.style_text_outline
    _Timeframe3.LabelStyle := label.style_text_outline
    _Timeframe4.LabelStyle := label.style_text_outline
    _Timeframe5.LabelStyle := label.style_none
    _Timeframe6.LabelStyle := label.style_none
    _Timeframe7.LabelStyle := label.style_none
    _Timeframe8.LabelStyle := label.style_none

    _Timeframe1.LabelColor := color.blue
    _Timeframe2.LabelColor := color.blue
    _Timeframe3.LabelColor := color.blue
    _Timeframe4.LabelColor := color.blue
    _Timeframe5.LabelColor := na
    _Timeframe6.LabelColor := na
    _Timeframe7.LabelColor := na
    _Timeframe8.LabelColor := na

    _Timeframe1.HeatmapColor := _Styling.HeatmapColor1
    _Timeframe2.HeatmapColor := _Styling.HeatmapColor1
    _Timeframe3.HeatmapColor := _Styling.HeatmapColor2
    _Timeframe4.HeatmapColor := _Styling.HeatmapColor2
    _Timeframe5.HeatmapColor := _Styling.HeatmapColor3
    _Timeframe6.HeatmapColor := _Styling.HeatmapColor3
    _Timeframe7.HeatmapColor := _Styling.HeatmapColor4
    _Timeframe8.HeatmapColor := _Styling.HeatmapColor4

var _DetectLevels = _Timeframe1.Enabled or _Timeframe2.Enabled or _Timeframe3.Enabled or _Timeframe4.Enabled or _Timeframe5.Enabled or _Timeframe6.Enabled or _Timeframe7.Enabled or _Timeframe8.Enabled

/////////////SUP/RES

for timeFrame in _Timeframes.values()
    timeFrame.IsClosing := false

isChartTfLowerThan(PAHelperFramework.TimeframeInfo timeframeInfo) =>
    _CurrentTFDuration <= timeframeInfo.Duration

if(_DetectLevels)
    barDiffLimit = 
         _CurrentTFDuration <= 1000 * 60 * 1 ? 40000 
       : _CurrentTFDuration <= 1000 * 60 * 5 ? 20000 
       : _CurrentTFDuration <= 1000 * 60 * 15 ? 15000 
       : _CurrentTFDuration <= 1000 * 60 * 30 ? 10000 
       : _CurrentTFDuration <= 1000 * 60 * 60 ? 8000
       : _CurrentTFDuration <= 1000 * 60 * 240 ? 5000
       : 1000

    for timeFrame in _Timeframes.values()
        PAHelperFramework.updateTimeframeDetails(timeFrame, (str.endswith(_IndicatorDescription, " ") or str.contains(_IndicatorDescription, "ALL")) ? true : isRecent(barDiffLimit),
       withBarConfirmed = true,
       roundingInterval = _MinTick)

var _LTAs = array.new<PAHelperFramework.LTAInfo>()
var _SortedLevels = array.new<PAHelperFramework.LevelInfo>()
var _LevelsInfo = PAHelperFramework.StateInfo.new()
var _ChartMin = low
var _ChartMax = high

_LevelsInfo.HasChanged := false
_levelBreakouts = array.new<PAHelperFramework.BreakoutInfo>()


addLevelShade(PAHelperFramework.LevelInfo level) =>
    levelTimeframeInfo = _Timeframes.get(level.Timeframe)
    float shadeSize = _atrSize * PAHelperFramework.getTimeframeRatioFactor(levelTimeframeInfo) / 100.0
    level.ShadeBox := box.new(level.Time, level.Price + shadeSize * _MinTick, _timeClose, level.Price - shadeSize * _MinTick, border_color = na, xloc = xloc.bar_time, bgcolor = _EnableLevelsHeatmap ? levelTimeframeInfo.HeatmapColor : na)

removeLevel(array<PAHelperFramework.LevelInfo> levels, int levelIndex) =>
    level = levels.get(levelIndex)

    if(_ShowLines)
        levelTimeframeInfo = _Timeframes.get(level.Timeframe)
        currentLevelLine = levelTimeframeInfo.Lines.get(levelIndex)
        currentLevelLabel = levelTimeframeInfo.Labels.get(levelIndex)

        if(_GroupLevelTimeframes != "Show the highest timeframe" and _isRecentBar)
            for tf in _Timeframes.values()
                for tfLabel in tf.Labels
                    tfLabelText = tfLabel.get_text()
                    currentLabelText = ", " + currentLevelLabel.get_text()
                    if(str.contains(tfLabelText, currentLabelText))
                        tfLabel.set_text(str.replace_all(tfLabelText, currentLabelText, ""))

        currentLevelLine.delete()
        currentLevelLabel.delete()

        levelTimeframeInfo.Lines.remove(levelIndex)
        levelTimeframeInfo.Labels.remove(levelIndex)
        for l in levels
            if(l.LineIndex > levelIndex)
                l.LineIndex := l.LineIndex - 1

    if(_EnableLevelsHeatmap)
        if(_PersistHeatmap == "Persisted")
            if(na(level.ShadeBox))
                addLevelShade(level)

            level.ShadeBox.set_left(level.Time - _CurrentTFDuration)
            level.ShadeBox.set_right(_time)
        else
            level.ShadeBox.delete()
            level.ShadeBox := na
        
    levels.remove(levelIndex)
    if(_PerformSorting)
        for i = _SortedLevels.size() - 1 to 0
            l = _SortedLevels.get(i)
            if(l.Timeframe == level.Timeframe and l.Price == level.Price)
                _SortedLevels.remove(i)
                _LevelsInfo.HasChanged := true
                break
    
if(isRecent(1000))
    _ChartMin := _candleLow < _ChartMin ? _candleLow : _ChartMin
    _ChartMax := _candleHigh > _ChartMax ? _candleHigh : _ChartMax

getLevelCaption(PAHelperFramework.LevelInfo level, forceNoPrice = false) =>
    _isRecentBar ? PAHelperFramework.getTimeframeTitle(level.Timeframe, _ShowDirection == "Simple" ? 0 : level.PivotDirection) + ((_ShowLevelPrices and not forceNoPrice) ? " (" + str.tostring(level.Price, format.mintick) + ")" : "") : ""

addLevelLine(PAHelperFramework.LevelInfo level, int lineIndex = -1) =>
    if(_ShowLines)
        levelTimeframeInfo = _Timeframes.get(level.Timeframe)
        extendTo = math.round(PAHelperFramework.getLast(levelTimeframeInfo.TimeClose) + levelTimeframeInfo.Duration * (_LevelInvalidationCrossesCount - level.CrossedThroughCount + (timeframe.period == level.Timeframe ? 1 : 0)))
        extendTo := math.max(math.min(extendTo, _timeClose + _CurrentTFDuration * 50), _timeClose + _CurrentTFDuration * 10)

        levelLine = line.new(lineIndex == -1 and str.contains(_IndicatorDescription, "RT") ? time : level.Time, level.Price, extendTo, level.Price, color = levelTimeframeInfo.LineColor, width = levelTimeframeInfo.LineWidth, style = line.style_solid, xloc = xloc.bar_time)
        levelLabel = label.new(extendTo, level.Price, getLevelCaption(level), xloc = xloc.bar_time, style = levelTimeframeInfo.LabelStyle, textcolor = levelTimeframeInfo.LabelTextColor, color = levelTimeframeInfo.LabelColor)

        if(lineIndex == -1 or lineIndex >= levelTimeframeInfo.Lines.size())
            level.LineIndex := levelTimeframeInfo.Lines.size()
            levelTimeframeInfo.Lines.push(levelLine)
            levelTimeframeInfo.Labels.push(levelLabel)
        else
            levelTimeframeInfo.Lines.set(lineIndex, levelLine)
            levelTimeframeInfo.Labels.set(lineIndex, levelLabel)
    
        if(_LevelInvalidationCrossesCount > 1)
            if(level.CrossedThroughCount >= 1)
                levelLine.set_style(line.style_dashed)

            if(level.CrossedThroughCount >= _LevelInvalidationCrossesCount - 1)
                levelLine.set_style(line.style_dotted)

        if(level.IsHidden)
            level.IsHidden := false
            PAHelperFramework.hideLevel(level, _Timeframes, _ShowLines, _isRecentBar)
        
        [levelLine, levelLabel]

array<string> _displayedLevelLabels = array.new_string()
var map<string, int> _DisplayedLevelsStatus = map.new<string, int>()

extendLevel(PAHelperFramework.LevelInfo level, line levelLn) =>
    extended = false

    if(_ShowLines and not (na(level)))
        if(not level.IsHidden)
            levelTimeframeInfo = _Timeframes.get(level.Timeframe)
            line levelLine = levelLn
            label levelLabel = na
            if(na(levelLine))
                [ln, lb] = addLevelLine(level, level.LineIndex)
                levelLine := ln
                levelLabel := lb
            
            if(_isRecentBar)
                extendTo = math.round(PAHelperFramework.getLast(levelTimeframeInfo.TimeClose) + levelTimeframeInfo.Duration * (_LevelInvalidationCrossesCount - level.CrossedThroughCount + (timeframe.period == level.Timeframe ? 1 : 0)))
                extendTo := math.max(math.min(extendTo, _timeClose + _CurrentTFDuration * 50), _timeClose + _CurrentTFDuration * 10)

                if(extendTo >= _timeClose and levelLine.get_x2() != extendTo)
                    _LevelsInfo.HasChanged := true
                    levelLine.set_x2(extendTo)
                
                    levelLabel := na(levelLabel) ? levelTimeframeInfo.Labels.get(level.LineIndex) : levelLabel
                    levelLabel.set_x(extendTo)
                    
                    extended := true

                displayedLevelLabelInfo = (level.CrossedThroughCount == _LevelInvalidationCrossesCount - 1 ? "half" : "full") + levelLabel.get_text()
                _DisplayedLevelsStatus.put(level.Timeframe, nz(_DisplayedLevelsStatus.get(level.Timeframe)) + 1)

                if(not _displayedLevelLabels.includes(displayedLevelLabelInfo))
                    _displayedLevelLabels.push(displayedLevelLabelInfo)
                else
                    if(_DisplayedLevelsStatus.get(level.Timeframe) % 3 == 0)
                        levelLabel.set_x(0)

    extended

extendLevels(array<PAHelperFramework.LevelInfo> levels) =>
    if(not na(levels) and barstate.isconfirmed)
        for level in levels
            levelTimeframeInfo = _Timeframes.get(level.Timeframe)
            levelLine = levelTimeframeInfo.Lines.size() > level.LineIndex ? levelTimeframeInfo.Lines.get(level.LineIndex) : na

            if(PAHelperFramework.isLevelInLimits(level.Price, _levelLimitsPts))
                extendLevel(level, levelLine)
            else
                levelLine.delete()
                levelTimeframeInfo.Labels.get(level.LineIndex).delete()
                false

type GroupedLevels
    array<string> LevelCaptions

prioritizeLevels(array<PAHelperFramework.LevelInfo> levels) =>
    array<PAHelperFramework.LevelInfo> orderedLevels = array.new<PAHelperFramework.LevelInfo>()

    for level in levels
        levelTimeframeInfo = _Timeframes.get(level.Timeframe)
        added = false
        for [i, orderedLevel] in orderedLevels
            if(levelTimeframeInfo.Duration >= _Timeframes.get(orderedLevel.Timeframe).Duration)
                orderedLevels.insert(i, level)
                added := true
                break
        
        if(not added)
            orderedLevels.push(level)

    prioritaryLevel = orderedLevels.first()
    prioritaryLevelTimeframeInfo = _Timeframes.get(prioritaryLevel.Timeframe)
    prioritaryLevel.IsHidden := false
    
    string groupedLevelCaption = ""
    concatenations = _GroupLevelTimeframes == "Group 2 timeframes" ? 2 : _GroupLevelTimeframes == "Group 3 timeframes" ? 3 : 10

    groupedLevelsByPrice = map.new<float, GroupedLevels>()
    for [i, level] in orderedLevels
        if(i > 0)
            PAHelperFramework.hideLevel(level, _Timeframes, _ShowLines, _isRecentBar)

        if(_GroupLevelTimeframes != "Show the highest timeframe" and concatenations > 0)
            levelTimeframeInfo = _Timeframes.get(level.Timeframe)
            string levelCaption = getLevelCaption(level, forceNoPrice = true)

            if(_ShowLevelPrices)
                existingGroup = groupedLevelsByPrice.get(level.Price)
                if(na(existingGroup))
                    existingGroup := GroupedLevels.new(array.new_string())
                    groupedLevelsByPrice.put(level.Price, existingGroup)
                    concatenations := concatenations - 1
                
                if(not existingGroup.LevelCaptions.includes(levelCaption))
                    existingGroup.LevelCaptions.push(levelCaption)
                    if(existingGroup.LevelCaptions.size() > 1)
                        concatenations := concatenations - 1
            else
                if(groupedLevelCaption != levelCaption and not str.contains(groupedLevelCaption, ", " + levelCaption))
                    concatenations := concatenations - 1
                    groupedLevelCaption := groupedLevelCaption + ", " + levelCaption

    for groupedLevelPrice in groupedLevelsByPrice.keys()
        groupedLevelCaption += ", " + groupedLevelsByPrice.get(groupedLevelPrice).LevelCaptions.join("/") + " (" + str.tostring(groupedLevelPrice, format.mintick) + ")"

    if(_GroupLevelTimeframes != "Show the highest timeframe")
        label prioritaryLevelLabel = prioritaryLevelTimeframeInfo.Labels.get(prioritaryLevel.LineIndex)
        if(na(prioritaryLevelLabel))
            [ln, lb] = addLevelLine(prioritaryLevel, prioritaryLevel.LineIndex)
            prioritaryLevelLabel := lb
        
        prioritaryLevelLabel.set_text(str.substring(groupedLevelCaption, 2))

detectLevelBreakout(PAHelperFramework.LevelInfo level, int sortedLevelIndex) =>
    closedAbove = _candleOpen < level.Price and level.Price < _candleClose
    closedBelow = _candleClose < level.Price and level.Price < _candleOpen

    if(closedAbove or closedBelow)
        levelBreakout = PAHelperFramework.BreakoutInfo.new(level, closedAbove ? 1 : -1, sortedLevelIndex)
        _levelBreakouts.push(levelBreakout)

detectLTAs(PAHelperFramework.LevelInfo currentLevel, PAHelperFramework.LevelInfo previousLevel, array<string> invalidatedLtaLevels) =>
    level = currentLevel
    prevLevel = previousLevel

    if(_LevelsInfo.HasChanged and level.Price != prevLevel.Price and (_EnableLTAs and _LtaType == "S/R") and  _SortedLevels.size() > 3)
        string toLevel = PAHelperFramework.getTimeframeTitle(prevLevel.Timeframe, _ShowDirection == "Simple" ? 0 : prevLevel.PivotDirection)
        
        lax = _EnableLtaLaxFilter ? level.CrossedThroughCount + prevLevel.CrossedThroughCount : 0
        if((_LtaLaxMin <= lax and lax <= _LtaLaxMax) or not _EnableLtaLaxFilter)
            distance = (level.Price - prevLevel.Price) / _MinTick
            minLtaSize = (not _EnableLtaSizeFilter or _MinLtaSize == 0) ? _atrSize : _MinLtaSize
            maxLtaSize = (not _EnableLtaSizeFilter or _MaxLtaSize < _MinLtaCandles or _MaxLtaSize == 0) ? _atrSize * 20 : _MaxLtaSize

            if(minLtaSize <= distance and distance <= maxLtaSize)
                x2 = math.max(_timeClose + _CurrentTFDuration * 10, math.min(time_close(level.Timeframe), time_close(prevLevel.Timeframe)))
                engulfedCandles = 1
                if(_EnableLtaCandlesFilter and _MinLtaCandles > 0)
                    ltaFromBar = math.min(level.BarIndex, prevLevel.BarIndex)
                    ltaToBar = math.max(level.BarIndex, prevLevel.BarIndex)
                    if(_MinLtaCandles <= ltaToBar - ltaFromBar and bar_index - ltaFromBar <= 500)
                        float prevLtaBarSize = na
                        for c = bar_index - ltaFromBar to bar_index - ltaToBar + 1
                            ltaBarSize = _candleClose[c] - _candleClose[c + 1]
                            if(not na(prevLtaBarSize))
                                if(math.sign(ltaBarSize) == math.sign(prevLtaBarSize) and math.max(_candleClose[c], _candleClose[c + 1]) <= math.max(level.Price, prevLevel.Price) and math.min(_candleClose[c], _candleClose[c + 1]) >= math.min(level.Price, prevLevel.Price) and _candleClose[c] != _candleClose[c + 1])
                                    engulfedCandles := engulfedCandles + 1
                                else
                                    if(_MinLtaCandles <= engulfedCandles and engulfedCandles <= _MaxLtaCandles)
                                        break
                                    else
                                        engulfedCandles := 0
                            prevLtaBarSize := ltaBarSize

                if((_MinLtaCandles <= engulfedCandles and engulfedCandles <= _MaxLtaCandles) or not _EnableLtaCandlesFilter)
                    [ltaExists, lta] = PAHelperFramework.addLTA(_time, math.max(level.Price, prevLevel.Price), x2, math.min(level.Price, prevLevel.Price), _LtaFillColor, _LTAs)
                    if(not ltaExists)
                        fromLevel = PAHelperFramework.getTimeframeTitle(level.Timeframe, _ShowDirection == "Simple" ? 0 : level.PivotDirection)

                        if(_candleClose <= level.Price)
                            tmp1 = level
                            level := prevLevel
                            prevLevel := tmp1

                            tmp2 = fromLevel
                            fromLevel := toLevel
                            toLevel := tmp2

                        ltaSize = math.abs(level.Price - prevLevel.Price) / _MinTick

                        if(_LtaCaptions != "No info")
                            caption = "LTA"
                            if(fromLevel != "")
                                caption += " from " + fromLevel
                            if(toLevel != "")
                                caption += " to " + toLevel

                            lta.Box.set_text(caption + " (" + str.tostring(ltaSize, "#") + "pts)")

                        if(_LTAAlert and _LTAAlertType != "Mitigated" and (__isInSession or not __InSessionNotificationFilter))
                            ltaAlertMessage = "LTA #" + str.tostring(_LTAs.size()) + " created"
                               +  " from "
                               + PAHelperFramework.getTimeframeTitle(level.Timeframe)
                               + " " + (level.PivotDirection == 1 ? "resistance" : "support")
                               + " @" + str.tostring(level.Price, format.mintick)

                               +  " to "
                               + PAHelperFramework.getTimeframeTitle(prevLevel.Timeframe)
                               + " " + (prevLevel.PivotDirection == 1 ? "resistance" : "support")
                               + " @" + str.tostring(prevLevel.Price, format.mintick)
                            
                               +  " (" + str.tostring(ltaSize) + "pts)"

                            alert(ltaAlertMessage, alert.freq_once_per_bar)
                            if(_ShowNotificationFlags)
                                PAHelperFramework.showNotificationFlag(ltaAlertMessage)
            else
                invalidatedLtaLevels.push(str.tostring(level.Price) + "-1")
        else
            invalidatedLtaLevels.push(str.tostring(level.Price) + "-" + str.tostring(prevLevel.Price) + "-1")
    true

parseSortedLevels() =>
    sortedLevelsCount = _PerformSorting ? _SortedLevels.size() : na
    if(sortedLevelsCount > 0 and _PerformSorting and _isRecentBar)
        invalidatedLtaLevels = array.new_string()
        PAHelperFramework.LevelInfo prevLevel = na
        array<PAHelperFramework.LevelInfo> decongestionBucket = array.new<PAHelperFramework.LevelInfo>()
        for [i, l] in _SortedLevels 
            level = l
            levelTimeframeInfo = _Timeframes.get(level.Timeframe)
            levelInLimits = PAHelperFramework.isLevelInLimits(level.Price, _levelLimitsPts)

            if(levelInLimits)
                if(_EnableLevelsHeatmap and barstate.isconfirmed)
                    if(na(level.ShadeBox))
                        addLevelShade(level)

                    if(level.ShadeBox.get_left() >= level.Time)
                        level.ShadeBox.set_right(_timeClose + _CurrentTFDuration * 10)

                if(_levelDecongestionPts >= 0 and i > 0 and _LevelsInfo.HasChanged and sortedLevelsCount > 3 and (_DecongestionSize != "Off" or _GroupLevelTimeframes != "Show the highest timeframe"))
                    if(decongestionBucket.size() > 0)
                        bucketRangeSize = (level.Price - decongestionBucket.first().Price) / _MinTick

                        if(bucketRangeSize > _levelDecongestionPts)
                            prioritizeLevels(decongestionBucket)
                            decongestionBucket.clear()
                        
                    decongestionBucket.push(level)

                if(barstate.isconfirmed and (_Timeframe1.IsClosing or _Timeframe2.IsClosing or _Timeframe3.IsClosing or _Timeframe4.IsClosing or _Timeframe5.IsClosing or _Timeframe6.IsClosing or _Timeframe7.IsClosing or _Timeframe8.IsClosing))
                    detectLevelBreakout(level, i)                    
                    if(not na(prevLevel))
                        detectLTAs(level, prevLevel, invalidatedLtaLevels)
            else
                hidden = PAHelperFramework.hideLevel(level, _Timeframes, _ShowLines, _isRecentBar)
        
            prevLevel := l
        
        if(decongestionBucket.size() > 0)
            prioritizeLevels(decongestionBucket)
        PAHelperFramework.removeLTAFromLevels(invalidatedLtaLevels, _LTAs, _RemoveOldLTAs, (__isInSession or not __InSessionNotificationFilter) and _LTAAlertType != "Created" ? _LTAAlert : false, _ShowNotificationFlags, _levelBreakouts)

addToSortedLevels(PAHelperFramework.LevelInfo level) =>
    added = false
    levelTimeframeInfo = _Timeframes.get(level.Timeframe)
    if(_PerformSorting and _SortedLevels.size() > 0)
        for [i, sortedLevel] in _SortedLevels
            sortedLevelTimeframeInfo = _Timeframes.get(sortedLevel.Timeframe)

            if (level.Price < sortedLevel.Price or (level.Price == sortedLevel.Price and sortedLevelTimeframeInfo.Duration < levelTimeframeInfo.Duration))
                _SortedLevels.insert(i, level)
                _LevelsInfo.HasChanged := true
                if(_LTAs.size() > 0)
                    for l = _LTAs.size() - 1 to 0
                        lta = _LTAs.get(l)
                        if(lta.Box.get_bottom() <= level.Price and level.Price <= lta.Box.get_top())
                            PAHelperFramework.removeLTAInfoEntry(lta, l, _LTAs, _RemoveOldLTAs, (__isInSession or not __InSessionNotificationFilter) and _LTAAlertType != "Created" ? _LTAAlert : false, _ShowNotificationFlags, _levelBreakouts)
                added := true
                break

    if(not added and _PerformSorting)
        _SortedLevels.push(level)
        _LevelsInfo.HasChanged := true

addLevel(array<PAHelperFramework.LevelInfo> levels, float price, string timeframe, int pivotDirection) =>
    timeframeInfo = _Timeframes.get(timeframe)

    canAdd = not na(price) 
     and price > 0
     and isChartTfLowerThan(timeframeInfo)

    if(timeframeInfo.TimeClose.size() > 1)
        levelTime = timeframeInfo.TimeClose.get(timeframeInfo.TimeClose.size() - 2)
        barIndex = bar_index - (time - levelTime) / _CurrentTFDuration

        if(canAdd and levels.size() > 0)
            for i = levels.size() - 1 to 0
                level = levels.get(i)
                if(level.Price == price and level.Timeframe == timeframe)
                    if(level.Time == levelTime)
                        canAdd := false
                        break
                    else
                        removeLevel(levels, i)
                        break

        if(canAdd)
            level = PAHelperFramework.LevelInfo.new(Price = price, Timeframe = timeframe, Time = levelTime, PivotDirection = pivotDirection, CrossedThroughTimes = array.new_float(), BarIndex = barIndex)
            levels.push(level)

            addToSortedLevels(level)
            addLevelLine(level)
            if(_EnableLevelsHeatmap)
                addLevelShade(level)

validateLevels(PAHelperFramework.TimeframeInfo timeFrame) =>
    timeFrameOpen = PAHelperFramework.getLast(timeFrame.Open)
    timeFrameClose = PAHelperFramework.getLast(timeFrame.Close)

    invalidatedLtaLevels = array.new_string()
    
    timeFrameTimeOpen = PAHelperFramework.getLast(timeFrame.TimeOpen)
    timeFrameTimeClose = PAHelperFramework.getLast(timeFrame.TimeClose)
    
    if(timeFrame.Levels.size() > 0)
        for i = timeFrame.Levels.size() - 1 to 0
            PAHelperFramework.LevelInfo level = timeFrame.Levels.get(i)
            if(level.CrossedThroughCount < _LevelInvalidationCrossesCount
             and level.Time < timeFrame.TimeClose.get(timeFrame.TimeClose.size() - 2)
             and PAHelperFramework.isLevelInLimits(level.Price, _levelLimitsPts * PAHelperFramework.getTimeframeRatioFactor(timeFrame) / 3.0))
                bool bodyCrossedThrough = timeFrame.IsClosing and math.min(timeFrameClose, timeFrameOpen) <= level.Price and level.Price <= math.max(timeFrameClose, timeFrameOpen) and timeFrameClose != timeFrameOpen
                bool wickCrossedThrough =  barstate.isconfirmed and level.CrossedThroughCount >= 1 and _candleLow < level.Price and level.Price < _candleHigh
                bool currentBodyCrossedThrough = barstate.isconfirmed and level.CrossedThroughCount >= 1 and math.min(_candleOpen, _candleClose) <= level.Price and level.Price <= math.max(_candleOpen, _candleClose) and timeFrameClose != timeFrameOpen

                if(bodyCrossedThrough or currentBodyCrossedThrough or (wickCrossedThrough and (timeframe.period == level.Timeframe or not level.CrossedThroughTimes.includes(PAHelperFramework.pairingFunction(0.5, timeFrameTimeOpen)))))
                    if(bodyCrossedThrough)
                        level.CrossedThroughCount := level.CrossedThroughCount + 1
                        level.CrossedThroughTimes.push(PAHelperFramework.pairingFunction(1, timeFrameTimeOpen))
                    
                    if(currentBodyCrossedThrough)
                        level.CrossedThroughCount := level.CrossedThroughCount + 0.5
                    else if(wickCrossedThrough)
                        level.CrossedThroughCount := level.CrossedThroughCount + 0.5
                        level.CrossedThroughTimes.push(PAHelperFramework.pairingFunction(0.5, timeFrameTimeOpen))
                                                    
                    if(level.CrossedThroughCount >= 1 and _ShowLines and _isRecentBar)
                        levelLine = timeFrame.Lines.size() > level.LineIndex ? timeFrame.Lines.get(level.LineIndex) : na
                        if(na(levelLine))
                            [ln, lb] = addLevelLine(level, level.LineIndex)
                            levelLine := ln

                        if(_LevelInvalidationCrossesCount > 1)
                            levelLine.set_style(line.style_dashed)

                            if(level.CrossedThroughCount >= _LevelInvalidationCrossesCount - 1)
                                levelLine.set_style(line.style_dotted)

                        if(_EnableLevelsHeatmap)
                            if(na(level.ShadeBox))
                                addLevelShade(level)
                            level.ShadeBox.set_bgcolor(color.rgb(color.r(timeFrame.HeatmapColor), color.g(timeFrame.HeatmapColor), color.b(timeFrame.HeatmapColor), color.t(timeFrame.HeatmapColor) * 2))

            if(level.CrossedThroughCount >= _LevelInvalidationCrossesCount)
                detectLevelBreakout(level, -1)                    
                removeLevel(timeFrame.Levels, i)

                invalidatedLtaLevels.push(str.tostring(level.Price))
        
    PAHelperFramework.removeLTAFromLevels(invalidatedLtaLevels, _LTAs, _RemoveOldLTAs, (__isInSession or not __InSessionNotificationFilter) and _LTAAlertType != "Created" ? _LTAAlert : false, _ShowNotificationFlags, _levelBreakouts)

if(_DetectLevels)
    array<PAHelperFramework.TimeframeInfo> parsedTimeframes = array.new<PAHelperFramework.TimeframeInfo>()

    for timeFrame in _Timeframes.values()
        parsedTimeframes.push(timeFrame)

        if(timeFrame.IsClosing)
            addLevel(timeFrame.Levels, timeFrame.PivotHigh, timeFrame.Timeframe, 1)
            addLevel(timeFrame.Levels, timeFrame.PivotLow, timeFrame.Timeframe, -1)
        
        validateLevels(timeFrame)

        extendLevels(timeFrame.Levels)

parseSortedLevels()

var _CleanZones = array.new<PAHelperFramework.CleanZone>()
array<PAHelperFramework.CleanZone> _removedCleanZones = na
if(_EnableLTAs and _LtaType == "Candles")
    _removedCleanZones := PAHelperFramework.detectCleanZones(_candleClose[1], _candleLow, _candleHigh, _candleClose, _MinLtaCandles, _MaxLtaCandles, _CleanZones, _levelLimitsPts, _LtaFillColor, (__isInSession or not __InSessionNotificationFilter) and _LTAAlert and _LTAAlertType != "Mitigated", _ShowNotificationFlags)

var _HardcodedPsychoLevelInterval = _ShowPsychologicalLevels ? PAHelperFramework.getHardcodedTickerValue(_HardcodedPsychoLevelIntervals) : na

var float _PsychologicalLevelsInterval = na
var array<line> _PsychoLevelLines = array.new_line()

if(_ShowPsychologicalLevels and barstate.isconfirmed and _isRecentBar)
    _PsychologicalLevelsInterval :=
     _RawPsychologicalLevelsInterval == "Auto"
     ? not na(_HardcodedPsychoLevelInterval) ? _HardcodedPsychoLevelInterval
     : PAHelperFramework.getDefaultPsychologicalLevelsInterval()
     : str.tonumber(_RawPsychologicalLevelsInterval)

    while(_RawPsychologicalLevelsInterval == "Auto" and _PsychologicalLevelsInterval / _MinTick < _atrSize)
        _PsychologicalLevelsInterval *= 2

    if(_Theme == "Light theme" and _PsychologicalLevelsColor == color.rgb(255, 249, 196, 70))
        _PsychologicalLevelsColor := color.rgb(0, 6, 59, 70)
    
    PAHelperFramework.drawPsychologicalLevels(_PsychologicalLevelsInterval, _PsychoLevelLines, _PsychologicalLevelsColor, _levelLimitsPts)

/////////////BIAS
var _BiasTf1Timeframe = input.timeframe("D", title = "", inline = "BiasTF", group = "Session Bias", display = display.none)
_BiasTf1Timeframe := PAHelperFramework.overrideOrDefault("BiasTf1Timeframe", _BiasTf1Timeframe, _IndicatorDescription)
var _BiasTf2Timeframe = input.timeframe("240", title = "", inline = "BiasTF", group = "Session Bias", display = display.none)
_BiasTf2Timeframe := PAHelperFramework.overrideOrDefault("BiasTf2Timeframe", _BiasTf2Timeframe, _IndicatorDescription)
var _AutoBias = input.bool(true, "Auto TFs (override)", tooltip = "Choose HTF1 and HTF2 to form the bias.\n\nThe session bias is considered to be a combination of the latest HTF candles (e.g. previous D candle & previous 4h candle). If both of them are bullish, then we're expecting a bullish move on a lower timeframe session (breakout, retracement, etc.), and vice-versa for bearish. A mixed session is when one of the higher timeframe candles is bullish, and the other is bearish. When the session is biased, trades in the opposite direction have lower probability. By default, there is an automatic selection of the HTF candles, e.g.: D & 4h for scalping/day-trading timeframes, W & D for swing timeframes, 3M & 1M for long term investment timeframes\n\nPredefined in templates:\nBias", inline = "BiasTF", group = "Session Bias", display = display.none)
_AutoBias := PAHelperFramework.overrideOrDefault("AutoBias", _AutoBias, _IndicatorDescription)

var _ShowBiasCandlesDepiction = input.bool(true, title = "Depict bias candles", inline = "BiasDepiction",  group = "Session Bias", display = display.none)
_ShowBiasCandlesDepiction := PAHelperFramework.overrideOrDefault("ShowBiasCandlesDepiction", _ShowBiasCandlesDepiction, _IndicatorDescription)
var _DepictionPosition = input.string(position.bottom_right, title = "", options = [position.top_left, position.top_center, position.top_right, position.middle_left, position.middle_center, position.middle_right, position.bottom_left, position.bottom_center, position.bottom_right], inline = "BiasDepiction", group = "Session Bias", display = display.none)
_DepictionPosition := PAHelperFramework.overrideOrDefault("DepictionPosition", _DepictionPosition, _IndicatorDescription)
var _DepictionSize = input.string(size.normal, title = "", inline = "BiasDepiction", tooltip = "Show a depiction of the HTF candles that give the bias, relative and proportional to their open/close prices", options = [size.tiny, size.small, size.normal, size.large, size.huge], group = "Session Bias", display = display.none)
_DepictionSize := PAHelperFramework.overrideOrDefault("DepictionSize", _DepictionSize, _IndicatorDescription)

var int _DepictionLevels = 
 _DepictionSize == size.tiny ? 0 :
 _DepictionSize == size.small ? 4 :
 _DepictionSize == size.normal ? 10 :
 _DepictionSize == size.large ? 15 :
 _DepictionSize == size.huge ? 20 :
 0

var _ShowBiasDepictionPrices = input.bool(false, title = "+ Prices", inline = "BiasDepiction",  group = "Session Bias", display = display.none)
_ShowBiasDepictionPrices := PAHelperFramework.overrideOrDefault("ShowBiasDepictionPrices", _ShowBiasDepictionPrices, _IndicatorDescription)
var _DepictionRowSize = _ShowBiasDepictionPrices ? size.small : size.tiny

var _DepictHtfBiasCandles1 = input(false, title = "Depict bias candles on chart (HTF 1)", group = "Session Bias", inline = "biasarea1", tooltip = "Highlight the background according to the current HTF1 candle", display = display.none)
var _BullishBiasCandleBoxColor1 = input.color(color.rgb(8, 122, 153, 90), title = "", group = "Session Bias", inline = "biasarea1", display = display.none)
_BullishBiasCandleBoxColor1 := PAHelperFramework.overrideOrDefault("BullishBiasCandleBoxColor1", _BullishBiasCandleBoxColor1, _IndicatorDescription)
var _BearishBiasCandleBoxColor1 = input.color(color.rgb(136, 14, 79, 90), title = "", group = "Session Bias", inline = "biasarea1", display = display.none)
_BearishBiasCandleBoxColor1 := PAHelperFramework.overrideOrDefault("BearishBiasCandleBoxColor1", _BearishBiasCandleBoxColor1, _IndicatorDescription)

var _DepictHtfBiasCandles2 = input(false, title = "Depict bias candles on chart (HTF 2)", group = "Session Bias", inline = "biasarea2", tooltip = "Highlight the background according to the current HTF2 candle", display = display.none)
var _BullishBiasCandleBoxColor2 = input.color(color.rgb(8, 153, 129, 85), title = "", group = "Session Bias", inline = "biasarea2", display = display.none)
_BullishBiasCandleBoxColor2 := PAHelperFramework.overrideOrDefault("BullishBiasCandleBoxColor2", _BullishBiasCandleBoxColor2, _IndicatorDescription)
var _BearishBiasCandleBoxColor2 = input.color(color.rgb(255, 82, 82, 85), title = "", group = "Session Bias", inline = "biasarea2", display = display.none)
_BearishBiasCandleBoxColor2 := PAHelperFramework.overrideOrDefault("BearishBiasCandleBoxColor2", _BearishBiasCandleBoxColor2, _IndicatorDescription)

var _ShowBiasArea = input(false, title = "Current bias on chart", group = "Session Bias", inline = "biasarea3", tooltip = "Highlight the background based on the current bias (bullish, bearish or mixed)", display = display.none)
var _BullishAreaBiasColor = input.color(color.rgb(33, 149, 243, 85), title = "", group = "Session Bias", inline = "biasarea3", display = display.none)
_BullishAreaBiasColor := PAHelperFramework.overrideOrDefault("BullishAreaBiasColor", _BullishAreaBiasColor, _IndicatorDescription)
var _BearishAreaBiasColor = input.color(color.rgb(255, 82, 82, 85), title = "", group = "Session Bias", inline = "biasarea3", display = display.none)
_BearishAreaBiasColor := PAHelperFramework.overrideOrDefault("BearishAreaBiasColor", _BearishAreaBiasColor, _IndicatorDescription)
var _MixedAreaBiasColor = input.color(color.rgb(120, 123, 134, 80), title = "", group = "Session Bias", inline = "biasarea3", display = display.none)
_MixedAreaBiasColor := PAHelperFramework.overrideOrDefault("MixedAreaBiasColor", _MixedAreaBiasColor, _IndicatorDescription)

var _BiasAlert = input.bool(false, title = "Alert on bias change", group = "Session Bias", display = display.none)
_BiasAlert := PAHelperFramework.overrideOrDefault("BiasAlert", _BiasAlert, _IndicatorDescription)

if((_Template != "-" and _Template != "Bias") or str.startswith(_IndicatorDescription, "Notifications"))
    _BiasTf1Timeframe := ""
    _BiasTf2Timeframe := ""
    _AutoBias := false
    _ShowBiasCandlesDepiction := false

if(_Template == "Bias" and not (_DepictHtfBiasCandles1 or _DepictHtfBiasCandles2 or _ShowBiasArea))
    _ShowBiasCandlesDepiction := true

var box _BiasCandleBox1 = na
var box _BiasCandleBox2 = na
var box _BiasAreaBox = na

float _biasMax = na
float _biasMin = na
var _Bias = 0

var _BiasTf1 = newTimeframeInfo(
 timeframe = 
  not (_DepictHtfBiasCandles1 or _DepictHtfBiasCandles2 or _ShowBiasArea or _ShowBiasCandlesDepiction) ? na :
  _AutoBias and _CurrentTFDuration >= 60000 * 60 * 24 * 28 ? "12M" :
  _AutoBias and _CurrentTFDuration >= 60000 * 60 * 24 * 7 ? "3M" :
  _AutoBias and _CurrentTFDuration >= 60000 * 60 * 24 ? "M" :
  _AutoBias and _CurrentTFDuration >= 60000 * 60 * 4 ? "W" :
  _AutoBias and _CurrentTFDuration >= 60000 * 5 ? "D" :
  _AutoBias and _CurrentTFDuration < 60000 * 5 ? "240" :
  _BiasTf1Timeframe,
 enabled = true
 )

var _BiasTf2 = newTimeframeInfo(
 timeframe = 
  not (_DepictHtfBiasCandles1 or _DepictHtfBiasCandles2 or _ShowBiasArea or _ShowBiasCandlesDepiction) ? na :
  _AutoBias and _CurrentTFDuration >= 60000 * 60 * 24 * 28 ? "3M" :
  _AutoBias and _CurrentTFDuration >= 60000 * 60 * 24 * 7 ? "M" :
  _AutoBias and _CurrentTFDuration >= 60000 * 60 * 24 ? "W" :
  _AutoBias and _CurrentTFDuration >= 60000 * 60 * 4 ? "D" :
  _AutoBias and _CurrentTFDuration >= 60000 * 5 ? "240" :
  _AutoBias and _CurrentTFDuration < 60000 * 5 ? "60" :
  _BiasTf2Timeframe,
 enabled = true
 )

if(_firstRun)
    _BiasTf1.TimeOpen.push(time(_BiasTf1.Timeframe))
    _BiasTf2.TimeOpen.push(time(_BiasTf2.Timeframe))

    _BiasTf1.TimeClose.push(time_close(_BiasTf1.Timeframe))
    _BiasTf2.TimeClose.push(time_close(_BiasTf2.Timeframe))

    _BiasTf1.Duration := timeframe.in_seconds(_BiasTf1.Timeframe) * 1000
    _BiasTf2.Duration := timeframe.in_seconds(_BiasTf2.Timeframe) * 1000

if(barstate.isfirst)
    _ShowBiasArea := _ShowBiasArea and timeframe.period != _BiasTf2.Timeframe and _CurrentTFDuration <= _BiasTf2.Duration
    _ShowBiasCandlesDepiction := _ShowBiasCandlesDepiction and _CurrentTFDuration <= _BiasTf2.Duration

var _BiasTable = _ShowBiasCandlesDepiction ? table.new(_DepictionPosition, 2, _DepictionLevels + 3, bgcolor = na) : na

_DepictHtfBiasCandles1 := _DepictHtfBiasCandles1 and timeframe.period != _BiasTf1.Timeframe and _CurrentTFDuration <= _BiasTf1.Duration
_DepictHtfBiasCandles2 := _DepictHtfBiasCandles2 and timeframe.period != _BiasTf2.Timeframe and _CurrentTFDuration <= _BiasTf2.Duration

_BiasTf1.IsClosing := false
_BiasTf2.IsClosing := false

if(_ShowBiasCandlesDepiction and not barstate.isfirst)
    biasTf1Title = PAHelperFramework.getTimeframeTitle(_BiasTf1.Timeframe)
    biasTf2Title = PAHelperFramework.getTimeframeTitle(_BiasTf2.Timeframe)

    while(str.length(biasTf1Title) < str.length(biasTf2Title))
        biasTf1Title := biasTf1Title + " "
    
    while(str.length(biasTf1Title) > str.length(biasTf2Title))
        biasTf2Title := biasTf2Title + " "

    _BiasTable.cell(0, 0, biasTf1Title, bgcolor = color.black, text_color = color.gray, text_font_family = font.family_monospace)
    _BiasTable.cell(1, 0, biasTf2Title, bgcolor = color.black, text_color = color.gray, text_font_family = font.family_monospace)

if(_ShowBiasCandlesDepiction or _DepictHtfBiasCandles1 or _DepictHtfBiasCandles2 or _ShowBiasArea)
    redraw = false
    if(_DepictHtfBiasCandles1 or _ShowBiasArea or _ShowBiasCandlesDepiction)
        PAHelperFramework.updateTimeframeDetails(_BiasTf1, str.contains(_IndicatorDescription, "All") ? true : isRecent(1000), withBarConfirmed = true, roundingInterval = _RoundingInterval)
        redraw := true
        
    if(_DepictHtfBiasCandles2 or _ShowBiasArea or _ShowBiasCandlesDepiction)
        PAHelperFramework.updateTimeframeDetails(_BiasTf2, str.contains(_IndicatorDescription, "All") ? true : isRecent(2000), withBarConfirmed = true, roundingInterval = _RoundingInterval)
        redraw := true
     
    biasTf1Close = PAHelperFramework.getLast(_BiasTf1.Close)
    biasTf1Open = PAHelperFramework.getLast(_BiasTf1.Open)

    biasTf2Close = PAHelperFramework.getLast(_BiasTf2.Close)
    biasTf2Open = PAHelperFramework.getLast(_BiasTf2.Open)

    biasTf1TimeClose = PAHelperFramework.getLast(_BiasTf1.TimeClose)
    biasTf1TimeOpen = PAHelperFramework.getLast(_BiasTf1.TimeOpen)

    biasTf2TimeClose = PAHelperFramework.getLast(_BiasTf2.TimeClose)
    biasTf2TimeOpen = PAHelperFramework.getLast(_BiasTf2.TimeOpen)

    _Bias := biasTf1Close < biasTf1Open and biasTf2Close < biasTf2Open ? -1
      : biasTf1Close > biasTf1Open and biasTf2Close > biasTf2Open ? 1
      : 0

    if((__isInSession or not __InSessionNotificationFilter) and _BiasAlert and ta.change(_Bias) != 0)
        caption = "Bias changed from " + (_Bias[1] == -1 ? "bearish" : _Bias[1] == 1 ? "bullish" : "mixed") + " to " + (_Bias == -1 ? "bearish" : _Bias == 1 ? "bullish" : "mixed")
        alert(caption, alert.freq_once_per_bar)
        if(_ShowNotificationFlags)
            PAHelperFramework.showNotificationFlag(caption)
    
    if(redraw and _ShowBiasCandlesDepiction)
        _BiasTable.clear(0, 1, 1, _DepictionLevels + 2)
        _biasMin := math.min(biasTf1Close, biasTf1Open, biasTf2Close, biasTf2Open)
        _biasMax := math.max(biasTf1Close, biasTf1Open, biasTf2Close, biasTf2Open)

        PAHelperFramework.depictBiasCandle(_BiasTable, 0, _BiasTf1, _DepictionLevels, _biasMin, _biasMax, _ShowBiasDepictionPrices, _DepictionRowSize)
        PAHelperFramework.depictBiasCandle(_BiasTable, 1, _BiasTf2, _DepictionLevels, _biasMin, _biasMax, _ShowBiasDepictionPrices, _DepictionRowSize)

    if(_DepictHtfBiasCandles1)
        if(_BiasTf1.IsClosing)
            int timeTo = biasTf1TimeClose
            if(timeframe.period != _BiasTf1.Timeframe)
                timeTo := timeTo - _CurrentTFDuration

            _BiasCandleBox1 := box.new(biasTf1TimeOpen, _candleOpen, timeTo, _candleClose, xloc = xloc.bar_time, bgcolor = na, border_width = 0)
        else
            _BiasCandleBox1.set_bottom(_candleClose)
            _BiasCandleBox1.set_bgcolor(_candleClose >= _BiasCandleBox1.get_top() ? _BullishBiasCandleBoxColor1 : _BearishBiasCandleBoxColor1)

    if(_DepictHtfBiasCandles2)
        if(_BiasTf2.IsClosing)
            int timeTo = biasTf2TimeClose
            if(timeframe.period != _BiasTf2.Timeframe)
                timeTo := timeTo - _CurrentTFDuration

            _BiasCandleBox2 := box.new(biasTf2TimeOpen, _candleOpen, timeTo, _candleClose, xloc = xloc.bar_time, bgcolor = na, border_width = 0)
        else
            _BiasCandleBox2.set_bottom(_candleClose)
            _BiasCandleBox2.set_bgcolor(_candleClose >= _BiasCandleBox2.get_top() ? _BullishBiasCandleBoxColor2 : _BearishBiasCandleBoxColor2)
    
    if(_ShowBiasArea)
        if(_BiasTf2.IsClosing)
            int timeTo = biasTf2TimeClose
            if(timeframe.period != _BiasTf2.Timeframe)
                timeTo := timeTo - _CurrentTFDuration
            
            _BiasAreaBox := box.new(biasTf2TimeOpen, _candleOpen, timeTo, _candleClose, xloc = xloc.bar_time, bgcolor = _Bias == 1 ? _BullishAreaBiasColor : _Bias == -1 ? _BearishAreaBiasColor : _MixedAreaBiasColor, border_width = 0)
        else
            _BiasAreaBox.set_bottom(_candleClose)

/////////////CANDLES

var _SmoothCandles = input.bool(false, title = "Smooth candles", group = "Candles", inline = "SmoothCandles", tooltip = "Eliminates the gaps between candles by redrawing the bars making their open price equal to the previous close. To be used while analysing the price action. Note: the instrument's original candles should be hidden, only leaving the indicator's candles.\n\nPredefined in templates:\nSmooth candles", display = display.none)
_SmoothCandles := PAHelperFramework.overrideOrDefault("SmoothCandles", _SmoothCandles, _IndicatorDescription)
var _SmoothCandlesBullishColor = input.color(color.rgb(39, 145, 133), title = "", inline = "SmoothCandles", group = "Candles", display = display.none)
_SmoothCandlesBullishColor := PAHelperFramework.overrideOrDefault("SmoothCandlesBullishColor", _SmoothCandlesBullishColor, _IndicatorDescription)
var _SmoothCandlesBearishColor = input.color(color.rgb(231, 108, 99), title = "", inline = "SmoothCandles", group = "Candles", display = display.none)
_SmoothCandlesBearishColor := PAHelperFramework.overrideOrDefault("SmoothCandlesBearishColor", _SmoothCandlesBearishColor, _IndicatorDescription)

var _GhostCurrentCandle = input.bool(false, title = "Highlight current candle", group = "Candles", inline = "CurrentCandles", tooltip = "Sets a different color for the currently open candle until a specified percentage of its time has elapsed  (e.g., for a 1h candle, setting the percentage to 75% means it will highlight the candle for the first 45m, showing its normal color only for the last 15m of that candle).\nCan be useful for situations where you want to be less influenced by a candle while it's still open, and so ghosting it out", display = display.none)
_GhostCurrentCandle := PAHelperFramework.overrideOrDefault("GhostCurrentCandle", _GhostCurrentCandle, _IndicatorDescription)
var _RawCandleCompletionThreshold = input.string("90%", title = "", options = ["50%", "75%", "90%", "Close"], inline = "CurrentCandles", group = "Candles", display = display.none)
_RawCandleCompletionThreshold := PAHelperFramework.overrideOrDefault("RawCandleCompletionThreshold", _RawCandleCompletionThreshold, _IndicatorDescription)
var _CurrentCandlesBullishColor = input.color(color.rgb(255, 224, 177, 80), title = "", inline = "CurrentCandles", group = "Candles", display = display.none)
_CurrentCandlesBullishColor := PAHelperFramework.overrideOrDefault("CurrentCandlesBullishColor", _CurrentCandlesBullishColor, _IndicatorDescription)
var _CurrentCandlesBearishColor = input.color(color.rgb(255, 224, 177, 80), title = "", inline = "CurrentCandles", group = "Candles", display = display.none)
_CurrentCandlesBearishColor := PAHelperFramework.overrideOrDefault("CurrentCandlesBearishColor", _CurrentCandlesBearishColor, _IndicatorDescription)

if(_CurrentCandlesBullishColor == color.rgb(255, 224, 177, 80))
    _CurrentCandlesBullishColor := _Styling.CurrentCandleBullish

if(_CurrentCandlesBearishColor == color.rgb(255, 224, 177, 80))
    _CurrentCandlesBearishColor := _Styling.CurrentCandleBearish

var _HighlighNoWick = input.bool(false, title = "Highlight no-wick candles", inline = "NoWickCandles", group = "Candles", tooltip = "Display a border around candles that opened without a wick", display = display.none)
_HighlighNoWick := PAHelperFramework.overrideOrDefault("HighlighNoWick", _HighlighNoWick, _IndicatorDescription)
var _HighlightNoWickColor = input.color(color.white, title = "", inline = "NoWickCandles", group = "Candles", display = display.none)
_HighlightNoWickColor := PAHelperFramework.overrideOrDefault("HighlightNoWickColor", _HighlightNoWickColor, _IndicatorDescription)

var float _CandleCompletionThreshold = _RawCandleCompletionThreshold == "Close" ? _CurrentTFDuration : (_CurrentTFDuration * str.tonumber(str.replace(_RawCandleCompletionThreshold, "%", "")) / 100.0)

color _smoothCandleColor = na
color _smoothCandleBorderColor = na
bool _canShowSmoothCandle = true

if(_SmoothCandles)
    _canShowSmoothCandle := true
    _smoothCandleColor := ((barstate.isrealtime or not barstate.isconfirmed) and _GhostCurrentCandle and timenow - _time < _CandleCompletionThreshold) ? (_isBullish ? _CurrentCandlesBullishColor : _CurrentCandlesBearishColor) : (_SmoothCandles ? (_isBullish ? _SmoothCandlesBullishColor : _SmoothCandlesBearishColor) : color.rgb(0, 0, 0, 100))
    
    noTopWickOnOpen = _HighlighNoWick ? (_candleOpen == _candleHigh and _candleOpen <= _candleClose[1]) or _candleHigh <= _candleClose[1] : false
    noBtmWickOnOpen = _HighlighNoWick ? (_candleOpen == _candleLow and _candleOpen >= _candleClose[1]) or _candleLow >= _candleClose[1] : false
    noTopWickOnClose = _HighlighNoWick ? not barstate.isrealtime and _candleClose == _candleHigh : false
    noBtmWickOnClose = _HighlighNoWick ? not barstate.isrealtime and _candleClose == _candleLow : false
    noWick = noTopWickOnOpen or noBtmWickOnOpen or noTopWickOnClose or noBtmWickOnClose
    _smoothCandleBorderColor := noWick ? (_Theme == "Dark theme" ? _HighlightNoWickColor : (_HighlightNoWickColor == color.white ? (_isBullish ? _SmoothCandlesBearishColor : _SmoothCandlesBullishColor) : _HighlightNoWickColor)) : _smoothCandleColor

plotcandle(_canShowSmoothCandle ? (_SmoothCandles ? _candleClose[1] : _candleOpen) : na, _canShowSmoothCandle ? _candleHigh : na, _canShowSmoothCandle ? _candleLow : na, _canShowSmoothCandle ? _candleClose : na, color = _smoothCandleColor, wickcolor = _smoothCandleColor, bordercolor = _smoothCandleBorderColor, editable = false, display = display.pane)

var _CandleSizes = array.new_float()
var _AtrSizes = array.new_float()

var _ShowStats = input.bool(false, title = "Additional info", inline = "Stats", group = "Candles", tooltip = "Choose whether to display statistical information about candles. Please note that the statistics will be gathered respecting configured sessions, if any.", display = display.none)
_ShowStats := PAHelperFramework.overrideOrDefault("ShowStats", _ShowStats, _IndicatorDescription)
var _ShowStatType = input.string("ATR", title = "", options = ["ATR", "Average candle size"], inline = "Stats", group = "Candles", display = display.none)
_ShowStatType := PAHelperFramework.overrideOrDefault("ShowStatType", _ShowStatType, _IndicatorDescription)
var _ShowAtr = _ShowStats and _ShowStatType == "ATR"
var _ShowAverageCandleSize = _ShowStats and _ShowStatType == "Average candle size"
var _StatsSampleSize = input.int(50, title = "", inline = "Stats", group = "Candles", display = display.none)
_StatsSampleSize := PAHelperFramework.overrideOrDefault("StatsSampleSize", _StatsSampleSize, _IndicatorDescription)
var _StatsInfoPosition = input.string(position.bottom_left, title = "", options = [position.top_left, position.top_center, position.top_right, position.middle_left, position.middle_center, position.middle_right, position.bottom_left, position.bottom_center, position.bottom_right], inline = "Stats", group = "Candles", display = display.none)
_StatsInfoPosition := PAHelperFramework.overrideOrDefault("StatsInfoPosition", _StatsInfoPosition, _IndicatorDescription)

var _EnableRounding = input.bool(false, title = "Snap candles", inline = "Rounding", group = "Candles", tooltip = "Normalize candles by rounding them to the nearest specified price interval (either entered as percentage of the ATR or as the fixed price interval)", display = display.none)
_EnableRounding := PAHelperFramework.overrideOrDefault("EnableRounding", _EnableRounding, _IndicatorDescription)
var _RoundingAmount = input.float(50, minval = 0, title = "", inline = "Rounding", group = "Candles", display = display.none)
_RoundingAmount := PAHelperFramework.overrideOrDefault("RoundingAmount", _RoundingAmount, _IndicatorDescription)
var _IntervalUnit = input.string("ATR %", "", options = ["ATR %", "Fixed amount"], inline = "Rounding", group = "Candles", display = display.none)
_IntervalUnit := PAHelperFramework.overrideOrDefault("IntervalUnit", _IntervalUnit, _IndicatorDescription)
var _SnapMethod = input.string("Floor/Ceil", "", options = ["Round", "Floor/Ceil"], inline = "Rounding", group = "Candles", display = display.none)
_SnapMethod := PAHelperFramework.overrideOrDefault("SnapMethod", _SnapMethod, _IndicatorDescription)
__SnapMethod := _SnapMethod

if(_EnableRounding)
    if(_IntervalUnit == "ATR %")
        _RoundingInterval := _RoundingAmount * _atrSize * _MinTick / 100
    else
        _RoundingInterval := _RoundingAmount

if(_AtrSizes.size() > _StatsSampleSize)
    _AtrSizes.shift()

if(_CandleSizes.size() > _StatsSampleSize)
    _CandleSizes.shift()

_atrSize := _AtrSizes.avg() / _MinTick

plot(_ShowAtr ? math.min(_rawClose, _rawOpen) + math.abs(_rawClose - _rawOpen) / 2 + _atrSize * _MinTick : na, "ATR top", color.rgb(166, 150, 85, 33), linewidth = 1, display = display.pane)
plot(_ShowAtr ? math.min(_rawClose, _rawOpen) + math.abs(_rawClose - _rawOpen) / 2 - _atrSize * _MinTick : na, "ATR bottom", color.rgb(166, 150, 85, 33), linewidth = 1, display = display.pane)

var _StatsInfoTable = table.new(_StatsInfoPosition, 2, 4)
_StatsInfoTable.cell(0, 0, "")
_StatsInfoTable.cell(0, 1, "")
_StatsInfoTable.cell(0, 2, "")
_StatsInfoTable.cell(0, 3, "")

_StatsInfoTable.cell(1, 0, "")
_StatsInfoTable.cell(1, 1, "")
_StatsInfoTable.cell(1, 2, "")
_StatsInfoTable.cell(1, 3, "")

if(_ShowAtr and __isInSession)
    _StatsInfoTable.cell(0, 1, "ATR", text_color = color.white, text_halign = text.align_left, bgcolor = color.gray)
    _StatsInfoTable.cell(1, 1, str.tostring(math.round(_atrSize)) + " pts", text_color = color.white, bgcolor = color.blue, text_halign = text.align_left)

if(_ShowAverageCandleSize and __isInSession)
    _StatsInfoTable.cell(0, 2, "Candle", text_color = color.white, text_halign = text.align_left, bgcolor = color.gray)
    _StatsInfoTable.cell(1, 2, str.tostring(math.round(_CandleSizes.avg())) + " pts", text_color = color.white, bgcolor = color.blue, text_halign = text.align_left)

/////////////GAPS

var _ShowGaps = input.bool(false, title = "Show gaps", group = "Candles", inline = "ShowGaps", tooltip = "Gaps are areas where the price moved sharply up or down, with no trading occurring in between. These gaps can be seen on the chart as the blank space between 2 bars and you can choose whether to identify and highlight any remaining gap from the past", display = display.none)
_ShowGaps := PAHelperFramework.overrideOrDefault("ShowGaps", _ShowGaps, _IndicatorDescription)
var _GapsColor = input.color(color.rgb(255, 235, 59, 73), title = "", group = "Candles", inline = "ShowGaps", display = display.none)
_GapsColor := PAHelperFramework.overrideOrDefault("GapsColor", _GapsColor, _IndicatorDescription)
var _HideOldGaps = input.bool(true, title = "Hide old gaps", group = "Candles", tooltip = "Remove the highlighted gaps that were already filled", display = display.none)
_HideOldGaps := PAHelperFramework.overrideOrDefault("HideOldGaps", _HideOldGaps, _IndicatorDescription)

var _Gaps = array.new<PAHelperFramework.GapInfo>()

if(_ShowGaps and _isRecentBar)
    PAHelperFramework.showGaps(_Gaps, _GapsColor, _CurrentTFDuration,  _HideOldGaps, _levelLimitsPts)

/////////////CANDLE SCORE
var _CandleScores = array.new_float()
var _Volumes = array.new_float()

var _ShowCandleScore = input.bool(false, title = "Show candle score", group = "Candles", inline = "ShowCandleScore", tooltip = "Display an indication whether the latest candle is weak or strong depending on its body, wicks and overall size", display = display.none)
_ShowCandleScore := PAHelperFramework.overrideOrDefault("ShowCandleScore", _ShowCandleScore, _IndicatorDescription)
float _candleScore = na

calculateCandleScore(float candleOpen, float candleClose, float candleHigh, float candleLow) =>
    if(_Volumes.size() > _StatsSampleSize * 1000)
        _Volumes.shift()
            
    _Volumes.push(volume)

    candleSize = (candleHigh -candleLow) / _MinTick
    bodySize = math.abs(candleClose - candleClose[1]) / _MinTick
    bodyScore = 30 * bodySize / candleSize

    topWickSize = (candleHigh - math.max(candleClose, candleClose[1])) / _MinTick
    btmWickSize = (math.min(candleClose, candleClose[1]) - candleLow) / _MinTick

    wickScore = 25 * (1 - (_isBullish ? topWickSize / candleSize : btmWickSize / candleSize))
    float liquidityScore = 0
    if(_isBullish)
        if(btmWickSize < bodySize / 10)
            liquidityScore += 25
        else
            liquidityScore += 25 * btmWickSize / candleSize
            if (topWickSize > 0)
                liquidityScore += 25 * btmWickSize / topWickSize
            else
                liquidityScore += 15
    else
        if(topWickSize < bodySize / 10)
            liquidityScore += 25
        else
            liquidityScore += 25 * topWickSize / candleSize
            if(btmWickSize > 0)    
                liquidityScore += 25 * topWickSize / btmWickSize
            else
                liquidityScore += 15

    atrScore = 5 * candleSize / _atrSize
    previousIsBullish = candleClose[1] > candleClose[2]

    float engulfingScore = 0
    if(_isBullish and not previousIsBullish)
        if(candleClose > candleClose[2])
            engulfingScore += 7.5
    
    if(not _isBullish and previousIsBullish)
        if(candleClose < candleClose[2])
            engulfingScore += 7.5

    volumeScore = 5 * (_Volumes.percentrank(_Volumes.size() - 1) > 50 ? 1 : 0.5)

    if(bodySize < _atrSize / 20)
        bodyScore := 0
        wickScore := 0
        liquidityScore := 0
        atrScore := 0
        engulfingScore := 0
        volumeScore := 0
        
    if(_CandleScores.size() > _StatsSampleSize * 1000)
        _CandleScores.shift()

    _CandleScores.push(bodyScore + wickScore + liquidityScore + atrScore + engulfingScore + volumeScore)
    _CandleScores.percentrank(_CandleScores.size() - 1)

/////////////SESSIONS

var _EnableSession1 = input.bool(false, title = "", inline = "session1", group = "Sessions", display = display.none)
_EnableSession1 := PAHelperFramework.overrideOrDefault("EnableSession1", _EnableSession1, _IndicatorDescription)
//var _Session1 = input.session("0600-1500", title = "", inline = "session1", group = "Sessions", display = display.none)
var _Session1 = input.session("0700-1600", title = "", inline = "session1", group = "Sessions", display = display.none) //DST
_Session1 := PAHelperFramework.overrideOrDefault("Session1", _Session1, _IndicatorDescription)
var _Color1 = input.color(color.rgb(213, 255, 194, 95), title = "", inline = "session1", group = "Sessions", display = display.none)
_Color1 := PAHelperFramework.overrideOrDefault("Color1", _Color1, _IndicatorDescription)
var _Fill1 = input.bool(true, title = "Highlight", inline = "session1", group = "Sessions", display = display.none)
_Fill1 := PAHelperFramework.overrideOrDefault("Fill1", _Fill1, _IndicatorDescription)

var _EnableSession2 = input.bool(false, title = "", inline = "session2", group = "Sessions", display = display.none)
_EnableSession2 := PAHelperFramework.overrideOrDefault("EnableSession2", _EnableSession2, _IndicatorDescription)
//var _Session2 = input.session("1200-2100", title = "", inline = "session2", group = "Sessions", display = display.none)
var _Session2 = input.session("1300-2200", title = "", inline = "session2", group = "Sessions", display = display.none) //DST
_Session2 := PAHelperFramework.overrideOrDefault("Session2", _Session2, _IndicatorDescription)
var _Color2 = input.color(color.rgb(194, 255, 247, 95), title = "", inline = "session2", group = "Sessions", display = display.none)
_Color2 := PAHelperFramework.overrideOrDefault("Color2", _Color2, _IndicatorDescription)
var _Fill2 = input.bool(true, title = "Highlight", inline = "session2", group = "Sessions", display = display.none)
_Fill2 := PAHelperFramework.overrideOrDefault("Fill2", _Fill2, _IndicatorDescription)

var _EnableSession3 = input.bool(false, title = "", inline = "session3", group = "Sessions", display = display.none)
_EnableSession3 := PAHelperFramework.overrideOrDefault("EnableSession3", _EnableSession3, _IndicatorDescription)
//var _Session3 = input.session("2000-0500", title = "", inline = "session3", group = "Sessions", display = display.none)
var _Session3 = input.session("2100-0600", title = "", inline = "session3", group = "Sessions", display = display.none) //DST
_Session3 := PAHelperFramework.overrideOrDefault("Session3", _Session3, _IndicatorDescription)
var _Color3 = input.color(color.rgb(194, 220, 255, 95), title = "", inline = "session3", group = "Sessions", display = display.none)
_Color3 := PAHelperFramework.overrideOrDefault("Color3", _Color3, _IndicatorDescription)
var _Fill3 = input.bool(true, title = "Highlight", inline = "session3", group = "Sessions", display = display.none)
_Fill3 := PAHelperFramework.overrideOrDefault("Fill3", _Fill3, _IndicatorDescription)

var _EnableSession4 = input.bool(false, title = "", inline = "session4", group = "Sessions", display = display.none)
_EnableSession4 := PAHelperFramework.overrideOrDefault("EnableSession4", _EnableSession4, _IndicatorDescription)
//var _Session4 = input.session("2300-0800", title = "", inline = "session4", group = "Sessions", display = display.none)
var _Session4 = input.session("0000-0900", title = "", inline = "session4", group = "Sessions", display = display.none) //DST
_Session4 := PAHelperFramework.overrideOrDefault("Session4", _Session4, _IndicatorDescription)
var _Color4 = input.color(color.rgb(255, 194, 235, 95), title = "", inline = "session4", group = "Sessions", display = display.none)
_Color4 := PAHelperFramework.overrideOrDefault("Color4", _Color4, _IndicatorDescription)
var _Fill4 = input.bool(true, title = "Highlight", inline = "session4", group = "Sessions", display = display.none)
_Fill4 := PAHelperFramework.overrideOrDefault("Fill4", _Fill4, _IndicatorDescription)

var _HideOutsideHoursCandles = input.bool(false, title = "", group = "Sessions", inline = "HideCandles", display = display.none)
_HideOutsideHoursCandles := PAHelperFramework.overrideOrDefault("HideOutsideHoursCandles", _HideOutsideHoursCandles, _IndicatorDescription)
var _OutsideHoursCandlesColor = input.color(color.rgb(42, 46, 57), "Hide candles outside session hours", inline = "HideCandles", group = "Sessions", display = display.none)
_OutsideHoursCandlesColor := PAHelperFramework.overrideOrDefault("OutsideHoursCandlesColor", _OutsideHoursCandlesColor, _IndicatorDescription)
var _Tz = input.string("UTC+0", title = "Timezone", group = "Sessions", display = display.none)
_Tz := PAHelperFramework.overrideOrDefault("Tz", _Tz, _IndicatorDescription)

if(_Tz != "UTC+0")
    _TimeZone := _Tz

if(_Template == "Smooth candles")
    _SmoothCandles := true

    if(str.endswith(_IndicatorDescription, " "))
        _GhostCurrentCandle := true
        _HighlighNoWick := true
        _ShowGaps := true
        _ShowStats := true
        _ShowAtr := true
        _ShowCandleScore := true
        
if(_Template == "Snapped candles")
    _SmoothCandles := true
    _EnableRounding := true
    _SmoothCandlesBearishColor := color.rgb(81, 45, 168)
    _SmoothCandlesBullishColor := color.rgb(24, 72, 204)
    if(str.endswith(_IndicatorDescription, " "))
        _ShowCandleScore := true

    if(str.endswith(_IndicatorDescription, " "))
        _EnableLTAs := true
        _LtaType := "Candles"
        _MinLtaCandles := _EnableLtaCandlesFilter ? _MinLtaCandles : 3

if(_Template == "Opening hours")
    _EnableSession1 := true
    _Session1 := "0200-0459"
    _EnableSession2 := true
    _Session2 := "0800-1059"
    _EnableSession3 := true
    _Session3 := "1800-2259"
    _TimeZone := "America/New_York"
    if(str.endswith(_IndicatorDescription, " "))
        _Session1 := "0130-0459"
        _Session2 := "0730-1129"
        _EnableSession3 := false

if(str.startswith(_IndicatorDescription, "Notifications"))
    _ShowStats := true
    _ShowStatType := "Average candle size"
    _ShowAverageCandleSize := true
    
if(str.startswith(_IndicatorDescription, "Notifications") or (_Template == "Snapped candles" and str.endswith(_IndicatorDescription, " ")) or (_Template == "Smooth candles" and str.endswith(_IndicatorDescription, " ")))
    _EnableSession1 := true
    _Session1 := "0130-0459"
    _EnableSession2 := true
    _Session2 := "0730-1129"
    _TimeZone := "America/New_York"
    _Fill1 := false
    _Fill2 := false



[_isInSession, _isInSession1, _isInSession2, _isInSession3, _isInSession4] = PAHelperFramework.isInSession(_Session1, _Session2, _Session3, _Session4, _EnableSession1, _EnableSession2, _EnableSession3, _EnableSession4, isRecent(5000), _TimeZone, _CurrentTFDuration, _Color1, _Color2, _Color3, _Color4, _Fill1, _Fill2, _Fill3, _Fill4, time + 1000 * 60 * 60 * 24 * 2, 50)
__isInSession := _isInSession

if(_isInSession)
    _AtrSizes.push(math.max(_rawHigh - _rawLow, math.abs(_rawHigh - _rawClose[1]), math.abs(_rawLow - _rawClose[1])))
    _CandleSizes.push(math.abs(_candleClose - _candleOpen) / _MinTick)

    if(_ShowCandleScore)
        _candleScore := calculateCandleScore(_rawOpen, _rawClose, _rawHigh, _rawLow)
        _StatsInfoTable.cell(0, 0, "Candle score", text_color = color.white, text_halign = text.align_left, bgcolor = color.gray)
        _StatsInfoTable.cell(1, 0, str.tostring(math.round(_candleScore)) + "%", text_color = color.white, bgcolor = color.blue, text_halign = text.align_left)

plot(_candleScore, "Candle score", display = display.data_window)

sessionCandleColor = _HideOutsideHoursCandles and not _isInSession ? _OutsideHoursCandlesColor : na
plotcandle(_candleOpen, _candleHigh, _candleLow, _candleClose, color = sessionCandleColor, wickcolor = sessionCandleColor, bordercolor = sessionCandleColor, editable = false, display = display.pane)

/////////////NOTIFICATIONS

var _EnableNotificationBeforeSession = input.bool(false, title = "Notification X min. before session start", group = "Notifications", inline = "BeforeSession", display = display.none)
_EnableNotificationBeforeSession := PAHelperFramework.overrideOrDefault("EnableNotificationBeforeSession", _EnableNotificationBeforeSession, _IndicatorDescription)
var _NotificationBeforeSessionMinutes = input.int(30, title = "", group = "Notifications", inline = "BeforeSession", tooltip = "Notification alert raised X minutes before the start of each of the specified sessions", display = display.none)
_NotificationBeforeSessionMinutes := PAHelperFramework.overrideOrDefault("NotificationBeforeSessionMinutes", _NotificationBeforeSessionMinutes, _IndicatorDescription)

var _EnableEndSessionNotification = input.bool(false, title = "Notification X min. before session end", group = "Notifications", inline = "EndSession", display = display.none)
_EnableEndSessionNotification := PAHelperFramework.overrideOrDefault("EnableEndSessionNotification", _EnableEndSessionNotification, _IndicatorDescription)
var _NotificationEndSessionMinutes = input.int(30, title = "", group = "Notifications", inline = "EndSession", tooltip = "Notification alert raised X minutes before the end of each of the specified sessions", display = display.none)
_NotificationEndSessionMinutes := PAHelperFramework.overrideOrDefault("NotificationEndSessionMinutes", _NotificationEndSessionMinutes, _IndicatorDescription)

var _EnableLevelClosureNotifications = input.bool(false, title = "Closing beyond levels", group = "Notifications", inline = "ClosureNotification", display = display.none)
_EnableLevelClosureNotifications := PAHelperFramework.overrideOrDefault("EnableLevelClosureNotifications", _EnableLevelClosureNotifications, _IndicatorDescription)
var _RawLevelClosureProximityTime = input.string("1m in advance", title = "", options = ["At close", "30s in advance", "1m in advance", "5m in advance"], group = "Notifications", inline = "ClosureNotification", display = display.none)
_RawLevelClosureProximityTime := PAHelperFramework.overrideOrDefault("RawLevelClosureProximityTime", _RawLevelClosureProximityTime, _IndicatorDescription)
var _LevelClosureProximityTime = 1000 * (
       _RawLevelClosureProximityTime == "30s in advance" ? 30
     : _RawLevelClosureProximityTime == "1m in advance" ? 60
     : _RawLevelClosureProximityTime == "5m in advance" ? 60 * 5
     : 0)

var _FilterLevelClosureNotifications = input.string("Always", title = "", options = ["Always", "Filter direction", "Filter bias", "Filter direction & bias"], group = "Notifications", inline = "ClosureNotification", tooltip = "Notification triggered whenever a candle is closing above or below the identified/marked levels on the selected timeframes, and optionally, only show the notifications if the broken level was a resistance when closing above it, or a support when closing below, or alternatively, when the bias aligns with the close (close above on bullish bias/close below on bearish bias)", display = display.none)
_FilterLevelClosureNotifications := PAHelperFramework.overrideOrDefault("FilterLevelClosureNotifications", _FilterLevelClosureNotifications, _IndicatorDescription)

var _DummyIndenter2 = input.color(color.rgb(30, 34, 45, 100), title = "", group = "Notifications", inline = "ClosureTimeframes", display = display.none)
_DummyIndenter2 := PAHelperFramework.overrideOrDefault("DummyIndenter2", _DummyIndenter2, _IndicatorDescription)
var _EnableLevelClosureNotificationsTF1 = input.bool(true, title = "TF1", group = "Notifications", inline = "ClosureTimeframes", display = display.none)
_EnableLevelClosureNotificationsTF1 := PAHelperFramework.overrideOrDefault("EnableLevelClosureNotificationsTF1", _EnableLevelClosureNotificationsTF1, _IndicatorDescription)
var _EnableLevelClosureNotificationsTF2 = input.bool(true, title = "TF2", group = "Notifications", inline = "ClosureTimeframes", display = display.none)
_EnableLevelClosureNotificationsTF2 := PAHelperFramework.overrideOrDefault("EnableLevelClosureNotificationsTF2", _EnableLevelClosureNotificationsTF2, _IndicatorDescription)
var _EnableLevelClosureNotificationsTF3 = input.bool(true, title = "TF3", group = "Notifications", inline = "ClosureTimeframes", display = display.none)
_EnableLevelClosureNotificationsTF3 := PAHelperFramework.overrideOrDefault("EnableLevelClosureNotificationsTF3", _EnableLevelClosureNotificationsTF3, _IndicatorDescription)
var _EnableLevelClosureNotificationsTF4 = input.bool(true, title = "TF4", group = "Notifications", inline = "ClosureTimeframes", display = display.none)
_EnableLevelClosureNotificationsTF4 := PAHelperFramework.overrideOrDefault("EnableLevelClosureNotificationsTF4", _EnableLevelClosureNotificationsTF4, _IndicatorDescription)
var _EnableLevelClosureNotificationsTF5 = input.bool(false, title = "TF5", group = "Notifications", inline = "ClosureTimeframes", display = display.none)
_EnableLevelClosureNotificationsTF5 := PAHelperFramework.overrideOrDefault("EnableLevelClosureNotificationsTF5", _EnableLevelClosureNotificationsTF5, _IndicatorDescription)
var _EnableLevelClosureNotificationsTF6 = input.bool(false, title = "TF6", group = "Notifications", inline = "ClosureTimeframes", display = display.none)
_EnableLevelClosureNotificationsTF6 := PAHelperFramework.overrideOrDefault("EnableLevelClosureNotificationsTF6", _EnableLevelClosureNotificationsTF6, _IndicatorDescription)
var _EnableLevelClosureNotificationsTF7 = input.bool(false, title = "TF7", group = "Notifications", inline = "ClosureTimeframes", display = display.none)
_EnableLevelClosureNotificationsTF7 := PAHelperFramework.overrideOrDefault("EnableLevelClosureNotificationsTF7", _EnableLevelClosureNotificationsTF7, _IndicatorDescription)
var _EnableLevelClosureNotificationsTF8 = input.bool(false, title = "TF8", group = "Notifications", inline = "ClosureTimeframes", display = display.none)
_EnableLevelClosureNotificationsTF8 := PAHelperFramework.overrideOrDefault("EnableLevelClosureNotificationsTF8", _EnableLevelClosureNotificationsTF8, _IndicatorDescription)

var _EnableCustomClosureNotifications = input.bool(false, title = "Closing beyond prices", inline = "CustomClosureNotification", group = "Notifications", tooltip = "Notification triggered whenever a candle is closing above or below any of the custom specified prices (one per line)\n\nNote: the custom levels will be marked on the chart if this option is enabled", display = display.none)
_EnableCustomClosureNotifications := PAHelperFramework.overrideOrDefault("EnableCustomClosureNotifications", _EnableCustomClosureNotifications, _IndicatorDescription)
var _RawCustomClosureProximityTime = input.string("1m in advance", title = "", options = ["At close", "30s in advance", "1m in advance", "5m in advance"], group = "Notifications", inline = "CustomClosureNotification", display = display.none)
_RawCustomClosureProximityTime := PAHelperFramework.overrideOrDefault("RawCustomClosureProximityTime", _RawCustomClosureProximityTime, _IndicatorDescription)
var _CustomClosureProximityTime = 1000 * (
       _RawCustomClosureProximityTime == "30s in advance" ? 30
     : _RawCustomClosureProximityTime == "1m in advance" ? 60
     : _RawCustomClosureProximityTime == "5m in advance" ? 60 * 5
     : 0)

var _RawCustomClosureProximityAllowance = input.string("50% allowance", title = "", options = ["0% allowance", "10% allowance", "20% allowance", "30% allowance", "40% allowance", "50% allowance", "60% allowance", "70% allowance", "80% allowance", "90% allowance", "100% allowance"], group = "Notifications", inline = "CustomClosureNotification", display = display.none)
_RawCustomClosureProximityAllowance := PAHelperFramework.overrideOrDefault("RawCustomClosureProximityAllowance", _RawCustomClosureProximityAllowance, _IndicatorDescription)
var _CustomClosureProximityAllowance =
       _RawCustomClosureProximityAllowance == "0% allowance" ? 0
     : _RawCustomClosureProximityAllowance == "10% allowance" ? 0.1
     : _RawCustomClosureProximityAllowance == "20% allowance" ? 0.2
     : _RawCustomClosureProximityAllowance == "30% allowance" ? 0.3
     : _RawCustomClosureProximityAllowance == "40% allowance" ? 0.4
     : _RawCustomClosureProximityAllowance == "50% allowance" ? 0.5
     : _RawCustomClosureProximityAllowance == "60% allowance" ? 0.6
     : _RawCustomClosureProximityAllowance == "70% allowance" ? 0.7
     : _RawCustomClosureProximityAllowance == "80% allowance" ? 0.8
     : _RawCustomClosureProximityAllowance == "90% allowance" ? 0.9
     : _RawCustomClosureProximityAllowance == "100% allowance" ? 1
     : 0

var _CustomClosureNotificationLevels = input.text_area("1.234 Daily Low\n2.345 Daily High\n3.456\n4.567 Important Level", title = "", group = "Notifications", display = display.none)
_CustomClosureNotificationLevels := PAHelperFramework.overrideOrDefault("CustomClosureNotificationLevels", _CustomClosureNotificationLevels, _IndicatorDescription)

if(str.startswith(_IndicatorDescription, "Notifications"))
    _EnableCustomClosureNotifications := true
    if(_CustomClosureNotificationLevels == "1.234 Daily Low\n2.345 Daily High\n3.456\n4.567 Important Level")
        _CustomClosureNotificationLevels := ""

var _EnableCheckChartTimer = input.bool(false, title = "Notification timer every X min.", group = "Notifications", inline = "CheckChartsTimer", display = display.none)
_EnableCheckChartTimer := PAHelperFramework.overrideOrDefault("EnableCheckChartTimer", _EnableCheckChartTimer, _IndicatorDescription)
var _CheckChartTimerInterval = input.int(60, title = "", group = "Notifications", inline = "CheckChartsTimer", display = display.none)
_CheckChartTimerInterval := PAHelperFramework.overrideOrDefault("CheckChartTimerInterval", _CheckChartTimerInterval, _IndicatorDescription)
var _CheckChartMessage = input.string("Quick chart check 📊⏰ - Current price is {{close}}", tooltip = "Repeated notification alerts every X minutes (timer), starting from the specified date/time (mind the specified base start time HH:mm as well).\n\nWhen using 'Any Alert Function Call', a customized message can be set, with possible variables:\n- {{open}}\n- {{close}}\n- {{low}}\n -{{high}}", display = display.none, group = "Notifications", inline = "CheckChartsTimer",  title = "")
_CheckChartMessage := PAHelperFramework.overrideOrDefault("CheckChartMessage", _CheckChartMessage, _IndicatorDescription)

var _EnableNotificationsStartDateTime = input.bool(false, title = "Notifications start time", group = "Notifications", inline = "NotificationsStart", display = display.none)
_EnableNotificationsStartDateTime := PAHelperFramework.overrideOrDefault("EnableNotificationsStartDateTime", _EnableNotificationsStartDateTime, _IndicatorDescription)
var _NotificationsStartDateTime = input.time(timestamp("2000-01-01T00:55:00"), title = "", group = "Notifications", inline = "NotificationsStart", tooltip = "Only raise alerts starting with the specified date/time", display = display.none)
_NotificationsStartDateTime := PAHelperFramework.overrideOrDefault("NotificationsStartDateTime", _NotificationsStartDateTime, _IndicatorDescription)
_NotificationsStartDateTime := not _EnableNotificationsStartDateTime ? 0 : _NotificationsStartDateTime

if(_NotificationsStartDateTime > 0)
    _StatsInfoTable.cell(0, 3, "Date", text_color = color.white, text_halign = text.align_left, bgcolor = color.gray)
    _StatsInfoTable.cell(1, 3, PAHelperFramework.toFriendlyDateString(time, _TimeZone), text_color = color.white, bgcolor = color.blue, text_halign = text.align_left)

var _InSessionNotificationFilter = input.bool(true, title = "Notifications respect sessions", group = "Notifications", tooltip = "Only raise alerts within the specified session hours", display = display.none)
_InSessionNotificationFilter := PAHelperFramework.overrideOrDefault("InSessionNotificationFilter", _InSessionNotificationFilter, _IndicatorDescription)
__InSessionNotificationFilter := _InSessionNotificationFilter

var _ShowNotificationFlagsInput = input.bool(false, title = "Notification flags", group = "Notifications", tooltip = "Display a flag every time a notification should be raised", display = display.none)
_ShowNotificationFlagsInput := PAHelperFramework.overrideOrDefault("ShowNotificationFlagsInput", _ShowNotificationFlagsInput, _IndicatorDescription)
_ShowNotificationFlags := _ShowNotificationFlagsInput

_triggerNotification = barstate.isrealtime or barstate.isconfirmed and timenow >= _NotificationsStartDateTime

//EnableNotificationBeforeSession
var _LastSessionStartNotificationTime = 0
varip _lastIntrabarSessionStartNotificationTime = 0

_isBeforeSession1 = false
if(_EnableSession1)
    int sessionStart = math.round(str.tonumber(str.split(_Session1, "-").get(0)))
    _isBeforeSession1 := PAHelperFramework.isTimeAtOffset(sessionStart, _NotificationBeforeSessionMinutes, _TimeZone)

_isBeforeSession2 = false
if(_EnableSession2)
    int sessionStart = math.round(str.tonumber(str.split(_Session2, "-").get(0)))
    _isBeforeSession2 := PAHelperFramework.isTimeAtOffset(sessionStart, _NotificationBeforeSessionMinutes, _TimeZone)

_isBeforeSession3 = false
if(_EnableSession3)
    int sessionStart = math.round(str.tonumber(str.split(_Session3, "-").get(0)))
    _isBeforeSession3 := PAHelperFramework.isTimeAtOffset(sessionStart, _NotificationBeforeSessionMinutes, _TimeZone)

_isBeforeSession4 = false
if(_EnableSession4)
    int sessionStart = math.round(str.tonumber(str.split(_Session4, "-").get(0)))
    _isBeforeSession4 := PAHelperFramework.isTimeAtOffset(sessionStart, _NotificationBeforeSessionMinutes, _TimeZone)

_isBeforeSession = false
if(_EnableSession1 or _EnableSession2 or _EnableSession3 or _EnableSession4)
    _isBeforeSession := _isBeforeSession1 or _isBeforeSession2 or _isBeforeSession3 or _isBeforeSession4

_triggerBeforeSessionNotification = _triggerNotification and _EnableNotificationBeforeSession
 and _isBeforeSession
 and _LastSessionStartNotificationTime < timenow
 and _lastIntrabarSessionStartNotificationTime < timenow

if(_triggerBeforeSessionNotification)
    _LastSessionStartNotificationTime := timenow + 62 * 1000 + 1
    _lastIntrabarSessionStartNotificationTime := timenow + 62 * 1000 + 1

alertcondition(_triggerBeforeSessionNotification, title = "Session Start", message = "Session will start soon 📊🌞")
if(_triggerBeforeSessionNotification)
    if(_isBeforeSession1)
        caption = "Session 1 will start soon 📊🌞 @" + PAHelperFramework.toTimeString(timenow + _NotificationBeforeSessionMinutes * 60 * 1000, includeDay = false, tzone = _TimeZone)
        alert(caption, alert.freq_all)
        if(_ShowNotificationFlags)
            PAHelperFramework.showNotificationFlag(caption)
        
    if(_isBeforeSession2)
        caption = "Session 2 will start soon 📊🌞 @" + PAHelperFramework.toTimeString(timenow + _NotificationBeforeSessionMinutes * 60 * 1000, includeDay = false, tzone = _TimeZone)
        alert(caption, alert.freq_all)
        if(_ShowNotificationFlags)
            PAHelperFramework.showNotificationFlag(caption)

    if(_isBeforeSession3)
        caption = "Session 3 will start soon 📊🌞 @" + PAHelperFramework.toTimeString(timenow + _NotificationBeforeSessionMinutes * 60 * 1000, includeDay = false, tzone = _TimeZone)
        alert(caption, alert.freq_all)
        if(_ShowNotificationFlags)
            PAHelperFramework.showNotificationFlag(caption)

    if(_isBeforeSession4)
        caption = "Session 4 will start soon 📊🌞 @" + PAHelperFramework.toTimeString(timenow + _NotificationBeforeSessionMinutes * 60 * 1000, includeDay = false, tzone = _TimeZone)
        alert(caption, alert.freq_all)
        if(_ShowNotificationFlags)
            PAHelperFramework.showNotificationFlag(caption)
        
//_EnableEndSessionNotification
var _LastEndSessionNotificationTime = 0
varip _lastIntrabarEndSessionNotificationTime = 0

_isEndSession1 = false
if (_EnableSession1)
    int sessionEnd = math.round(str.tonumber(str.split(_Session1, "-").get(1)))
    _isEndSession1 := PAHelperFramework.isTimeAtOffset(sessionEnd, _NotificationEndSessionMinutes, _TimeZone)

_isEndSession2 = false
if(_EnableSession2)
    int sessionEnd = math.round(str.tonumber(str.split(_Session2, "-").get(1)))
    _isEndSession2 := PAHelperFramework.isTimeAtOffset(sessionEnd, _NotificationEndSessionMinutes, _TimeZone)

_isEndSession3 = false
if(_EnableSession3)
    int sessionEnd = math.round(str.tonumber(str.split(_Session3, "-").get(1)))
    _isEndSession3 := PAHelperFramework.isTimeAtOffset(sessionEnd, _NotificationEndSessionMinutes, _TimeZone)

_isEndSession4 = false
if(_EnableSession4)
    int sessionEnd = math.round(str.tonumber(str.split(_Session4, "-").get(1)))
    _isEndSession4 := PAHelperFramework.isTimeAtOffset(sessionEnd, _NotificationEndSessionMinutes, _TimeZone)

_isEndSession = (_EnableSession1 or _EnableSession2 or _EnableSession3 or _EnableSession4) or _isEndSession1 or _isEndSession2 or _isEndSession3 or _isEndSession4

bool _triggerEndSessionNotification = _triggerNotification and _EnableEndSessionNotification
 and _isEndSession
 and _LastEndSessionNotificationTime < timenow
 and _lastIntrabarEndSessionNotificationTime < timenow

if(_triggerEndSessionNotification)
    _LastEndSessionNotificationTime := timenow + 62 * 1000 + 1
    _lastIntrabarEndSessionNotificationTime := timenow + 62 * 1000 + 1

alertcondition(_triggerEndSessionNotification, title = "Session End", message = "Session will end soon 📊🌙")
if(_triggerEndSessionNotification)
    if(_isEndSession1)
        caption = "Session 1 will end soon 📊🌙 @" + PAHelperFramework.toTimeString(timenow + _NotificationEndSessionMinutes * 60 * 1000, includeDay = false, tzone = _TimeZone)
        alert(caption, alert.freq_all)
        if(_ShowNotificationFlags)
            PAHelperFramework.showNotificationFlag(caption)

    if(_isEndSession2)
        caption = "Session 2 will end soon 📊🌙 @" + PAHelperFramework.toTimeString(timenow + _NotificationEndSessionMinutes * 60 * 1000, includeDay = false, tzone = _TimeZone)
        alert(caption, alert.freq_all)
        if(_ShowNotificationFlags)
            PAHelperFramework.showNotificationFlag(caption)

    if(_isEndSession3)
        caption = "Session 3 will end soon 📊🌙 @" + PAHelperFramework.toTimeString(timenow + _NotificationEndSessionMinutes * 60 * 1000, includeDay = false, tzone = _TimeZone)
        alert(caption, alert.freq_all)
        if(_ShowNotificationFlags)
            PAHelperFramework.showNotificationFlag(caption)

    if(_isEndSession4)
        caption = "Session 4 will end soon 📊🌙 @" + PAHelperFramework.toTimeString(timenow + _NotificationEndSessionMinutes * 60 * 1000, includeDay = false, tzone = _TimeZone)
        alert(caption, alert.freq_all)
        if(_ShowNotificationFlags)
            PAHelperFramework.showNotificationFlag(caption)

//_EnableLevelClosureNotifications
bool _triggerLevelClosureNotification = _triggerNotification and _EnableLevelClosureNotifications
 and barstate.isconfirmed
 and (_isInSession or not _InSessionNotificationFilter)

if(_triggerLevelClosureNotification)
    _triggerLevelClosureNotification := false
    for levelBreakout in _levelBreakouts
        notifyCurrentBreakout = ((levelBreakout.Level.Timeframe == _Timeframe1.Timeframe and _EnableLevelClosureNotificationsTF1)
         or (levelBreakout.Level.Timeframe == _Timeframe2.Timeframe and _EnableLevelClosureNotificationsTF2)
         or (levelBreakout.Level.Timeframe == _Timeframe3.Timeframe and _EnableLevelClosureNotificationsTF3)
         or (levelBreakout.Level.Timeframe == _Timeframe4.Timeframe and _EnableLevelClosureNotificationsTF4)
         or (levelBreakout.Level.Timeframe == _Timeframe5.Timeframe and _EnableLevelClosureNotificationsTF5)
         or (levelBreakout.Level.Timeframe == _Timeframe6.Timeframe and _EnableLevelClosureNotificationsTF6)
         or (levelBreakout.Level.Timeframe == _Timeframe7.Timeframe and _EnableLevelClosureNotificationsTF7)
         or (levelBreakout.Level.Timeframe == _Timeframe8.Timeframe and _EnableLevelClosureNotificationsTF8))
         and (levelBreakout.Level.PivotDirection == levelBreakout.BreakoutDirection or (_FilterLevelClosureNotifications != "Filter direction" and _FilterLevelClosureNotifications != "Filter direction & bias"))
         and (levelBreakout.BreakoutDirection == _Bias or (_FilterLevelClosureNotifications != "Filter bias" and _FilterLevelClosureNotifications != "Filter direction & bias"))

        if(notifyCurrentBreakout)
            _triggerLevelClosureNotification := true
            caption = PAHelperFramework.getTimeframeTitle(timeframe.period) + " candle closed " + (levelBreakout.BreakoutDirection == 1 ? "above" : "below") + " " + PAHelperFramework.getTimeframeTitle(levelBreakout.Level.Timeframe) + " " + (levelBreakout.Level.PivotDirection == 1 ? "resistance" : "support") + " " + str.tostring(levelBreakout.Level.Price, format.mintick)
            alert(caption, alert.freq_once_per_bar_close)
            if(_ShowNotificationFlags)
                PAHelperFramework.showNotificationFlag(caption)

alertcondition(_triggerLevelClosureNotification, title = "Close Above/Below Marked Levels", message = "Candle closed above/below an identified marked level 📊📐")

//enableCustomClosureNotifications
bool _triggerCustomClosureNotification = _triggerNotification and _EnableCustomClosureNotifications
var _AlertLevelBoxes = array.new_box()

bool _raiseCustomClosureNotification = false
if(_triggerCustomClosureNotification)
    proximitySize = _MinTick * _atrSize * _CustomClosureProximityAllowance
    _raiseCustomClosureNotification := PAHelperFramework.raiseCustomBreakoutAlerts(_CustomClosureNotificationLevels, proximitySize, _CustomClosureProximityTime, _AlertLevelBoxes, _ShowNotificationFlags, _Styling.AlertTextColor, time >= _InitTime and (_isInSession or not _InSessionNotificationFilter), _candleOpen, _candleClose)

alertcondition(_raiseCustomClosureNotification, title = "Close Above/Below Specified Prices", message = "Candle closed above/below a specified custom level 📊🦞")

//_EnableCheckChartTimer
var lastCheckChartTimerNotificationTime = 0
varip lastIntrabarCheckChartTimerNotificationTime = 0

triggerTimerNotification = _triggerNotification and _EnableCheckChartTimer
 and (_isInSession or not _InSessionNotificationFilter)
 and lastCheckChartTimerNotificationTime < timenow
 and lastIntrabarCheckChartTimerNotificationTime < timenow
 and math.floor((timenow - _NotificationsStartDateTime) / 60 / 1000) % _CheckChartTimerInterval == 0

if(triggerTimerNotification)
    lastCheckChartTimerNotificationTime := timenow + 62 * 1000 + 1
    lastIntrabarCheckChartTimerNotificationTime := timenow + 62 * 1000 + 1

alertcondition(triggerTimerNotification, title = "Timer every X min.", message = "Quick Chart Check 📊⏰")
if(triggerTimerNotification)
    message = str.replace_all(_CheckChartMessage, "{{open}}", str.tostring(_candleOpen, format.mintick))
    message := str.replace_all(message, "{{close}}", str.tostring(_candleClose, format.mintick))
    message := str.replace_all(message, "{{low}}", str.tostring(_candleLow, format.mintick))
    message := str.replace_all(message, "{{high}}", str.tostring(_candleHigh, format.mintick))

    alert(message, alert.freq_all)
    if(_ShowNotificationFlags)
        PAHelperFramework.showNotificationFlag(message)

_firstRun := false