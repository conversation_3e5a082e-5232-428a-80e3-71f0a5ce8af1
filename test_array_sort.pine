//@version=6
indicator("Array Sort_By Test", overlay=true)

// --- User-Defined Type ---
// This type has two properties we might want to sort by: 'priority' and 'price'.
type Level
    int priority
    float price
    string name

// --- Array Initialization ---
var levelArray = array.new<Level>()

// Populate the array only on the first bar
if barstate.isfirst
    array.push(levelArray, Level.new(2, 105.5, "Level A"))
    array.push(levelArray, Level.new(1, 102.0, "Level B"))
    array.push(levelArray, Level.new(3, 102.0, "Level C")) // Same price as B, but lower priority
    array.push(levelArray, Level.new(1, 108.0, "Level D")) // Same priority as B, but higher price

// --- Sorting Logic (v6 Compliant) ---

// To sort this array, we use the .sort_by() method, specifying the field name as a string.
// This is the correct and efficient way to sort arrays of custom types in v6.
// It eliminates the need for separate comparison functions like 'compareLevels' or 'priceCompareFunction'.

// Example 1: Sort by price in ascending order.
// array.sort_by(levelArray, "price") 

// Example 2: Sort by priority in ascending order.
array.sort_by(levelArray, "priority")


// --- Visualization ---
// Draw labels on the last bar to display the final, sorted order of the array.
if barstate.islast
    string labelText = "Sorted Array by Priority:\n"
    for i = 0 to array.size(levelArray) - 1
        level = array.get(levelArray, i)
        labelText += "Name: " + level.name + ", Priority: " + str.tostring(level.priority) + ", Price: " + str.tostring(level.price) + "\n"
    
    label.new(bar_index, high, labelText, yloc = yloc.abovebar, style=label.style_label_left)
