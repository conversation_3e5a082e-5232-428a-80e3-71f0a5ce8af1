//@version=6
indicator("Scope Test", overlay=true)

// 1. GLOBAL VARIABLE
// This variable's scope is managed by <PERSON>.
var string storyline_status = "INACTIVE"

// 2. FUNCTION CORRECTLY RETURNING THE NEW STATE
// The function now returns the new status as a string.
manageStoryline() =>
    string newStatus = "ACTIVE"
    newStatus // This is the return value

// 3. SCRIPT LOGIC
if barstate.isconfirmed
    // The global variable is updated by assigning the return value from the function.
    storyline_status := manageStoryline()

// 4. VISUALIZE OUTPUT
if barstate.islast
    label.new(bar_index, high, text="Status: " + storyline_status)
