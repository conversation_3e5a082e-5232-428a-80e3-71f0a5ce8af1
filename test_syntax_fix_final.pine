//@version=6
indicator("Function Syntax Test Final", overlay=true)

// This script uses the syntax discovered from analyzing `Clouds.pine`.
// The key is that when using the `=>` operator for a function assignment,
// parameter types are NOT declared inside the parentheses.

// --- Test Function with correct v5/v6 syntax ---
// The type of 'inputNumber' is inferred by the compiler.
testFunction = (inputNumber) =>
    prefix = "Input was: "
    result = prefix + str.tostring(inputNumber)
    result // Implicit return

// --- Main Logic ---
if barstate.islastconfirmed
    // Call the function.
    string result = testFunction(123)
    
    // If this compiles, the syntax is finally correct.
    label.new(bar_index, high, result, color=color.green, textcolor=color.white)
