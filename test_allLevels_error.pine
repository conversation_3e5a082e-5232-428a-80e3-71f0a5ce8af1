//@version=6
indicator("Test 'allLevels' Scope Error - v6", overlay=true)

// This script demonstrates the "Undeclared identifier" error and its fix.

// --- 1. The Error: Undeclared Identifier ---
// This function is defined BEFORE 'myArray' is declared.
// Pine Script reads from top to bottom, so at this point, 'myArray' does not exist.
/*
clearArray_BROKEN() =>
    label.new(bar_index, high, "Array size: " + str.tostring(array.size(myArray))) // ERROR: Undeclared identifier 'myArray'

clearArray_BROKEN()
*/


// --- 2. The Fix: Pass the Array as a Parameter ---
// This function is self-contained. It accepts any array as an argument 'arr'.
// This is the correct and robust way to handle such operations.
clearArray_FIXED(arr) =>
    // We can now safely operate on 'arr' because it's a defined parameter.
    label.new(bar_index, high, "Array size: " + str.tostring(array.size(arr)))
    array.clear(arr) // Example operation


// --- 3. Declaration and Execution ---
// The array is declared here.
var string[] myArray = array.from("one", "two", "three")

// Now, we call the FIXED function and pass our array into it.
// This will compile and run correctly.
if barstate.islast
    clearArray_FIXED(myArray)
    label.new(bar_index, low, "Cleared array size: " + str.tostring(array.size(myArray)))
