//@version=6
indicator("Function Syntax Test v8 Final", overlay=true)

// This script adds a simple `plot(close)` to provide the compiler with enough
// context to recognize the built-in `barstate` variable. This should resolve
// the "Undeclared identifier" error.

// --- Test Function with correct v6 direct declaration syntax ---
testFunction(int inputNumber) =>
    prefix = "Input was: "
    result = prefix + str.tostring(inputNumber)
    result // Implicit return

// --- Main Logic ---
plot(close) // Provide context for the compiler

if barstate.islastconfirmed
    // Call the function.
    string result = testFunction(123)
    
    // This must compile.
    label.new(bar_index, high, result, color=color.green, textcolor=color.white)
