//@version=5
indicator("State Management Test", overlay=true)

// --- Global State ---
// Declare global variables to hold the state.
var string storyline_status = "Idle"
var int storyline_rejection_time = na
var int storyline_rejection_htf_bar_start_time = na
var int storyline_rejection_htf_bar_end_time = na
var string storyline_level_info = "None" // Using a string for simplicity in the test

// --- State Management Function ---
// This function simulates our complex logic and returns the new state as a tuple.
manageStoryline() =>
    // In a real script, logic would determine these new values.
    // Here, we'll just cycle through states on each bar for testing.
    new_status = storyline_status == "Idle" ? "Awaiting Rejection" : "Confirmed"
    new_rejection_time = time
    new_start_time = time - 3600000 // An hour ago
    new_end_time = time + 3600000 // An hour from now
    new_level_info = "Level at 1.2345"
    
    // Return the new values as a tuple.
    [new_status, new_rejection_time, new_start_time, new_end_time, new_level_info]

// --- Main processing pipeline ---
// This is the core of the solution.
// 1. Call the function and store the returned tuple in a temporary, local tuple.
[new_status_local, new_time_local, new_start_local, new_end_local, new_level_local] = manageStoryline()

// 2. Reassign each global 'var' variable individually using the ':=' operator.
storyline_status := new_status_local
storyline_rejection_time := new_time_local
storyline_rejection_htf_bar_start_time := new_start_local
storyline_rejection_htf_bar_end_time := new_end_local
storyline_level_info := new_level_local

// --- Visual Verification ---
// Display the current state on the chart to confirm it's working.
if barstate.islast
    label.new(bar_index, high, 
      "Status: " + storyline_status + 
      "\nLevel: " + storyline_level_info +
      "\nRejection Time: " + str.tostring(storyline_rejection_time),
      yloc = yloc.abovebar)
