//@version=6
indicator("Var vs Varip Array Test for Custom Types", overlay=true, max_labels_count=500)

// 1. Define a simple custom type, mirroring the original script's structure.
type SnrLevel
    float price
    int detectionTime

// 2. ERROR REPRODUCTION (This line is the problem)
// The 'varip' modifier is not allowed on arrays of a custom user-defined type.
// Uncommenting the line below would cause the compilation error:
// "Variables with varip modifier cannot have type 'array<SnrLevel>'"
// varip array<SnrLevel> processedLevels_error = array.new<SnrLevel>()

// 3. PROPOSED SOLUTION
// Use the 'var' modifier instead. 'var' ensures the variable is initialized only
// once and its value persists across bars, which is the desired behavior here.
var array<SnrLevel> processedLevels_ok = array.new<SnrLevel>()

// 4. SIMULATE NEW DATA & PERSISTENCE
// We'll simulate adding a "new level" every 10 bars to test if the array grows.
bool hasNewLevels = bar_index % 10 == 0

// This block simulates the expensive calculation that should only run when new data arrives.
if (hasNewLevels)
    // Create a new level and add it to our 'var' array.
    newLevel = SnrLevel.new(close, time)
    array.push(processedLevels_ok, newLevel)

// 5. VALIDATE STATE PERSISTENCE
// On every bar, we draw labels for all levels currently in the array.
// If 'var' works correctly, we will see more and more labels appear on the chart
// as the script runs, proving the array is persisting and growing.
if (array.size(processedLevels_ok) > 0)
    for i = 0 to array.size(processedLevels_ok) - 1
        level = array.get(processedLevels_ok, i)
        // Display label for each stored level.
        label.new(bar_index[1], level.price + i * ta.tr, text="Level " + str.tostring(i) + " | Price: " + str.tostring(level.price, format.mintick), style=label.style_label_lower_right, color=color.new(color.blue, 40), textcolor=color.white)

// 6. PLOT ARRAY SIZE
// Plotting the size of the array provides a clear visual confirmation that
// the array is retaining its elements across bars. The line should step up every 10 bars.
plot(array.size(processedLevels_ok), title="Array Size", color=color.orange, linewidth=2)
