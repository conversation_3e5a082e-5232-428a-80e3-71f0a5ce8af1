//@version=6
indicator("Test 'warningLabel' Scope Error - v6", overlay=true)

// This script demonstrates the "Cannot modify global variable" error and its fix.

// --- Global Variable Declaration ---
// 'var' makes this variable persist across bars.
var label warningLabel = na


// --- 1. The Error: Cannot Modify Global Variable ---
// This function attempts to directly reassign the global 'warningLabel'.
// This is not allowed in Pine Script for 'var' variables.
/*
drawLabel_BROKEN() =>
    warningLabel := label.new(bar_index, high, "BROKEN") // ERROR: Cannot modify global variable 'warningLabel' in function.

if barstate.islast
    drawLabel_BROKEN()
/


// --- 2. The Fix: Return the New Object from the Function ---
// This function creates a new label and returns its ID.
// It does not try to modify any global variables directly.
drawLabel_FIXED() =>
    newLabel = label.new(bar_index, high, "FIXED")
    newLabel // Return the ID of the newly created label


// --- 3. Execution and Reassignment ---
// We call the fixed function and assign its return value back to our global variable.
// This is the correct pattern for updating the state of a persistent 'var' object.
if barstate.islast
    warningLabel := drawLabel_FIXED()


// --- Optional: Cleanup ---
// This shows how you can then use the updated global variable.
if barstate.isfirst and not na(warningLabel[1])
    label.delete(warningLabel[1]) // Delete the label from the previous run
