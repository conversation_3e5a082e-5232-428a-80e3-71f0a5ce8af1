// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © RenkCapital [Ren<PERSON>] 2023 - CLOUDS I [TradersMove ™]
// © T3 Calclulation taken from the great Kivan<PERSON> Ozbilgic https://de.tradingview.com/u/KivancOzbilgic/
// © Vortex Cloud ??? (If you know the copyright holder, let me know)
// !!! IF I USE CODE AND FORGOT TO MENTION THE AUTHOR, LEAVE ME A MESSAGE. I WILL MENTION THE CREATOR IMMEDIATELY.


//@version=5
indicator("CLOUDS EN I ™ [TradersMove]", shorttitle="CLOUDS EN I ™ v.0.2", overlay=true, max_boxes_count=500, max_labels_count = 500, max_lines_count=500, max_bars_back=500, explicit_plot_zorder = true)










//+--- Line 20 -----------------------------------------------------------------------------------------------------------------------------------------------------------+//
//+---    ICONS                                                                                                                                                        ---+//
//+------------------+-------------------+-----------------------------+----------------------------------+------------------+----------------+---------------------------+//
// Ⓐ Ⓑ Ⓒ Ⓓ Ⓔ Ⓕ Ⓖ Ⓗ Ⓘ Ⓙ Ⓚ
// ① ② ③ ④ ⑤ ⑥ ⑦ ⑧ ⑨ ⑩ ⑪ ⑫
// ➀ ➁ ➂ ➃ ➄ ➅ ➆ ➇ ➈ ➉ 
// ➊ ➋ ➌ ➍ ➎ ➏ ➐ ➑ ➒ ➓
// ← ↑ → ↓ ⇠ ⇡ ⇢ ⇣ ⇦ ⇧ ⇨ ⇩ ▲ ▼
// ⊕ ⊖ 🔜
// 🔸 🔹 🔶 🔷 🛑
// ™
// TIP: Using ANSI code for naming script modules/parts makes it easy to find your code at the minimap on the right side... enjoy 
// ASCII GRAFICS:   https://patorjk.com/software/taag/#p=display&f=ANSI%20Regular&t= 
// FONT:            ANSI REGULAR
















//+--- Line 50 ------------------------------------------------------------------------------------------------------------------------------------------------------------+//
//+---  █████████  ██████   ██████  ██      ████████ ██ ██████  ███████ 
//+---     ██    ██    ██ ██    ██ ██         ██    ██ ██   ██ ██      
//+---     ██    ██    ██ ██    ██ ██         ██    ██ ██████  ███████ 
//+---     ██    ██    ██ ██    ██ ██         ██    ██ ██           ██ 
//+---     ██     ██████   ██████  ███████    ██    ██ ██      ███████   
//+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------+//
//+---  TOOLTIPS   
//+------------------+-------------------+-----------------------------+----------------------------------+------------------+----------------+---------------------------+//
tt_tc_mode           = 'Modus \'AUTO\':\nCloudwerte können über die Voreinstellungen (Presets) gewählt werden.\nModus \'MANUELL\':\nCloudwerte können manuell eingegeben werden.\n'
tt_tc_mode_en        = 'Mode \'AUTO\':\nCloud values can be set with presets.\nMode \'MANUAL\':\nCloud values can be set individually.\n'

tt_sync              = 'Sync Modus \'aktiviert\':\nSystem verwendet die Einstellungen der TrendCloud automatisch für die Higher Timeframe Cloud.\n'

tt_preset            = 'Hinweis:\nPresets werden nur dann berücksichtigt, wenn der Modus \'AUTO\' ausgewählt wurde.'
tt_preset_en         = 'Attention:\nPresets are only taken into account if \'MODE\' is set to \'AUTO\'.'

tt_tc_vortex         = 'Einstellung der Sensibilität wenn VORTEX als Cloud ausgewählt wurde.'
tt_tc_vortex_en      = 'Sensibility settings if VORTEX cloud is choosen.'

tt_src               = '[Q | L | T]\n[ Quelle | Länge | MA Typ]\nHinweis:\nDer Wert des \'schnellen\' MA muss niedriger sein als der Wert des \'langsamen\' MA.'
tt_src_en            = '[S | L | T]\n[ Source | Length | MA Type]\nHint:\nValue of \'fast\' MA has to be smaller than value of \'slow\' MA.'

tt_fill              = 'FÜLLMODUS:\n\nPosition: Die Füllung der Linien erfolgt erst dann, wenn beide Linien in der korrekten Reihenfolge verlaufen (Fast > Slow | Fast < Slow)\n\nRichtung: Die Füllung der beiden Linien erfolgt, wenn beide Linien in die gleiche Richtung laufen.'
tt_fill_en           = 'FILLING MODE:\n\nPosition: The lines are filled when both lines run in the correct order (Fast > Slow | Fast < Slow)\n\nDirection: The two lines are filled when both lines run in the same direction.'
tt_tc_line           = 'Zeige Linien der Cloud, Einstellung für Transparenz und Linienstärke.'
tt_tc_line_en        = 'Show lines of the cloud, settings for transparency and linewidth.'

tt_tc_band           = 'Zeige Cloud Füllung (Band), Einstellung für Transparenz.'
tt_tc_band_en        = 'Show cloud filling (Band), settings for transparency.'

tt_signal            = '[Grösse | Bull | Bear]'
tt_signal_en         = '[Size | Bull | Bear]'

tt_auto              = 'AUTO:\nWenn \'Auto\' aktiviert ist, versucht das System automatisch die beiden nächst höheren und sinnhaften Timeframes (HTF 1, HTF 2) zu ermitteln.\nIst \'Auto\' deaktiviert, kann das Timeframe manuell gewählt werden.'
tt_auto_en           = 'AUTO:\nIf \'Auto\' is activated, the system automatically tries to determine the next two highest and most sensible time frames (HTF 1, HTF 2).\nIf \'Auto\' is deactivated, the time frame can be selected manually.'

tt_htf               = 'Chart Timeframe => HTF 1 | HTF 2\n 1S => 10S | 30S\n10S =>  1M |  5M\n15S =>  1M |  5M\n30S =>  3M | 15M\n 1M =>  5M | 15M\n 2M =>  5M | 15M\n 3M => 15M |  1H\n 5M => 15M |  1H\n15M =>  1H |  4H\n30M =>  2H |  4H\n 1H =>  4H |  8H\n 2H =>  4H |  8H\n 3H =>  6H |  1D\n 4H =>  8H |  1D\n 1D =>  5D |  2W\n 1W =>  4W |  3M'
tt_htf_tip           = 'Manuelle Auswahl des Timeframes, wird nur berücksichtigt, wenn der Modus \'Auto\' deaktiviert ist.'
tt_htf_tip_en        = 'Manual selection of the timeframe is only taken into account if the \'Auto\' mode is deactivated.'

tt_tc                = 'TRENDCOLOR:\nFärbt die Linie entsprechend des ermittelten Trends ein.'
tt_alert             = 'Die Verwendung des Funktionsaufrufs „alert()“ ermöglicht die Verwendung nur einer Warnung für jede der nachfolgenden Auswahlmöglichkeiten. Alle aktuellen Einstellungen im Skript werden zum Zeitpunkt der Einstellung der Warnung gespeichert.\nEinfach die Alarme auswählen, die gewünscht sind, diese werden dann für das jeweilige Timeframe ausgeführt.'
tt_alert_en          = 'Using the alert() function call allows only one alert to be used for each of the subsequent choices. All current settings in the script are saved at the time the alert is set.\nSimply select the alerts you want and they will then be executed for the respective time frame.'

tt_lb                = '\n'    // Simple linebreak for combining tooltips (e.g.: tooltip_1 + tt_lb + tooltip_2)
tt_dlb               = '\n\n'  // Double linebreak for combining tooltips (e.g.: tooltip_1 + tt_dlb + tooltip_2)



//+--- Line 100 ----------------------------------------------------------------------------------------------------------------------------------------------------------+//
//+---  ████████ ██    ██ ██████  ███████ ███████ 
//+---     ██     ██  ██  ██   ██ ██      ██      
//+---     ██      ████   ██████  █████   ███████ 
//+---     ██       ██    ██      ██           ██ 
//+---     ██       ██    ██      ███████ ███████  
//+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------+//
//+---  TYPES   
//+------------------+-------------------+-----------------------------+----------------------------------+------------------+----------------+---------------------------+//

type SYSTEM
	string  version
	string  date 
	string  info
	int     tz_incr
	bool    tz_sz
	string  tz
	string  datetime
	float   src
	int     n
	string  tf_num
	string  tf_txt
	string  tf_full
	string  tf_short
	string  font

type DISPLAY
	bool    tc     // TrendCloud
	bool    tcs    // TrendCloud Signals
	bool    htc    // HTF TrendCloud
	bool    htcs   // HTF TrendCloud Signals
	bool    ib     // InfoBoard
	bool    cb     // Candlebars 

type THEME
	string  mode
	string  title_t1
	color   bull_t1
	color   bear_t1
	color   neutral_t1
	string  title_t2
	color   bull_t2
	color   bear_t2
	color   neutral_t2
	color   bull
	color   bear
	color   neutral
	string  txt_title
	color   txt_light
	color   txt_dark
	color   white           = #ffffff
	color   black           = #000000
	color   trans           = #ffffff00
	color   darkgrey        = #323232
	color   midgrey         = #646464
	color   lightgrey       = #969696
	color   lightgreen      = #6aff00 
	color   yellow          = #d9ff00
	//==>Theme Farben
	color   grellgruen      = #99ff00
	color   grellpink       = #ff00e8
	color   nextblau        = #00ffff
	color   nextrot         = #ff0066
	color   sysgreen        = #4caf50 	// color.green
	color   sysred          = #ff5252 	// color.red
	color   sysgray         = #787b86 	// color.gray


































//+--- Line 200 ----------------------------------------------------------------------------------------------------------------------------------------------------------+//
//+---  ███████ ██    ██ ███████ ████████ ███████ ███    ███ 
//+---  ██       ██  ██  ██         ██    ██      ████  ████ 
//+---  ███████   ████   ███████    ██    █████   ██ ████ ██ 
//+---       ██    ██         ██    ██    ██      ██  ██  ██ 
//+---  ███████    ██    ███████    ██    ███████ ██      ██  
//+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------+//
//+---  SYSTEM   
//+------------------+-------------------+-----------------------------+----------------------------------+------------------+----------------+---------------------------+//
SYSTEM               sys = SYSTEM.new()
sys_set              = '🔸🔸🔸 System'
sys_cl               = 'ChangeLog:\n'+'Initial Version -> Framework, Functions, Theme\n'

sys.version          := input.string('v.0.2', title='CLOUDS EN ™ [TradersMove]', group=sys_set, inline='02')
sys.date             := input.string('29.03.2024', title='', group=sys_set, inline='02', tooltip=sys_cl)
sys.info             := 'CLOUDS EN ™ [TradersMove]' + ' ' + sys.version
sys.tz_incr          := input.int(1, 'Timezone UTC (+/-) ➜', group = sys_set, inline='04')
//sys.tz_sz          := input.bool(false, title='Summertime?', group = sys_set, inline='04')
sys.tz               := str.format('UTC{0}{1}', sys.tz_incr >= 0 ? '+' : '-', math.abs(sys.tz_incr))
sys.datetime         := str.format_time(timenow, "dd.MM.yyyy | HH:mm", sys.tz) 
sys.src              := close
sys.n                := bar_index

//=> Internal methods
f_getTimeframeNum() =>	
	if timeframe.isminutes
		if str.tonumber(timeframe.period) % 60 == 0
			str.tostring(str.tonumber(timeframe.period)/60)
		else 
			timeframe.period
	else
		timeframe.period

f_getTimeframeUnit() =>
	if timeframe.isminutes
		if str.tonumber(timeframe.period) % 60 == 0
			"H"
		else 
			"m"
	else 
		na

sys.tf_num          := f_getTimeframeNum()
sys.tf_txt          := f_getTimeframeUnit()
sys.tf_full         := sys.tf_num + sys.tf_txt + " TIMEFRAME"
sys.tf_short        := sys.tf_num + sys.tf_txt






















































//+--- Line 300 ----------------------------------------------------------------------------------------------------------------------------------------------------------+//
//+---  ██████  ██ ███████ ██████  ██       █████  ██    ██ 
//+---  ██   ██ ██ ██      ██   ██ ██      ██   ██  ██  ██  
//+---  ██   ██ ██ ███████ ██████  ██      ███████   ████   
//+---  ██   ██ ██      ██ ██      ██      ██   ██    ██    
//+---  ██████  ██ ███████ ██      ███████ ██   ██    ██    
//+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------+//
//+---  DISPLAY SHOW ON/OFF   
//+------------------+-------------------+-----------------------------+----------------------------------+------------------+----------------+---------------------------+//
DISPLAY show         = DISPLAY.new()

display_1            = '🔸🔸🔸 DISPLAY'
show.tc              := input.bool       (true                         ,'① TrendCloud [TC]       '        ,inline='00'      ,group=display_1)
show.tcs             := input.bool       (false                        ,'Signals [MTS]'                    ,inline='00'      ,group=display_1)
show.htc             := input.bool       (false                        ,'② HTF TrendCloud [HTC]   '       ,inline='02'      ,group=display_1)
show.htcs            := input.bool       (false                        ,'Signals [HTCS]'                   ,inline='02'      ,group=display_1)
show.ib              := input.bool       (false                        ,'③ InfoBoard [IB]  '              ,inline='04'      ,group=display_1)

































//+--- Line 350 ---------------------------------------------------------------------------------------------------------------------------------------------------------+//
//+---  ███    ███ ███████ ████████ ██   ██  ██████  ██████  ███████ 
//+---  ████  ████ ██         ██    ██   ██ ██    ██ ██   ██ ██      
//+---  ██ ████ ██ █████      ██    ███████ ██    ██ ██   ██ ███████ 
//+---  ██  ██  ██ ██         ██    ██   ██ ██    ██ ██   ██      ██ 
//+---  ██      ██ ███████    ██    ██   ██  ██████  ██████  ███████ 
//+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------+//
//+---  METHODS   
//+------------------+-------------------+-----------------------------+----------------------------------+------------------+----------------+---------------------------+//

f_Alert(cond, txt, alert_mode, lang='de')  =>
	if cond and lang == 'de'
		alert (txt + ' | TF: ' + sys.tf_short + ' | Kurs: ' + str.tostring(close), alert_mode)
	if cond and lang == 'en'
		alert (txt + ' | TF: ' + sys.tf_short + ' | Price: ' + str.tostring(close), alert_mode)

//=> Internal Functions for f_ma
_haVal() =>
	float _c         =  (open + high + low + close) / 4
	float _o         =  float(na)
	_o               :=  na(_o[1])  ? (open + close) / 2 : (nz(_o[1]) + nz(_c[1])) / 2
	float _h         =  math.max     (high , math.max     (_o,  _c))
	float _l         =  math.min     (low  , math.min     (_o,  _c))
	[_o, _h, _l, _c]

[o_,h_,l_,c_]        =  _haVal()


_gd(src, length, alpha) =>
	float result     = ta.ema(src, length) * (1 + alpha) - ta.ema(ta.ema(src, length), length) * alpha


_t3(src, length, alpha = 0.7) =>
	float result     = _gd(_gd(_gd(src, length, alpha), length, alpha), length, alpha)


_dema(src, length) =>
	float ema1       = ta.ema(src,  length)
	float ema2       = ta.ema(ema1, length)
	float result     = 2 * ema1 - ema2


_tema(src, length) =>
	float ema1       = ta.ema(src,  length)
	float ema2       = ta.ema(ema1, length)
	float ema3       = ta.ema(ema2, length)
	float result     = 3 * (ema1 - ema2) + ema3


// options=['EMA','SMA','SMMA (RMA)','TMA','WMA','VWMA','ZEMA','TEMA','HMA','EHMA','THMA','DEMA','TEMA2','T3','HEMA','VWAP']
method f_ma(string type, float src, simple int len, float alphaInput = .7) =>
	switch type
		'EMA'        => ta.ema(src, len)
		'SMA'        => ta.sma(src, len)
		'SMMA (RMA)' => ta.rma(src, len)
		'TMA'        => ta.sma(ta.sma(src, math.ceil(len / 2)), math.floor(len / 2) + 1)
		'WMA'        => ta.wma(src, len)
		'VWMA'       => ta.vwma(src, len)
		'ZEMA'       => ta.ema(src, len)                   + (ta.ema(src, len)                     - ta.ema( ta.ema(src, len), len ))
		'TEMA'       => (3 * (ta.ema(src, len)))           - (3 * ta.ema((ta.ema(src, len)), len)) + ta.ema(ta.ema((ta.ema(src, len)), len), len)
		'HMA'        => ta.wma(2 * ta.wma(src, len /2)     - ta.wma(src, len), math.round(math.sqrt(len)))
		'HMA3'       => ta.wma( ta.wma(src, (len/2) /3) *3 - ta.wma(src, (len/2)/2)                - ta.wma(src,(len/2)),(len/2))
		'EHMA'       => ta.ema(2 * ta.ema(src, len /2)     - ta.ema(src, len), math.round(math.sqrt(len)))
		'THMA'       => ta.wma( ta.wma(src, len/3) *3      - ta.wma(src, len / 2)                  - ta.wma(src, len), len)
		'DEMA'       => _dema (src, len)
		'TEMA2'      => _tema (src, len)
		'T3'         => _t3   (src, len, alphaInput)
		'HEMA'       => ta.ema (o_, len)
		'VWAP'       => ta.vwap
		=> 
			e1 = ta.ema(close, len)
			e2 = ta.ema(e1, len)
			2 * e1 - e2


method f_getCloudValues( simple string cloudpreset ) =>
	switch cloudpreset
		'EHMA 20/28' 	=> [20   ,'EHMA' ,28   ,'EHMA']
		'EMA 13/48'		=> [13   ,'EMA'  ,48   ,'EMA']
		'EMA 5/21'	    => [5    ,'EMA'  ,21   ,'EMA']
		'EMA 8/13'	    => [8    ,'EMA'  ,13   ,'EMA']
		'EMA 20/50'	    => [20   ,'EMA'  ,50   ,'EMA']
		'EMA 50/200'	=> [50   ,'EMA'  ,200  ,'EMA']
		'EMA 55/144'	=> [55   ,'EMA'  ,144  ,'EMA']
	

method f_getTrend( float src ) =>
	t_isBullish         = src > src[1]
	t_isBearish         = src < src[1]
	t_wasBullish        = src[1] > src[2]
	t_wasBearish        = src[1] < src[2]
	t_isBullishChange   = t_isBullish and t_wasBearish
	t_isBearishChange   = t_isBearish and t_wasBullish
	t_CondIni           = 0, t_CondIni := t_isBullishChange ? 1 : t_isBearishChange ? -1 : t_CondIni[1]
	t_long              = t_isBullishChange and t_CondIni[1] == -1
	t_short             = t_isBearishChange and t_CondIni[1] == 1
	[t_isBullish, t_isBearish, t_wasBullish, t_wasBearish, t_long, t_short]


method f_vortex_cloud( int factor, int sensibility ) =>
	v_len        = factor * (sensibility / 2)
	// Using TA built in ta.functions because of speed
	v_sma        = ta.sma(close  ,v_len)
	v_ema        = ta.ema(close  ,v_len)
	v_wma        = ta.wma(close  ,v_len)
	v_vwma       = ta.vwma(close ,v_len)
	v_hma        = ta.hma(close  ,v_len)
	v_rma        = ta.rma(close  ,v_len)
	v_out        = (v_sma + v_ema + v_wma + v_vwma + v_hma + v_rma) / 6
	v_out


method f_getFont( simple string font ) =>
	switch font
		"Standard"          => font.family_default
		"Monospace"         => font.family_monospace


method f_getAutoTimeframes ( simple string tf ) =>
	switch tf
		//"target_timeframe"	=> [tf-2, tf-1, tf+1, tf+2]
		'1S' 	=>	['10S'  ,'30S']
		'10S' 	=>	['1'    ,'3']
		'15S' 	=>	['1'    ,'5']
		'30S' 	=>	['3'    ,'15']
		'1' 	=>	['5'    ,'15']
		'2' 	=>	['5'    ,'15']
		'3'		=>	['15'   ,'60']
		'5' 	=>	['15'   ,'60']
		'15' 	=>	['60'   ,'240']
		'30'	=>	['120'  ,'240']
		'60' 	=>	['240'  ,'480']
		'120' 	=>	['240'  ,'480']
		'180' 	=>	['360'  ,'D']
		'240'	=>	['480'  ,'D']
		'D'		=> 	['5D'   ,'2W']
		'W'		=> 	['4W'   ,'3M']


method f_getPosition( simple string pos ) =>
	switch pos
		'Oben Links'	=> position.top_left
		'Oben Center'	=> position.top_center
		'Oben Rechts'	=> position.top_right
		'Mitte Links'	=> position.middle_left
		'Mitte Center'	=> position.middle_center
		'Mitte Rechts'	=> position.middle_right
		'Unten Links'	=> position.bottom_left
		'Unten Center'	=> position.bottom_center
		'Unten Rechts'	=> position.bottom_right
		'Top Left'		=> position.top_left
		'Top Center'	=> position.top_center
		'Top Right'		=> position.top_right
		'Middle Left'	=> position.middle_left
		'Middle Center'	=> position.middle_center
		'Middle Right'	=> position.middle_right
		'Bottom Left'	=> position.bottom_left
		'Bottom Center'	=> position.bottom_center
		'Bottom Right'	=> position.bottom_right

method f_getSize(string _size, bool _l=false) =>
	size = switch _size
		'Auto'             => not(_l) ? size.auto   : size.auto
		'Mini'             => not(_l) ? size.tiny   : size.small
		'Klein'            => not(_l) ? size.small  : size.normal
		'Normal'           => not(_l) ? size.normal : size.large
		'Groß'             => not(_l) ? size.large  : size.huge
		'Sehr Groß'        => not(_l) ? size.huge   : size.huge
		'Tiny'             => not(_l) ? size.tiny   : size.small
		'Small'            => not(_l) ? size.small  : size.normal
		'Large'            => not(_l) ? size.large  : size.huge
		'Huge'             => not(_l) ? size.huge   : size.huge
	size


//=> don't like it... seems to be buggy... recode it Renkman !!!
f_getTF_label(_tf) =>
	string output = ''
	[tfperiod, tfmult, tfsec, tfmin, tfd, tfw, tfm]  = request.security('',_tf,[timeframe.period,timeframe.multiplier,timeframe.isseconds,timeframe.isminutes,timeframe.isdaily,timeframe.isweekly,timeframe.ismonthly])

	if(tfsec)
		output := str.tostring(tfmult) + 's'
	else if(tfmin)
		if(tfmult < 59)
			output := tfperiod + 'm'
		else
			output := str.tostring(tfmult / 60) + 'h'
	else if(tfd)
		//if(tfmult < 2 or _tf == '1D' or _tf == 'D')
		//	output := '1D'
		//else
		output := tfperiod
	else if(tfw)
		if(tfmult == 1 or _tf == '1W')
			output := '1W'
		else
			output := tfperiod
	else if(tfm)
		if(tfmult == 1)
			output := '1M'
		else
			output := tfperiod	
	output

f_SecurityNoRepaint(_tf, _exp, _barmerge) =>
	request.security(syminfo.tickerid, _tf, _exp[barstate.isrealtime ? 1 : 0], _barmerge)[barstate.isrealtime ? 0 : 1]












































//+--- Line 600 ----------------------------------------------------------------------------------------------------------------------------------------------------------+//
//+---  ████████ ██   ██ ███████ ███    ███ ███████ 
//+---     ██    ██   ██ ██      ████  ████ ██      
//+---     ██    ███████ █████   ██ ████ ██ █████   
//+---     ██    ██   ██ ██      ██  ██  ██ ██      
//+---     ██    ██   ██ ███████ ██      ██ ███████ 
//+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------+//
//+---  THEME   
//+---                input              def_val                       title                              inline             group            opt/tooltips/etc.        ---+//
//+------------------+-------------------+-----------------------------+----------------------------------+------------------+----------------+---------------------------+//
THEME col            = THEME.new()
theme                = '🔸🔸🔸 THEME'
col.mode             := input.string     ('Default'                    ,'Theme  '                         ,inline='02'       ,group=theme     ,options=['Default', 'Basic'])
col_font_tmp         = input.string      ('Monospace'                  ,'Fontstyle'                       ,inline='02'       ,group=theme     ,options=['Standard','Monospace'])
col.title_t1         := input.string     ('Default'                    ,'Theme 1 '                        ,inline='04'       ,group=theme)
col.bull_t1          := input.color      (color.rgb(0,255,255)       ,'Bull'                            ,inline='04'       ,group=theme)
col.bear_t1          := input.color      (color.rgb(255,0,102)       ,'  Bear'                          ,inline='04'       ,group=theme)
col.neutral_t1       := input.color      (color.rgb(178,181,190)     ,'Neutral'                         ,inline='04'       ,group=theme)
col.title_t2         := input.string     ('Basic'                      ,'Theme 2 '                        ,inline='06'       ,group=theme)
col.bull_t2          := input.color      (color.rgb(153,255,0)       ,'Bull'                            ,inline='06'       ,group=theme)
col.bear_t2          := input.color      (color.rgb(255,0,232)       ,'  Bear'                          ,inline='06'       ,group=theme)
col.neutral_t2       := input.color      (color.rgb(178,181,190)     ,'Neutral'                         ,inline='06'       ,group=theme)
col.txt_title        := input.string     ('Text'                       ,'       '                         ,inline='08'       ,group=theme)
col.txt_light        := input.color      (color.rgb(255,255,255,0)   ,'Light'                           ,inline='08'       ,group=theme)
col.txt_dark         := input.color      (color.rgb(63,63,63)        ,'Dark'                            ,inline='08'       ,group=theme)

col.bull             := col.mode == 'Basic' ? col.bull_t2    : col.bull_t1 
col.bear             := col.mode == 'Basic' ? col.bear_t2    : col.bear_t1
col.neutral          := col.mode == 'Basic' ? col.neutral_t2 : col.neutral_t1

//==> FontSettings
sys.font             := f_getFont(col_font_tmp)


















//+--- Line 650 ----------------------------------------------------------------------------------------------------------------------------------------------------------+//
//+---  ████████ ██████  ███████ ███    ██ ██████   ██████ ██       ██████  ██    ██ ██████  
//+---     ██    ██   ██ ██      ████   ██ ██   ██ ██      ██      ██    ██ ██    ██ ██   ██ 
//+---     ██    ██████  █████   ██ ██  ██ ██   ██ ██      ██      ██    ██ ██    ██ ██   ██ 
//+---     ██    ██   ██ ██      ██  ██ ██ ██   ██ ██      ██      ██    ██ ██    ██ ██   ██ 
//+---     ██    ██   ██ ███████ ██   ████ ██████   ██████ ███████  ██████   ██████  ██████  
//+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------+//
//+---  TrendCloud [TC]   
//+---                input              def_val                       title                              inline             group            opt/tooltips/etc.        ---+//
//+------------------+-------------------+-----------------------------+----------------------------------+------------------+----------------+---------------------------+//

tc_set               = '🔸🔸🔸 ① TRENDCLOUD [TC]'

tc_mode              = input.string      ('Auto'                       ,'Mode     '                       ,inline='02'        ,group=tc_set   ,options=['Auto', 'Manual'] ,tooltip=tt_tc_mode_en)
tc_preset            = input.string      ('EHMA 20/28'                 ,'Presets   '                      ,inline='04'        ,group=tc_set   ,options=['EHMA 20/28', 'EMA 5/21', 'EMA 8/13', 'EMA 13/48', 'EMA 20/50', 'EMA 55/144', 'EMA 50/200', 'Vortex'], tooltip=tt_preset)
tc_sens              = input.int         (2                            ,'Vortex Sensibility'              ,inline='04'        ,group=tc_set   ,tooltip=tt_tc_vortex_en)
tc_fill              = input.string      ('Direction'                  ,'Fillmode   '                     ,inline='06'        ,group=tc_set   ,options=['Position', 'Direction'] ,tooltip=tt_fill_en)
tc_f_src             = input.source      (close                        ,'MA Fast   '                      ,inline='08'        ,group=tc_set   ,tooltip=tt_src_en)
tc_f_len             = input.int         (21                           ,''                                ,inline='08'        ,group=tc_set   ,minval=1)
tc_f_type            = input.string      ('SMA'                        ,''                                ,inline='08'        ,group=tc_set   ,options=['EMA','SMA','SMMA (RMA)','TMA','WMA','VWMA','ZEMA','TEMA','HMA','EHMA','THMA','DEMA','TEMA2','T3','HEMA','VWAP'])
tc_s_src             = input.source      (close                        ,'MA Slow   '                      ,inline='10'        ,group=tc_set   ,tooltip=tt_src_en)
tc_s_len             = input.int         (55                           ,''                                ,inline='10'        ,group=tc_set   ,minval=1)
tc_s_type            = input.string      ('SMA'                        ,''                                ,inline='10'        ,group=tc_set   ,options=['EMA','SMA','SMMA (RMA)','TMA','WMA','VWMA','ZEMA','TEMA','HMA','EHMA','THMA','DEMA','TEMA2','T3','HEMA','VWAP'])
tc_col_mode          = input.string      ('Theme'                      ,'Colormode [Bull | Bear]     '    ,inline='14'        ,group=tc_set   ,options=['Theme', 'Manual'])
tc_col_bull_i        = input.color       (color.green                ,''                                ,inline='14'        ,group=tc_set)
tc_col_bear_i        = input.color       (color.red                  ,''                                ,inline='14'        ,group=tc_set)
tc_line_plot         = input.bool        (false                        ,'Line [Trans. | Width]    '       ,inline='16'        ,group=tc_set)
tc_line_trans        = input.int         (40                           ,''                                ,inline='16'        ,group=tc_set   ,minval=0, maxval=100, step=1, tooltip=tt_tc_line_en)
tc_line_width        = input.int         (1                            ,''                                ,inline='16'        ,group=tc_set   ,options=[1,2,3,4,5])
tc_band_plot         = input.bool        (true                         ,'Band [Transparency]   '          ,inline='18'        ,group=tc_set)
tc_band_trans        = input.int         (60                           ,''                                ,inline='18'        ,group=tc_set   ,minval=0, maxval=100, step=1)
// Deactivated... I think it doesn't make sense to show different clouds for different timeframes
tc_sync              = true //input.bool        (true                         ,'Sync Mode?     '                ,inline='02'        ,group=tc_set   ,tooltip=tt_mode+tt_sync)

tc_sig_size          = input.string      ('Tiny'                       ,'Signal     '                     ,inline='20'        ,group=tc_set   ,options=['Tiny','Small','Normal','Large','Huge'])
tc_sig_bull          = input.string      ('△'                          ,''                                ,inline='20'        ,group=tc_set   ,options=['▽','△','•','∘','▼','▲','↑','↓','.','⊕','⊖','⊙'])
tc_sig_bear          = input.string      ('▽'                          ,''                                ,inline='20'        ,group=tc_set   ,options=['▽','△','•','∘','▼','▲','↑','↓','.','⊕','⊖','⊙'], tooltip=tt_signal_en)


tc_col_bull          = tc_col_mode == 'Manual' ? tc_col_bull_i : col.bull
tc_col_bear          = tc_col_mode == 'Manual' ? tc_col_bear_i : col.bear

if tc_mode == 'Auto' and tc_preset != 'Vortex'
	[tc_fast_len, tc_fast_type, tc_slow_len, tc_slow_type] = f_getCloudValues( tc_preset )
	tc_f_type  := tc_fast_type
	tc_f_len   := tc_fast_len
	tc_s_type  := tc_slow_type
	tc_s_len   := tc_slow_len

tc_f_out	            = tc_mode == 'Auto' and tc_preset == 'Vortex' ? f_vortex_cloud( 10, tc_sens ) : f_ma(tc_f_type, tc_f_src, tc_f_len)
tc_s_out                = tc_mode == 'Auto' and tc_preset == 'Vortex' ? f_vortex_cloud( 40, tc_sens ) : f_ma(tc_s_type, tc_s_src, tc_s_len)

//f_getTrend() = [t_isBullish, t_isBearish, t_wasBullish, t_wasBearish, t_long, t_short]
//=> Calculations FAST
[tc_f_isBullish, tc_f_isBearish, tc_f_wasBullish, tc_f_wasBearish, tc_f_long, tc_f_short] = f_getTrend(tc_f_out)
tc_f_col                = tc_f_isBullish ? color.new(tc_col_bull, tc_line_trans) : color.new(tc_col_bear, tc_line_trans)

//=> Calculations SLOW
[tc_s_isBullish, tc_s_isBearish, tc_s_wasBullish, tc_s_wasBearish, tc_s_long, tc_s_short] = f_getTrend(tc_s_out)
tc_s_col                = tc_s_isBullish ? color.new(tc_col_bull, tc_line_trans) : color.new(tc_col_bear, tc_line_trans)

//=> Trendcloud
tc_isBullish            = tc_fill == 'Position' ? tc_f_out > tc_s_out                : tc_f_isBullish and tc_s_isBullish
tc_isBearish            = tc_fill == 'Position' ? tc_f_out < tc_s_out                : tc_f_isBearish and tc_s_isBearish
tc_wasBullish           = tc_fill == 'Position' ? ta.crossover(tc_f_out, tc_s_out)   : tc_f_isBullish and tc_s_isBullish and ( tc_f_wasBearish or tc_s_wasBearish )
tc_wasBearish           = tc_fill == 'Position' ? ta.crossunder(tc_f_out, tc_s_out)  : tc_f_isBearish and tc_s_isBearish and ( tc_f_wasBullish or tc_s_wasBullish )

//=> Colors
tc_color                = tc_isBullish ? color.new(tc_col_bull, tc_band_trans) : tc_isBearish ? color.new(tc_col_bear, tc_band_trans) : color.new(col.neutral, tc_band_trans)
tc_barcolor             = tc_color

//=> Trendcloud Signals
tc_long_dir             = tc_f_isBullish and tc_s_isBullish and ( tc_f_wasBearish or tc_s_wasBearish )
tc_short_dir            = tc_f_isBearish and tc_s_isBearish and ( tc_f_wasBullish or tc_s_wasBullish )
tc_long_ord             = ta.crossover   (tc_f_out, tc_s_out)
tc_short_ord            = ta.crossunder  (tc_f_out, tc_s_out)
tc_long                 = tc_fill == 'Position' ? tc_long_ord  : tc_long_dir
tc_short                = tc_fill == 'Position' ? tc_short_ord : tc_short_dir

//=> Plots
plot(show.tc ? tc_f_out :na, title='TrendCloud Fast Line', color=tc_f_col, linewidth=tc_line_width, display=tc_line_plot ? display.all : display.none)	
plot(show.tc ? tc_s_out :na, title='TrendCloud Slow Line', color=tc_s_col, linewidth=tc_line_width, display=tc_line_plot ? display.all : display.none)	

fill(plot(show.tc and tc_band_plot ? tc_f_out : na, '', na, editable=false), plot(show.tc and tc_band_plot ? tc_s_out : na, '', na, editable=false), tc_color, 'Trendcloud Fill')

if show.tc and show.tcs and tc_long
	label.new(bar_index  ,low  ,tc_sig_bull  ,color=col.trans  ,style=label.style_label_up    ,textcolor=tc_col_bull  ,size=f_getSize(tc_sig_size), tooltip='TrendCloud Long')
if show.tc and show.tcs and tc_short
	label.new(bar_index  ,high ,tc_sig_bear  ,color=col.trans  ,style=label.style_label_down  ,textcolor=tc_col_bear  ,size=f_getSize(tc_sig_size), tooltip='TrendCloud Short')





























































//+--- Line 800 ----------------------------------------------------------------------------------------------------------------------------------------------------------+//
//+---  ██   ██ ████████ ███████     ████████ ██████  ███████ ███    ██ ██████   ██████ ██       ██████  ██    ██ ██████  
//+---  ██   ██    ██    ██             ██    ██   ██ ██      ████   ██ ██   ██ ██      ██      ██    ██ ██    ██ ██   ██ 
//+---  ███████    ██    █████          ██    ██████  █████   ██ ██  ██ ██   ██ ██      ██      ██    ██ ██    ██ ██   ██ 
//+---  ██   ██    ██    ██             ██    ██   ██ ██      ██  ██ ██ ██   ██ ██      ██      ██    ██ ██    ██ ██   ██ 
//+---  ██   ██    ██    ██             ██    ██   ██ ███████ ██   ████ ██████   ██████ ███████  ██████   ██████  ██████  
//+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------+//
//+---  HTF TrendCloud [HTC]   
//+---                input              def_val                       title                              inline             group            opt/tooltips/etc.        ---+//
//+------------------+-------------------+-----------------------------+----------------------------------+------------------+----------------+---------------------------+//
htc_set              = '🔸🔸🔸 ② HTF TRENDCLOUD [HTC]'

htc_tf_auto          = input.bool        (true                         ,'Auto   '                         ,inline='00'       ,group=htc_set   ,tooltip=tt_auto_en)
htc_tf_autotype      = input.string      ('HTF 1'                      ,'TF Auto | Manual'                ,inline='00'       ,group=htc_set   ,options=['HTF 1', 'HTF 2'])
htc_tf               = input.timeframe   (''                           ,''                                ,inline='00'       ,group=htc_set   ,tooltip=tt_htf + tt_lb + tt_htf_tip_en)
htc_fill             = input.string      ('Direction'                  ,'Fillmode                '        ,inline='04'       ,group=htc_set   ,options=['Position', 'Direction'] ,tooltip=tt_fill_en)
htc_tf_smooth        = input.bool        (true                         ,'Smooth'                          ,inline='04'       ,group=htc_set)

htc_col_mode         = input.string      ('Theme'                      ,'Colormode [Bull | Bear]     '    ,inline='10'       ,group=htc_set   ,options=['Theme', 'Manual'])
htc_col_bull_i       = input.color       (color.green                ,''                                ,inline='10'       ,group=htc_set)
htc_col_bear_i       = input.color       (color.red                  ,''                                ,inline='10'       ,group=htc_set) 
htc_line_plot        = input.bool        (false                        ,'Line [Trans. | Width]    '       ,inline='12'       ,group=htc_set)
htc_line_trans       = input.int         (40                           ,''                                ,inline='12'       ,group=htc_set   ,minval=0, maxval=100, step=1)
htc_line_width       = input.int         (1                            ,''                                ,inline='12'       ,group=htc_set   ,options=[1,2,3,4,5], tooltip=tt_tc_line_en)
htc_band_plot        = input.bool        (true                         ,'Band [Transparency]   '          ,inline='14'       ,group=htc_set)
htc_band_trans       = input.int         (60                           ,''                                ,inline='14'       ,group=htc_set   ,minval=0, maxval=100, step=1)

htc_sig_size         = input.string      ('Tiny'                       ,'Signal     '                     ,inline='20'       ,group=htc_set   ,options=['Tiny','Small','Normal','Large','Huge'])
htc_sig_bull         = input.string      ('▲'                          ,''                                ,inline='20'       ,group=htc_set   ,options=['▽','△','•','∘','▼','▲','↑','↓','.','⊕','⊖','⊙'])
htc_sig_bear         = input.string      ('▼'                          ,''                                ,inline='20'       ,group=htc_set   ,options=['▽','△','•','∘','▼','▲','↑','↓','.','⊕','⊖','⊙'], tooltip=tt_signal)


htc_col_bull         = htc_col_mode == 'Manual' ? htc_col_bull_i : col.bull
htc_col_bear         = htc_col_mode == 'Manual' ? htc_col_bear_i : col.bear




[htf1, htf2]         = f_getAutoTimeframes ( timeframe.period ) 
htc_tf_mode          = htc_tf_auto ? 'Auto' : 'Manual'
htc_tf_val           = htc_tf_auto and htc_tf_autotype == 'HTF 1' ? htf1 : htc_tf_auto and htc_tf_autotype == 'HTF 2' ? htf2 : htc_tf 

htc_c_seconds        = timeframe.in_seconds(timeframe.period)
htc_htf_seconds      = timeframe.in_seconds(htc_tf_val) 
htc_smooth_factor    = math.round(htc_htf_seconds / htc_c_seconds)

htc_fast_val         = tc_f_out
htc_slow_val         = tc_s_out
htc_fast_tmp         = f_SecurityNoRepaint(htc_tf_val, htc_fast_val, barmerge.gaps_off)
htc_slow_tmp         = f_SecurityNoRepaint(htc_tf_val, htc_slow_val, barmerge.gaps_off)
htc_f_out            = htc_tf_smooth ? ta.sma(htc_fast_tmp, htc_smooth_factor) : htc_fast_tmp
htc_s_out            = htc_tf_smooth ? ta.sma(htc_slow_tmp, htc_smooth_factor) : htc_slow_tmp


// Trend fast
[htc_f_isBullish, htc_f_isBearish, htc_f_wasBullish, htc_f_wasBearish, htc_f_long, htc_f_short] = f_getTrend(htc_f_out)

htc_f_col            = htc_f_isBullish ? htc_col_bull : htc_col_bear
htc_f_txt_col        = htc_f_isBullish ? htc_col_bull : htc_col_bear

// Trend slow
[htc_s_isBullish, htc_s_isBearish, htc_s_wasBullish, htc_s_wasBearish, htc_s_long, htc_s_short] = f_getTrend(htc_s_out)

htc_s_col            = htc_s_isBullish ? htc_col_bull : htc_col_bear
htc_s_txt_col        = htc_s_isBullish ? htc_col_bull : htc_col_bear


//=> Trendcloud
htc_isBullish       = htc_fill == 'Position' ? htc_f_out > htc_s_out : htc_f_isBullish and htc_s_isBullish
htc_isBearish       = htc_fill == 'Position' ? htc_f_out < htc_s_out : htc_f_isBearish and htc_s_isBearish
htc_wasBullish      = htc_fill == 'Position' ? ta.crossover(htc_f_out, htc_s_out) : htc_f_isBullish and htc_s_isBullish and ( htc_f_wasBearish or htc_s_wasBearish )
htc_wasBearish      = htc_fill == 'Position' ? ta.crossunder(htc_f_out, htc_s_out) : htc_f_isBearish and htc_s_isBearish and ( htc_f_wasBullish or htc_s_wasBullish )

//=> Colors
htc_color           = htc_isBullish ? color.new(htc_col_bull, htc_band_trans) : htc_isBearish ? color.new(htc_col_bear, htc_band_trans) : color.new(col.neutral, htc_band_trans)
htc_barcolor        = htc_color

//=> Trendcloud Signals
htc_long_dir        = htc_f_isBullish and htc_s_isBullish and ( htc_f_wasBearish or htc_s_wasBearish )
htc_short_dir       = htc_f_isBearish and htc_s_isBearish and ( htc_f_wasBullish or htc_s_wasBullish )
htc_long_ord        = ta.crossover(htc_f_out, htc_s_out)
htc_short_ord       = ta.crossunder(htc_f_out, htc_s_out)
htc_long            = htc_fill == 'Position' ? htc_long_ord : htc_long_dir
htc_short           = htc_fill == 'Position' ? htc_short_ord : htc_short_dir

//=> Plots
plot(show.htc ? htc_f_out :na, title='TrendCloud Fasttrend', color=htc_f_col, linewidth=htc_line_width, display=htc_line_plot ? display.all : display.none)	
plot(show.htc ? htc_s_out :na, title='TrendCloud Slowtrend', color=htc_s_col, linewidth=htc_line_width, display=htc_line_plot ? display.all : display.none)			

fill(plot(show.htc and htc_band_plot ? htc_f_out : na, '', na, editable=false), plot(show.htc and htc_band_plot ? htc_s_out : na, '', na, editable=false), htc_color, 'Trendcloud Fill Color')

if show.htc and show.htcs and htc_long
	label.new(bar_index  ,low  ,htc_sig_bull  ,color=col.trans  ,style=label.style_label_up    ,textcolor=htc_col_bull  ,size=f_getSize(htc_sig_size), tooltip='HTF TrendCloud Long')
if show.htc and show.htcs and htc_short
	label.new(bar_index  ,high ,htc_sig_bear  ,color=col.trans  ,style=label.style_label_down  ,textcolor=htc_col_bear  ,size=f_getSize(htc_sig_size), tooltip='HTF TrendCloud Long')























































//+--- Line 950 ----------------------------------------------------------------------------------------------------------------------------------------------------------+//
//+---  ██ ███    ██ ███████  ██████  ██████   ██████   █████  ██████  ██████  
//+---  ██ ████   ██ ██      ██    ██ ██   ██ ██    ██ ██   ██ ██   ██ ██   ██ 
//+---  ██ ██ ██  ██ █████   ██    ██ ██████  ██    ██ ███████ ██████  ██   ██ 
//+---  ██ ██  ██ ██ ██      ██    ██ ██   ██ ██    ██ ██   ██ ██   ██ ██   ██ 
//+---  ██ ██   ████ ██       ██████  ██████   ██████  ██   ██ ██   ██ ██████ 
//+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------+//
//+---  InfoBoard [IB]                                                                                                                                                 ---+//
//+---                input              def_val                       title                              inline             group            opt/tooltips/etc.        ---+//
//+------------------+-------------------+-----------------------------+----------------------------------+------------------+----------------+---------------------------+//
ib_set               = '🔸🔸🔸 ③ INFOBOARD [IB]'
// SYMBOL INFO
ib_col_frame         = input.color       (#000000                    ,'Border | Text | Backgrounf'      ,inline='00'       ,group=ib_set)
ib_col_txt           = input.color       (#000000                    ,''                                ,inline='00'       ,group=ib_set)
ib_col_bg            = input.color       (#d1d4dc                    ,''                                ,inline='00'       ,group=ib_set)
ib_y2                = input.string      ('Bottom'                     ,'Position'                        ,inline='02'       ,group=ib_set    ,options=['Top','Center','Bottom'])
ib_x2                = input.string      ('Center'                     ,''                                ,inline='02'       ,group=ib_set    ,options=['Left','Center','Right'])
ib_size              = input.string      ('Tiny'                       ,'Size'                            ,inline='04'       ,group=ib_set    ,options=['Tiny','Small','Normal','Large','Huge'])
ib_showBorder        = input.bool        (false                        ,'Hide Border?'                    ,inline='04'       ,group=ib_set)
ib_showTXT           = input.bool        (true                         ,'Systeminfo'                      ,inline='06'       ,group=ib_set)
ib_showCT            = input.bool        (true                         ,'Cloud Type'                      ,inline='06'       ,group=ib_set)
ib_showTF            = input.bool        (true                         ,'HTF Info'                        ,inline='06'       ,group=ib_set)
ib_showDate          = input.bool        (true                         ,'Date/Time'                       ,inline='06'       ,group=ib_set)

ib_position          = f_getPosition(ib_y2+' '+ib_x2)

//==> PLOT
ib_table             = table.new(ib_position, 4, 4, frame_color = ib_col_frame, frame_width = ib_showBorder ? 0 : 1)
if show.ib
	if ib_showTXT
		table.cell(ib_table    ,0   ,0   ,sys.info      ,text_color=ib_col_txt, text_halign='center', text_size=f_getSize(ib_size), bgcolor=ib_col_bg, text_font_family=sys.font)
		table.merge_cells(ib_table, 0, 0, 3, 0)
  
	if ib_showCT or ib_showTF
		txt_div     = ' | '                          // Divider
		txt_sp      = ' '                            // Space 
		txt_sl      = '/'                            // Slash

		txt_htf     = f_getTF_label(htc_tf_val) + txt_sp + (htc_tf_auto ? 'Auto' : 'Man')
		txt_ct_pre  = tc_preset + txt_sp + tc_mode                                       
		txt_ct_man  = tc_f_type + txt_sp + str.tostring(tc_f_len) + txt_sl + tc_s_type + txt_sp + str.tostring(tc_s_len) + txt_sp + tc_mode                                               
		
		// CloudType
		txt_ct      = ib_showCT ? tc_mode == 'Auto' ? txt_ct_pre : txt_ct_man : ''
		 // Higher Timeframe
		txt_tf      = ib_showTF ? txt_htf : ''
	
		txt         = txt_ct + (ib_showCT and ib_showTF ? txt_div : '') + txt_tf
		
		table.cell(ib_table    ,0   ,1   ,txt     ,text_color=ib_col_txt, text_halign='center', text_size=f_getSize(ib_size), bgcolor=ib_col_bg, text_font_family = sys.font)
		table.merge_cells(ib_table, 0, 1, 3, 1)
  
	if ib_showDate
		table.cell(ib_table, 0, 2, sys.datetime,          text_color=ib_col_txt, text_halign='center', text_size=f_getSize(ib_size),       bgcolor=ib_col_bg, text_font_family = sys.font)
		table.merge_cells(ib_table, 0, 2, 3, 2)













































//+--- Line 1050 --------------------------------------------------------------------------------------------------------------------------------------------------------+//
//+---  █████  ██      ███████ ██████  ████████ ███████ 
//+---  ██   ██ ██      ██      ██   ██    ██    ██      
//+---  ███████ ██      █████   ██████     ██    ███████ 
//+---  ██   ██ ██      ██      ██   ██    ██         ██ 
//+---  ██   ██ ███████ ███████ ██   ██    ██    ███████
//+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------+//
//+--- ALERTS                                                                                                                                                          ---+//
//+---                input              def_val                       title                              inline             group            opt/tooltips/etc.        ---+//
//+------------------+-------------------+-----------------------------+----------------------------------+------------------+----------------+---------------------------+//
al_group             = '🔸🔸🔸 ALERTS' 
//                   1 x pro Balken / 1 x per bar                      = alert.freq_once_per_bar
//                   1 x pro Balkenschluss / 1 x per bar clos          = alert.freq_once_per_bar_close
al_mode_i            = input.string      ('1 x per bar close'          ,'Alert Mode'                                         ,group=al_group  ,options= ['1 x per bar close', '1 x per bar'], tooltip=tt_alert_en)
al_mode              = al_mode_i == '1 x per bar' ? alert.freq_once_per_bar : alert.freq_once_per_bar_close

TCL                  =  input.bool       (false                        , 'TrendCloud Long'                ,inline='00'       ,group=al_group)
TCS                  =  input.bool       (false                        , 'TrendCloud Long'                ,inline='00'       ,group=al_group)
HTCL                 =  input.bool       (false                        , 'HTF TrendCloud Long'            ,inline='02'       ,group=al_group)
HTCS                 =  input.bool       (false                        , 'HTF TrendCloud Long'            ,inline='02'       ,group=al_group)


f_Alert              (tc_long            and TCL                       ,'TrendCloud Long → '              ,al_mode           ,lang='en')
f_Alert              (tc_short           and TCS                       ,'TrendCloud Short → '             ,al_mode           ,lang='en')
f_Alert              (htc_long           and HTCL                      ,'HTF TrendCloud Long → '          ,al_mode           ,lang='en')
f_Alert              (htc_short          and HTCS                      ,'HTF TrendCloud Short → '         ,al_mode           ,lang='en')
