//@version=6
indicator("Scope Test for Global Variables (Final Corrected)", overlay=true)

// --- Global Variable Declaration ---
// 'var' declares variables that persist across bars.
var bool is_active = false
var int counter = 0

// --- Function to Calculate New State ---
// This function is "pure". It takes the current state as input and returns the new state.
calculateNewState(current_is_active, current_counter) =>
    new_is_active = not current_is_active
    new_counter = current_counter + 1
    [new_is_active, new_counter] // Return the new state as a tuple


// --- Execution Logic ---
// On every bar where the close is greater than the open, we'll update our state.
if close > open
    // 1. Call the function and store the returned values in a temporary tuple.
    // This is a standard variable declaration, so we use '='.
    [temp_is_active, temp_counter] = calculateNewState(is_active, counter)

    // 2. Individually reassign the global 'var' variables using the values
    // from the temporary tuple. This is the correct v6 pattern for updating
    // global state from a function's multi-value return.
    is_active := temp_is_active
    counter := temp_counter


// --- Visualization ---
// Display the current state of the global variables on the chart.
if barstate.islast
    label.new(bar_index, high, 
     "Global State:\n" + 
     "is_active = " + str.tostring(is_active) + "\n" +
     "counter = " + str.tostring(counter),
     yloc = yloc.abovebar, style = label.style_label_left)
