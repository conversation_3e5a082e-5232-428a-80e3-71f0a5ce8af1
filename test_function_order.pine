//@version=6
indicator("Function Order Test", overlay=true)

// --- User-Defined Type ---
type TestType
    int value

// --- INCORRECT IMPLEMENTATION (Causes 'Undeclared Identifier' Error) ---
// This section is commented out to allow the script to compile.
// To see the error, uncomment the block below and comment out the 'CORRECT IMPLEMENTATION' block.

// // 1. Array is created
// var testArray_error = array.new<TestType>()
// array.push(testArray_error, TestType.new(10))
// array.push(testArray_error, TestType.new(5))
// 
// // 2. Attempt to sort the array by calling a function that has not been defined yet.
// // This will cause a compilation error: "Undeclared identifier 'comparisonFunction_error'"
// testArray_error.sort(comparisonFunction_error)
// 
// // 3. The function definition appears AFTER the call.
// comparisonFunction_error(TestType a, TestType b) =>
//     a.value > b.value ? 1 : -1
// 
// // 4. Draw label to show result (this code will not be reached)
// if barstate.islast
//     label.new(bar_index, high, "Error version would not compile.")


// --- CORRECT IMPLEMENTATION (v6 Compliant) ---

// 1. The comparison function is defined BEFORE it is called.
correctComparisonFunction(TestType a, TestType b) =>
    // Sorting logic: 1 for a > b, -1 for a < b, 0 for equal
    // Note: For simple property sorting, array.sort_by() is more efficient.
    // This method is for demonstrating function order with custom logic.
    if a.value > b.value
        1
    else if a.value < b.value
        -1
    else
        0

// 2. Array is created
var testArray_correct = array.new<TestType>()
if barstate.isfirst
    array.push(testArray_correct, TestType.new(25))
    array.push(testArray_correct, TestType.new(10))
    array.push(testArray_correct, TestType.new(15))


// 3. The array is sorted using the pre-defined function. This will compile successfully.
testArray_correct.sort(correctComparisonFunction)


// 4. Draw labels to show the sorted result on the last bar.
if barstate.islast
    label.new(bar_index, high, "Correct Version Compiled!\nThis demonstrates defining a function before calling it.\n\nSorted Values:\n" + str.tostring(testArray_correct.get(0).value) + "\n" + str.tostring(testArray_correct.get(1).value) + "\n" + str.tostring(testArray_correct.get(2).value), yloc=yloc.abovebar)
