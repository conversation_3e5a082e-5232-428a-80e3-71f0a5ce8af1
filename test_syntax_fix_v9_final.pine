//@version=6
indicator("Function Syntax Test v9 Final", overlay=true)

// This is a final attempt to fix the test script.
// The `barstate` variable may have been moved to the `chart` object in v6.
// This script tests `chart.islastconfirmed` instead of `barstate.islastconfirmed`.

// --- Test Function with correct v6 direct declaration syntax ---
testFunction(int inputNumber) =>
    prefix = "Input was: "
    result = prefix + str.tostring(inputNumber)
    result // Implicit return

// --- Main Logic ---
plot(close) // Provide context for the compiler

if chart.islastconfirmed
    // Call the function.
    string result = testFunction(123)
    
    // This must compile.
    label.new(bar_index, high, result, color=color.green, textcolor=color.white)
