//@version=6
indicator("Sort Test for SnrLevel (v6 - Final)", overlay=true)

// 1. EXACT SnrLevel TYPE DEFINITION
type SnrLevel
    float price
    string timeframeSource

// 2. CORRECTED v6 COMPARISON FUNCTION
// The function must be a standard, globally-scoped function, NOT a method.
priceCompareFunction(SnrLevel a, SnrLevel b) =>
    if a.price < b.price
        -1
    else if a.price > b.price
        1
    else
        0

// 3. SETUP TEST ARRAY
var testLevels = array.new<SnrLevel>()
if barstate.isfirst
    array.push(testLevels, SnrLevel.new(1.2, "Chart"))
    array.push(testLevels, SnrLevel.new(1.1, "HTF1"))
    array.push(testLevels, SnrLevel.new(1.3, "HTF2"))

// 4. ATTEMPT TO SORT (Corrected for v6)
if array.size(testLevels) > 0
    // Correct v6 syntax: call the sort method on the array object
    // and pass the globally-scoped comparison function as an argument.
    testLevels.sort(priceCompareFunction)

// 5. VISUALIZE OUTPUT
if barstate.islast
    for level in testLevels
        label.new(bar_index, level.price, text=level.timeframeSource + " @ " + str.tostring(level.price))
