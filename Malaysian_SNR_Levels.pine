//@version=5
// Title: Malaysian SNR Levels + MTF + Freshness Indicator (Historical Scan v5)
// Description: Detects Malaysian SNR levels (A, V, Gaps) on the current chart and up to two higher timeframes (HTFs).
//              HTF detection now scans historical HTF data to find older levels.
//              Tracks 'Freshness' state based on wick touches on the current chart.
//              Visually distinguishes levels from different timeframes.
// Features:
// - Detects A-Levels, V-Levels, Gap Levels based on Open/Close prices.
// - Supports levels from Chart TF, HTF1, and HTF2.
// - HTF detection scans N past bars for patterns.
// - Tracks Fresh/Unfresh status based on wick touches on the current chart.
// - Customizable lookback period for level display duration.
// - Customizable historical scan depth for HTF detection.
// - Customizable colors and line styles per timeframe.
// - Option to hide unfresh levels.
// - Robust loop handling for array management.
// - Corrected indentation and related syntax.

indicator("Malaysian SNR Levels + MTF + Freshness", overlay=true, max_lines_count=500, max_boxes_count = 0, max_labels_count = 500) // Optimize drawing objects

// --- Inputs ---
// General Settings
string grpGen = "General Settings"
int lookbackPeriod = input.int(300, "Level Display Lookback (Chart Bars)", minval=10, tooltip="How many current chart bars back to keep displaying detected levels.", group=grpGen)
int htfScanDepth = input.int(50, "HTF Historical Scan Depth (HTF Bars)", minval=2, maxval=500, tooltip="How many past HTF bars to scan for SNR patterns when a new HTF bar forms. Higher values may impact performance.", group=grpGen)
bool showUnfreshLevels = input.bool(true, "Show Unfresh Levels", tooltip="If unchecked, lines disappear once touched by a wick.", group=grpGen)
color colUnfresh = input.color(color.new(color.gray, 30), "Unfresh Color", group=grpGen)

// Chart Timeframe Settings
string grpChart = "Chart Timeframe Levels"
bool showChartLevels = input.bool(true, "Show Chart TF Levels", group=grpChart)
string showChartA_str = input.string("Show", "A-Level", options=["Show", "Hide"], inline="ChartA", group=grpChart)
color colChartFreshA = input.color(color.new(color.red, 0), "", inline="ChartA", group=grpChart)
string showChartV_str = input.string("Show", "V-Level", options=["Show", "Hide"], inline="ChartV", group=grpChart)
color colChartFreshV = input.color(color.new(color.green, 0), "", inline="ChartV", group=grpChart)
string showChartBG_str = input.string("Show", "Bullish Gap", options=["Show", "Hide"], inline="ChartBG", group=grpChart)
color colChartFreshBG = input.color(color.new(color.blue, 0), "", inline="ChartBG", group=grpChart)
string showChartXG_str = input.string("Show", "Bearish Gap", options=["Show", "Hide"], inline="ChartXG", group=grpChart)
color colChartFreshXG = input.color(color.new(color.purple, 0), "", inline="ChartXG", group=grpChart)
int widthChart = input.int(1, "Line Width", minval=1, maxval=5, group=grpChart)

// Higher Timeframe 1 Settings
string grpHTF1 = "Higher Timeframe 1 (HTF1) Levels"
bool showHTF1Levels = input.bool(true, "Show HTF1 Levels", group=grpHTF1)
string htf1 = input.string("D", "Timeframe", options=["1", "5", "15", "60", "240", "D", "W"], group=grpHTF1, tooltip="Select a higher timeframe. '60' = 1H, '240' = 4H, 'D' = Daily, 'W' = Weekly.")
string showHTF1A_str = input.string("Show", "A-Level", options=["Show", "Hide"], inline="HTF1A", group=grpHTF1)
color colHTF1FreshA = input.color(color.new(color.maroon, 0), "", inline="HTF1A", group=grpHTF1)
string showHTF1V_str = input.string("Show", "V-Level", options=["Show", "Hide"], inline="HTF1V", group=grpHTF1)
color colHTF1FreshV = input.color(color.new(color.olive, 0), "", inline="HTF1V", group=grpHTF1)
string showHTF1BG_str = input.string("Show", "Bullish Gap", options=["Show", "Hide"], inline="HTF1BG", group=grpHTF1)
color colHTF1FreshBG = input.color(color.new(color.navy, 0), "", inline="HTF1BG", group=grpHTF1)
string showHTF1XG_str = input.string("Show", "Bearish Gap", options=["Show", "Hide"], inline="HTF1XG", group=grpHTF1)
color colHTF1FreshXG = input.color(color.new(color.fuchsia, 0), "", inline="HTF1XG", group=grpHTF1)
int widthHTF1 = input.int(2, "Line Width", minval=1, maxval=5, group=grpHTF1)

// Higher Timeframe 2 Settings
string grpHTF2 = "Higher Timeframe 2 (HTF2) Levels"
bool showHTF2Levels = input.bool(true, "Show HTF2 Levels", group=grpHTF2)
string htf2 = input.string("W", "Timeframe", options=["1", "5", "15", "60", "240", "D", "W"], group=grpHTF2, tooltip="Select a higher timeframe. '60' = 1H, '240' = 4H, 'D' = Daily, 'W' = Weekly.")
string showHTF2A_str = input.string("Show", "A-Level", options=["Show", "Hide"], inline="HTF2A", group=grpHTF2)
color colHTF2FreshA = input.color(color.new(color.orange, 0), "", inline="HTF2A", group=grpHTF2)
string showHTF2V_str = input.string("Show", "V-Level", options=["Show", "Hide"], inline="HTF2V", group=grpHTF2)
color colHTF2FreshV = input.color(color.new(color.lime, 0), "", inline="HTF2V", group=grpHTF2)
string showHTF2BG_str = input.string("Show", "Bullish Gap", options=["Show", "Hide"], inline="HTF2BG", group=grpHTF2)
color colHTF2FreshBG = input.color(color.new(color.teal, 0), "", inline="HTF2BG", group=grpHTF2)
string showHTF2XG_str = input.string("Show", "Bearish Gap", options=["Show", "Hide"], inline="HTF2XG", group=grpHTF2)
color colHTF2FreshXG = input.color(color.new(color.aqua, 0), "", inline="HTF2XG", group=grpHTF2)
int widthHTF2 = input.int(3, "Line Width", minval=1, maxval=5, group=grpHTF2)

// --- Boolean Conversion ---
// Convert the string dropdown selections to booleans for use in the script logic.
bool showChartA = showChartA_str == "Show"
bool showChartV = showChartV_str == "Show"
bool showChartBG = showChartBG_str == "Show"
bool showChartXG = showChartXG_str == "Show"

bool showHTF1A = showHTF1A_str == "Show"
bool showHTF1V = showHTF1V_str == "Show"
bool showHTF1BG = showHTF1BG_str == "Show"
bool showHTF1XG = showHTF1XG_str == "Show"

bool showHTF2A = showHTF2A_str == "Show"
bool showHTF2V = showHTF2V_str == "Show"
bool showHTF2BG = showHTF2BG_str == "Show"
bool showHTF2XG = showHTF2XG_str == "Show"

// --- Constants & UDT ---
// Level Types
string LEVEL_A = "A"
string LEVEL_V = "V"
string LEVEL_BG = "BG"
string LEVEL_XG = "XG"

// Timeframe Source Identifiers
string TF_CHART = "Chart"
string TF_HTF1 = "HTF1"
string TF_HTF2 = "HTF2"

// Structure to hold all info about a single SNR level
type SnrLevel
    float price
    int barIndex
    string levelType
    bool isFresh
    line levelLineID
    label levelLabelID
    string timeframeSource
    int sourceBarIndex

// --- Global State ---
// Use 'var' to make arrays persistent across bars.
var SnrLevel[] aLevels = array.new<SnrLevel>()
var SnrLevel[] vLevels = array.new<SnrLevel>()
var SnrLevel[] bullGapLevels = array.new<SnrLevel>()
var SnrLevel[] bearGapLevels = array.new<SnrLevel>()

// --- Helper Functions ---
// Returns the appropriate line style based on freshness
getLineStyle(bool isFresh) =>
    isFresh ? line.style_solid : line.style_dashed

// Determines the correct fresh color based on level type and source timeframe
getFreshColor(string levelType, string timeframeSource) =>
    color result = na
    if timeframeSource == TF_CHART
        result := switch levelType
            LEVEL_A => colChartFreshA
            LEVEL_V => colChartFreshV
            LEVEL_BG => colChartFreshBG
            LEVEL_XG => colChartFreshXG
            => color.gray // Default/fallback
    else if timeframeSource == TF_HTF1
        result := switch levelType
            LEVEL_A => colHTF1FreshA
            LEVEL_V => colHTF1FreshV
            LEVEL_BG => colHTF1FreshBG
            LEVEL_XG => colHTF1FreshXG
            => color.gray
    else if timeframeSource == TF_HTF2
        result := switch levelType
            LEVEL_A => colHTF2FreshA
            LEVEL_V => colHTF2FreshV
            LEVEL_BG => colHTF2FreshBG
            LEVEL_XG => colHTF2FreshXG
            => color.gray
    result

// Determines the correct line width based on source timeframe
getLineWidth(string timeframeSource) =>
    width = timeframeSource == TF_CHART ? widthChart :
      timeframeSource == TF_HTF1 ? widthHTF1 :
      timeframeSource == TF_HTF2 ? widthHTF2 :
      1 // Default width
    width

// --- Label Helpers ---
// Translates level type to a display string (S/R/Gap)
getLevelTypeAbbreviation(string levelType) =>
    switch levelType
        LEVEL_A => "R"
        LEVEL_V => "S"
        LEVEL_BG => "Bullish Gap"
        LEVEL_XG => "Bearish Gap"
        => "?"

// Creates the full label text
getLabelText(string levelType, string timeframeSource) =>
    string tfString = timeframeSource == TF_CHART ? "Chart" : timeframeSource
    string levelAbbr = getLevelTypeAbbreviation(levelType)
    tfString + " " + levelAbbr

// Gets the bar index for the end of the current trading day based on the length of the previous day.
getEndOfDayBarIndex(int bars_in_day, int current_day_start_bindex) =>
    // Estimate the end of the current day's bar index by projecting from the start of the day.
    current_day_start_bindex + bars_in_day - 1

// --- Fetch HTF Historical Data Arrays ---
// Function to request historical data arrays for a given timeframe
// Returns tuple: [series<float> opens, series<float> closes, series<int> bar_indices]
getHistoricalData(string tf) =>
    htf_open = request.security(syminfo.tickerid, tf, open, lookahead=barmerge.lookahead_off)
    htf_close = request.security(syminfo.tickerid, tf, close, lookahead=barmerge.lookahead_off)
    htf_bindex = request.security(syminfo.tickerid, tf, bar_index, lookahead=barmerge.lookahead_off)
    [htf_open, htf_close, htf_bindex]

// --- Core Logic (runs on every bar) ---

// ① --- Level Detection ---

// Function to check if a level already exists in a given array
levelExists(SnrLevel[] arr, float price, string tfSource, int srcBarIndex) =>
    bool exists = false
    if not arr.size() == 0
        // Check the last N levels for performance.
        checkDepth = math.min(arr.size(), 100)
        for i = 1 to checkDepth
            // Safe access to the array
            if arr.size() > i - 1
                level = arr.get(arr.size() - i)
                if level.price == price and level.timeframeSource == tfSource and level.sourceBarIndex == srcBarIndex
                    exists := true
                    break
            else
                break
    exists

// Centralized function to add a new level if it doesn't already exist.
addLevel(SnrLevel[] levelArray, string levelType, float price, string tfSource, int srcBarIndex) =>
    if not levelExists(levelArray, price, tfSource, srcBarIndex)
        levelArray.push(SnrLevel.new(price, bar_index, levelType, true, na, na, tfSource, srcBarIndex))


// Function to scan historical HTF data and add unique levels
scanHTFHistoryAndAddLevels(string tf, int depth, string tfSource, bool enableA, bool enableV, bool enableBG, bool enableXG) =>
    [htf_o, htf_c, htf_bindex] = getHistoricalData(tf)

    // *** CORRECTED: Loop based on 'depth' input, not array size ***
    // Loop backwards through the specified historical depth
    // Start from index 1 (previous bar) up to 'depth' bars ago.
    for i = 1 to depth by 1 // Loop 'depth' times into the past
        // Get data for the two consecutive historical candles using history operator []
        // Compare candle [i+1] (older) with candle [i] (newer)
        float o1 = htf_o[i+1] // Older candle open
        float c1 = htf_c[i+1] // Older candle close
        float o2 = htf_o[i]   // Newer candle open
        float c2 = htf_c[i]   // Newer candle close
        int source_bindex = htf_bindex[i] // Bar index where pattern completed on HTF

        // Check if data is valid (request.security might return na, especially deep in history)
        if na(o1) or na(c1) or na(o2) or na(c2) or na(source_bindex)
            // If data is na deep in history, we can likely stop scanning further back for this TF
            // Or simply continue to check the next pair if it's intermittent
            continue

        // --- Apply Detection Logic ---
        // A-Level: Candle [i+1] bullish, Candle [i] bearish
        if enableA and c1 > o1 and c2 < o2
            addLevel(aLevels, LEVEL_A, math.min(c1, o2), tfSource, source_bindex)

        // V-Level: Candle [i+1] bearish, Candle [i] bullish
        if enableV and c1 < o1 and c2 > o2
            addLevel(vLevels, LEVEL_V, math.max(c1, o2), tfSource, source_bindex)

        // Bullish Gap: Candle [i+1] bullish, Candle [i] bullish, o[i] > c[i+1]
        if enableBG and c1 > o1 and c2 > o2 and o2 > c1
            addLevel(bullGapLevels, LEVEL_BG, c1, tfSource, source_bindex)

        // Bearish Gap: Candle [i+1] bearish, Candle [i] bearish, o[i] < c[i+1]
        if enableXG and c1 < o1 and c2 < o2 and o2 < c1
            addLevel(bearGapLevels, LEVEL_XG, c1, tfSource, source_bindex)

// --- Trigger Detections ---

// Detect for Chart Timeframe
if showChartLevels
    int prevBarIndex = bar_index - 1
    // A-Level
    if showChartA and close[1] > open[1] and close < open
        addLevel(aLevels, LEVEL_A, math.min(close[1], open), TF_CHART, prevBarIndex)
    // V-Level
    if showChartV and close[1] < open[1] and close > open
        addLevel(vLevels, LEVEL_V, math.max(close[1], open), TF_CHART, prevBarIndex)
    // Bullish Gap
    if showChartBG and close[1] > open[1] and close > open and open > close[1]
        addLevel(bullGapLevels, LEVEL_BG, close[1], TF_CHART, prevBarIndex)
    // Bearish Gap
    if showChartXG and close[1] < open[1] and close < open and open < close[1]
        addLevel(bearGapLevels, LEVEL_XG, close[1], TF_CHART, prevBarIndex)


// Scan HTF1 History on new HTF1 bar
if showHTF1Levels and htf1 != "" and ta.change(time(htf1))
    scanHTFHistoryAndAddLevels(htf1, htfScanDepth, TF_HTF1, showHTF1A, showHTF1V, showHTF1BG, showHTF1XG)

// Scan HTF2 History on new HTF2 bar
if showHTF2Levels and htf2 != "" and ta.change(time(htf2))
    scanHTFHistoryAndAddLevels(htf2, htfScanDepth, TF_HTF2, showHTF2A, showHTF2V, showHTF2BG, showHTF2XG)


// ② --- Process Existing Levels (Freshness Check, Pruning, Line Updates) ---

// Function to iterate through an array of levels, check freshness, remove old ones, and manage drawings
processLevelArray(SnrLevel[] levelArray, int endOfDayBarIndex) =>
    // Create a new array to hold the levels that will be kept for the next bar.
    SnrLevel[] keptLevels = array.new<SnrLevel>()

    // Iterate through the current levels.
    for level in levelArray
        bool prune = bar_index - level.barIndex > lookbackPeriod
        bool stateChanged = false

        // Check freshness if the level is still fresh.
        if level.isFresh and bar_index > level.barIndex
            if high >= level.price and low <= level.price
                level.isFresh := false
                stateChanged := true

        // Prune old levels or hide unfresh levels.
        if prune or (not showUnfreshLevels and not level.isFresh)
            if not na(level.levelLineID)
                line.delete(level.levelLineID)
            if not na(level.levelLabelID)
                label.delete(level.levelLabelID)
        else
            // This level will be kept. Update its line and label.
            color freshColor = getFreshColor(level.levelType, level.timeframeSource)
            int lineWidth = getLineWidth(level.timeframeSource)
            color lineColor = level.isFresh ? freshColor : colUnfresh
            color labelTextColor = color.new(lineColor, 0) // Opaque version of the line color
            string lineStyle = getLineStyle(level.isFresh)
            string labelText = getLabelText(level.levelType, level.timeframeSource)
            int x_pos = endOfDayBarIndex // Position all lines to the calculated end of the current day.

            if na(level.levelLineID)
                // Create a new line if it doesn't exist.
                level.levelLineID := line.new(level.barIndex, level.price, x_pos, level.price,
                     extend=extend.none, color=lineColor, style=lineStyle, width=lineWidth)
                // Create a new label.
                level.levelLabelID := label.new(x_pos, level.price, text=labelText,
                     color=color.new(color.white, 100), textcolor=labelTextColor, style=label.style_label_right)
            else
                // Update existing line properties on every bar for robustness.
                line.set_x2(level.levelLineID, x_pos)
                line.set_color(level.levelLineID, lineColor)
                line.set_style(level.levelLineID, lineStyle)
                line.set_width(level.levelLineID, lineWidth)
                // Update existing label properties.
                label.set_xy(level.levelLabelID, x_pos, level.price)
                label.set_textcolor(level.levelLabelID, labelTextColor)

            // Add the updated level to the 'kept' list.
            keptLevels.push(level)

    // Return the new, filtered array.
    keptLevels

// --- Day Tracking for Line Endpoints ---
var int g_last_day_start_bindex = 0
var int g_bars_in_last_day = na

if ta.change(time_tradingday)
    if g_last_day_start_bindex != 0
        g_bars_in_last_day := bar_index - g_last_day_start_bindex
    g_last_day_start_bindex := bar_index

// Estimate the number of bars in a day.
int bars_in_day_est = na
if na(g_bars_in_last_day)
    // Fallback for the first day on the chart: calculate based on bar duration.
    bar_duration_sec = (time - time[1]) / 1000
    // If duration is invalid (e.g., first bar), use a safe default (e.g., 1h bar).
    if na(bar_duration_sec) or bar_duration_sec == 0
        bar_duration_sec := 3600
    bars_in_day_est := int(86400 / bar_duration_sec)
else
    // Use the measured length of the previous day for accuracy.
    bars_in_day_est := g_bars_in_last_day

int endOfDayIndex = getEndOfDayBarIndex(bars_in_day_est, g_last_day_start_bindex)

// Call the processing function for each type of level array, passing the calculated end-of-day index.
aLevels := processLevelArray(aLevels, endOfDayIndex)
vLevels := processLevelArray(vLevels, endOfDayIndex)
bullGapLevels := processLevelArray(bullGapLevels, endOfDayIndex)
bearGapLevels := processLevelArray(bearGapLevels, endOfDayIndex)

// --- End of Script ---
