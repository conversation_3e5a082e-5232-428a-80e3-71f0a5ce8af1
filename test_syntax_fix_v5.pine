//@version=6
indicator("Test Syntax Fix v5", overlay=true)

// This is a diagnostic script to test a new hypothesis.
// The theory is that the Pine Script v6 parser is failing to handle the
// custom user-defined type (UDT) `array<SnrLevel>` in the function signature.
// This version changes the return type to a simple, built-in `string` to check
// if the rest of the function syntax is valid.

// --- Simplified UDT and Constants for testing ---
type SnrLevel
    float price
    string timeframeSource
    string patternType
    string mtfParentInfo

// --- Function with a simple 'string' return type for diagnostics ---
correlateMtfLevels(levels) -> string
    if array.size(levels) < 2
        return "Array size is less than 2"

    float proximity = 1.0 // Dummy value
    for i = 0 to array.size(levels) - 1
        child = array.get(levels, i)
        child.mtfParentInfo := ""
        // The rest of the logic is kept for structural validation,
        // but the final return value is what matters for this test.
    return "OK"

// --- Main script logic to trigger the function ---
if barstate.islastconfirmed
    var SnrLevel[] testLevels = array.new<SnrLevel>()
    array.push(testLevels, SnrLevel.new(100.0, "Chart", "V", ""))
    
    // Call the function and store its string result.
    string result = correlateMtfLevels(testLevels) 

    // If this compiles, a label will appear with the result.
    label.new(bar_index, high, "Diagnostic Result: " + result, color=color.blue, textcolor=color.white)
