' ===================================================================
' ENHANCED UTILITIES TRACKER - COMPREHENSIVE TEST SUITE
' ===================================================================
' Purpose: Verify all functionality of the Enhanced Utilities Tracker
' Tests: Overdue detection, conditional formatting, archive process, integration
' ===================================================================

Option Explicit

Sub RunComprehensiveTestSuite()
    ' Master test function to run all verification tests
    
    Debug.Print String(80, "=")
    Debug.Print "ENHANCED UTILITIES TRACKER - COMPREHENSIVE TEST SUITE"
    Debug.Print "Test Started: " & Format(Now, "MM/DD/YYYY HH:MM:SS")
    Debug.Print String(80, "=")
    
    Dim testsPassed As Long
    Dim testsTotal As Long
    
    ' Test 1: Overdue Detection Logic
    Debug.Print ""
    Debug.Print "TEST 1: OVERDUE DETECTION LOGIC"
    Debug.Print "================================"
    If TestOverdueDetectionLogic() Then
        testsPassed = testsPassed + 1
        Debug.Print "✅ PASSED: Overdue detection logic working correctly"
    Else
        Debug.Print "❌ FAILED: Overdue detection logic has issues"
    End If
    testsTotal = testsTotal + 1
    
    ' Test 2: Conditional Formatting Application
    Debug.Print ""
    Debug.Print "TEST 2: CONDITIONAL FORMATTING APPLICATION"
    Debug.Print "==========================================="
    If TestConditionalFormattingApplication() Then
        testsPassed = testsPassed + 1
        Debug.Print "✅ PASSED: Conditional formatting applied correctly"
    Else
        Debug.Print "❌ FAILED: Conditional formatting has issues"
    End If
    testsTotal = testsTotal + 1
    
    ' Test 3: Manual Date Entry Compatibility
    Debug.Print ""
    Debug.Print "TEST 3: MANUAL DATE ENTRY COMPATIBILITY"
    Debug.Print "========================================"
    If TestManualDateEntryCompatibility() Then
        testsPassed = testsPassed + 1
        Debug.Print "✅ PASSED: Manual date entry works with conditional formatting"
    Else
        Debug.Print "❌ FAILED: Manual date entry compatibility issues"
    End If
    testsTotal = testsTotal + 1
    
    ' Test 4: Archive System Verification
    Debug.Print ""
    Debug.Print "TEST 4: ARCHIVE SYSTEM VERIFICATION"
    Debug.Print "===================================="
    If TestArchiveSystemSetup() Then
        testsPassed = testsPassed + 1
        Debug.Print "✅ PASSED: Archive system setup correctly"
    Else
        Debug.Print "❌ FAILED: Archive system has issues"
    End If
    testsTotal = testsTotal + 1
    
    ' Test 5: Real-World Scenario Testing
    Debug.Print ""
    Debug.Print "TEST 5: REAL-WORLD SCENARIO TESTING"
    Debug.Print "===================================="
    If TestRealWorldScenarios() Then
        testsPassed = testsPassed + 1
        Debug.Print "✅ PASSED: Real-world scenarios work correctly"
    Else
        Debug.Print "❌ FAILED: Real-world scenario issues found"
    End If
    testsTotal = testsTotal + 1
    
    ' Test Summary
    Debug.Print ""
    Debug.Print String(80, "=")
    Debug.Print "TEST SUITE SUMMARY"
    Debug.Print "Tests Passed: " & testsPassed & "/" & testsTotal
    Debug.Print "Success Rate: " & Format((testsPassed / testsTotal) * 100, "0.0") & "%"
    
    If testsPassed = testsTotal Then
        Debug.Print "🎉 ALL TESTS PASSED - SYSTEM FULLY VERIFIED"
    Else
        Debug.Print "⚠️ SOME TESTS FAILED - REVIEW ISSUES ABOVE"
    End If
    
    Debug.Print "Test Completed: " & Format(Now, "MM/DD/YYYY HH:MM:SS")
    Debug.Print String(80, "=")
    
    ' Display results to user
    Dim resultMessage As String
    resultMessage = "Enhanced Utilities Tracker Test Suite Results:" & vbCrLf & vbCrLf & _
                   "Tests Passed: " & testsPassed & "/" & testsTotal & vbCrLf & _
                   "Success Rate: " & Format((testsPassed / testsTotal) * 100, "0.0") & "%" & vbCrLf & vbCrLf
    
    If testsPassed = testsTotal Then
        resultMessage = resultMessage & "🎉 ALL TESTS PASSED!" & vbCrLf & _
                       "Your Enhanced Utilities Tracker is fully verified and ready for production use."
    Else
        resultMessage = resultMessage & "⚠️ Some tests failed." & vbCrLf & _
                       "Check the Immediate window (Ctrl+G) for detailed results."
    End If
    
    MsgBox resultMessage, vbInformation, "Test Suite Results"
End Sub

Function TestOverdueDetectionLogic() As Boolean
    ' Test the overdue detection conditional formatting logic
    On Error GoTo TestError
    
    Debug.Print "Testing overdue detection formula logic..."
    
    Dim wb As Workbook
    Dim ws As Worksheet
    Set wb = ActiveWorkbook
    
    ' Find or create test worksheet
    On Error Resume Next
    Set ws = wb.Worksheets("Main Issue Tracker")
    On Error GoTo TestError
    
    If ws Is Nothing Then
        Debug.Print "❌ Main Issue Tracker worksheet not found"
        TestOverdueDetectionLogic = False
        Exit Function
    End If
    
    ' Test the conditional formatting formula components
    Debug.Print "Verifying overdue detection formula components..."
    
    ' Check if conditional formatting exists
    If ws.Range("A2").FormatConditions.Count > 0 Then
        Debug.Print "✓ Conditional formatting rules found"
        
        ' Look for overdue formatting rule
        Dim i As Long
        Dim foundOverdueRule As Boolean
        For i = 1 To ws.Range("A2").FormatConditions.Count
            If InStr(ws.Range("A2").FormatConditions(i).Formula1, "TODAY()>$L2") > 0 Then
                foundOverdueRule = True
                Debug.Print "✓ Overdue detection rule found: " & ws.Range("A2").FormatConditions(i).Formula1
                Exit For
            End If
        Next i
        
        If foundOverdueRule Then
            Debug.Print "✓ Overdue detection logic verified"
            TestOverdueDetectionLogic = True
        Else
            Debug.Print "❌ Overdue detection rule not found"
            TestOverdueDetectionLogic = False
        End If
    Else
        Debug.Print "❌ No conditional formatting rules found"
        TestOverdueDetectionLogic = False
    End If
    
    Exit Function
    
TestError:
    Debug.Print "❌ Error testing overdue detection: " & Err.Description
    TestOverdueDetectionLogic = False
End Function

Function TestConditionalFormattingApplication() As Boolean
    ' Test that conditional formatting is properly applied
    On Error GoTo TestError
    
    Debug.Print "Testing conditional formatting application..."
    
    Dim wb As Workbook
    Dim ws As Worksheet
    Set wb = ActiveWorkbook
    Set ws = wb.Worksheets("Main Issue Tracker")
    
    ' Test different ranges for conditional formatting
    Dim testRanges As Variant
    testRanges = Array("A2", "J2", "K2", "L2", "T2") ' Key columns to test
    
    Dim rangeIndex As Long
    Dim allRangesHaveFormatting As Boolean
    allRangesHaveFormatting = True
    
    For rangeIndex = 0 To UBound(testRanges)
        Dim testRange As String
        testRange = testRanges(rangeIndex)
        
        If ws.Range(testRange).FormatConditions.Count > 0 Then
            Debug.Print "✓ Conditional formatting found in " & testRange & " (" & ws.Range(testRange).FormatConditions.Count & " rules)"
        Else
            Debug.Print "❌ No conditional formatting in " & testRange
            allRangesHaveFormatting = False
        End If
    Next rangeIndex
    
    ' Test specific color values
    Debug.Print "Verifying specific formatting colors..."
    
    ' Check for overdue formatting color (RGB 255, 199, 206)
    Dim foundOverdueColor As Boolean
    Dim j As Long
    For j = 1 To ws.Range("A2").FormatConditions.Count
        If ws.Range("A2").FormatConditions(j).Interior.Color = RGB(255, 199, 206) Then
            foundOverdueColor = True
            Debug.Print "✓ Overdue color (Light Red) verified: RGB(255, 199, 206)"
            Exit For
        End If
    Next j
    
    If Not foundOverdueColor Then
        Debug.Print "❌ Overdue color not found"
        allRangesHaveFormatting = False
    End If
    
    TestConditionalFormattingApplication = allRangesHaveFormatting
    Exit Function
    
TestError:
    Debug.Print "❌ Error testing conditional formatting: " & Err.Description
    TestConditionalFormattingApplication = False
End Function

Function TestManualDateEntryCompatibility() As Boolean
    ' Test that manual date entry works with conditional formatting
    On Error GoTo TestError
    
    Debug.Print "Testing manual date entry compatibility..."
    
    Dim wb As Workbook
    Dim ws As Worksheet
    Set wb = ActiveWorkbook
    Set ws = wb.Worksheets("Main Issue Tracker")
    
    ' Find first empty row for testing
    Dim testRow As Long
    testRow = ws.Cells(ws.Rows.Count, "C").End(xlUp).Row + 1
    
    Debug.Print "Using test row: " & testRow
    
    ' Test manual date entry
    ws.Cells(testRow, 2).Value = Date ' Column B - Date Logged
    ws.Cells(testRow, 3).Value = "Test Complex" ' Column C - Complex Name
    ws.Cells(testRow, 4).Value = "Test Unit" ' Column D - Unit Number
    ws.Cells(testRow, 10).Value = "In Progress" ' Column J - Status
    ws.Cells(testRow, 12).Value = Date - 5 ' Column L - Target Date (5 days ago - overdue)
    
    Debug.Print "✓ Test data entered in row " & testRow
    
    ' Force calculation to update conditional formatting
    Application.Calculate
    
    ' Check if the row has conditional formatting applied
    If ws.Cells(testRow, 1).FormatConditions.Count > 0 Then
        Debug.Print "✓ Conditional formatting applied to test row"
        
        ' Clean up test data
        ws.Rows(testRow).Delete
        Debug.Print "✓ Test data cleaned up"
        
        TestManualDateEntryCompatibility = True
    Else
        Debug.Print "❌ Conditional formatting not applied to test row"
        
        ' Clean up test data
        ws.Rows(testRow).Delete
        
        TestManualDateEntryCompatibility = False
    End If
    
    Exit Function
    
TestError:
    Debug.Print "❌ Error testing manual date entry: " & Err.Description
    TestManualDateEntryCompatibility = False
End Function

Function TestArchiveSystemSetup() As Boolean
    ' Test that archive system is properly set up
    On Error GoTo TestError
    
    Debug.Print "Testing archive system setup..."
    
    Dim wb As Workbook
    Set wb = ActiveWorkbook
    
    ' Check for Archive Control worksheet
    Dim wsArchive As Worksheet
    On Error Resume Next
    Set wsArchive = wb.Worksheets("Archive Control")
    On Error GoTo TestError
    
    If wsArchive Is Nothing Then
        Debug.Print "❌ Archive Control worksheet not found"
        TestArchiveSystemSetup = False
        Exit Function
    Else
        Debug.Print "✓ Archive Control worksheet found"
    End If
    
    ' Check for key archive elements
    If wsArchive.Range("A1").Value = "MONTHLY ARCHIVE CONTROL" Then
        Debug.Print "✓ Archive Control header verified"
    Else
        Debug.Print "❌ Archive Control header incorrect"
        TestArchiveSystemSetup = False
        Exit Function
    End If
    
    ' Check for archive behavior explanation
    If InStr(wsArchive.Range("A4").Value, "30 days") > 0 Then
        Debug.Print "✓ 30-day archive rule documented"
    Else
        Debug.Print "❌ Archive rule documentation missing"
        TestArchiveSystemSetup = False
        Exit Function
    End If
    
    TestArchiveSystemSetup = True
    Exit Function
    
TestError:
    Debug.Print "❌ Error testing archive system: " & Err.Description
    TestArchiveSystemSetup = False
End Function

Function TestRealWorldScenarios() As Boolean
    ' Test real-world scenarios with sample data
    On Error GoTo TestError
    
    Debug.Print "Testing real-world scenarios..."
    
    Dim wb As Workbook
    Dim ws As Worksheet
    Set wb = ActiveWorkbook
    Set ws = wb.Worksheets("Main Issue Tracker")
    
    ' Count existing data
    Dim existingRows As Long
    existingRows = ws.Cells(ws.Rows.Count, "C").End(xlUp).Row - 1
    Debug.Print "Found " & existingRows & " existing data rows"
    
    ' Test calendar formula functionality
    If ws.Range("S2").HasFormula Then
        Debug.Print "✓ Calendar formula present in Column S"
        
        ' Test formula result
        If Len(ws.Range("S2").Value) > 0 Then
            Debug.Print "✓ Calendar formula producing results: " & Left(ws.Range("S2").Value, 50) & "..."
        Else
            Debug.Print "⚠️ Calendar formula not producing results (may need data)"
        End If
    Else
        Debug.Print "❌ Calendar formula missing in Column S"
        TestRealWorldScenarios = False
        Exit Function
    End If
    
    ' Test consumption analysis formula
    If ws.Range("T2").HasFormula Then
        Debug.Print "✓ Consumption analysis formula present in Column T"
        
        ' Test formula result
        If Len(ws.Range("T2").Value) > 0 Then
            Debug.Print "✓ Consumption analysis producing results: " & ws.Range("T2").Value
        Else
            Debug.Print "⚠️ Consumption analysis not producing results (may need data)"
        End If
    Else
        Debug.Print "❌ Consumption analysis formula missing in Column T"
        TestRealWorldScenarios = False
        Exit Function
    End If
    
    ' Test data validation
    On Error Resume Next
    Dim validationTest As String
    validationTest = ws.Range("E2").Validation.Formula1
    If Err.Number = 0 And Len(validationTest) > 0 Then
        Debug.Print "✓ Data validation working in Column E: " & Left(validationTest, 30) & "..."
    Else
        Debug.Print "❌ Data validation not working in Column E"
        TestRealWorldScenarios = False
        Exit Function
    End If
    On Error GoTo TestError
    
    TestRealWorldScenarios = True
    Exit Function
    
TestError:
    Debug.Print "❌ Error testing real-world scenarios: " & Err.Description
    TestRealWorldScenarios = False
End Function
