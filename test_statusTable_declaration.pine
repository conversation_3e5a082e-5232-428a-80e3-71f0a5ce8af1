//@version=5
indicator("Test Case 2: Table Conditional Declaration ('statusTable')", overlay=true)

// --- Shared Setup ---
// This input will control which code path is executed.
bool shouldCreateTable = input.bool(true, "Should the table be created?")

// The table variable is declared here, but it is NOT initialized yet.
// Its value is 'na' until table.new() is called.
var table statusTable = na


// --- ERROR REPRODUCTION ---
// This block will execute if the input is FALSE.
if (not shouldCreateTable)
    // The code attempts to write to the table here.
    // However, since shouldCreateTable is false, the 'else' block containing
    // table.new() was never executed. 'statusTable' is still 'na'.
    // This will cause a runtime error: "Cannot call 'table.cell' on 'na'."
    table.cell(statusTable, 0, 0, "Error: This will fail", bgcolor=color.red)


// --- CORRECTED LOGIC ---
// This block will execute if the input is TRUE.
if (shouldCreateTable)
    // First, we check if the table has been created. If not, we create it.
    // This is the correct pattern.
    if (na(statusTable))
        statusTable := table.new(position.top_right, 1, 1, border_width=1)
    
    // Now it is safe to write to the table.
    table.cell(statusTable, 0, 0, "Success: Table created", bgcolor=color.green)


// --- VISUAL FEEDBACK ---
// Add a label to the chart to show which path is being tested.
if (shouldCreateTable)
    label.new(bar_index, high, "Testing CORRECT path.\nTable should appear.", color=color.green)
else
    label.new(bar_index, high, "Testing ERROR path.\nCheck console for runtime error.", color=color.red)
