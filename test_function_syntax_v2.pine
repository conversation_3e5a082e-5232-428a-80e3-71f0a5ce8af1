//@version=6
indicator("Function Syntax Test v2", overlay=true)

// This script tests the correct function syntax based on the latest error.
// Hypothesis: Functions are assigned to variables.
// Syntax for multi-line: functionName = (parameters) =>
//     ...indented block...
//     return_value // The last expression is the return value.

// --- Test Function using correct variable assignment syntax ---
testFunction = (int inputNumber) =>
    prefix = "Input was: "
    result = prefix + str.tostring(inputNumber)
    result // Implicit return of the last expression

// --- Main Logic ---
if barstate.islastconfirmed
    // Call the function via the variable it was assigned to.
    string result = testFunction(123)
    
    // If this compiles, the syntax is finally correct.
    label.new(bar_index, high, result, color=color.green, textcolor=color.white)
