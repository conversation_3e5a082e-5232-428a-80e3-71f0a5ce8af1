**Performance Optimization & Storyline Architecture Report**

**1. Performance Optimization**

*   **ATR Calculation**: The repetitive `ta.atr()` calls have been extracted to the global scope, ensuring that the ATR value is calculated only once per bar. This significantly reduces the computational overhead and improves the script's loading time.
*   **Loop Structures**: The `correlateMtfLevels()` function has been optimized to use a more efficient algorithm, reducing the number of iterations and improving performance.
*   **Variable Scope**: The `proximity` and `priceRange` variables have been moved to the functions where they are used, ensuring they are only calculated when needed.

**2. Storyline State Architecture**

*   **State Management**: The script now includes a performance-optimized storyline state management system with the following states: `INACTIVE`, `ACTIVE`, `PENDING`, and `INVALIDATED`.
*   **Hierarchical Control**: The visual hierarchy has been updated to prioritize the storyline. When a storyline is active, all non-essential manual levels are hidden to declutter the chart.
*   **Price Origin Tracking**: A persistent "★" marker has been added to clearly mark the exact bar and price where the storyline began.

**3. Roadblock Intelligence Framework**

*   **Roadblock Detection**: A placeholder framework for roadblock intelligence has been integrated. This system can identify and visually flag opposing SNR levels with a "🛑" marker.
*   **User Inputs**: The script now includes user-configurable settings to enable or disable roadblock detection and adjust its sensitivity.

**4. User Documentation**

*   **Storyline Strategy**: The "Storyline Strategy" input now allows you to select a pre-defined storyline or "Off" for manual timeframe selection.
*   **Roadblock Intelligence**: The "Roadblock Intelligence" inputs allow you to enable or disable roadblock detection and adjust its sensitivity.
*   **Status Table**: The status table has been updated to provide more detailed information about the active storyline, including its direction and origin price.
