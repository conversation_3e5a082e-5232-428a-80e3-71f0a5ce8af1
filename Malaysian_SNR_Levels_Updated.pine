//@version=6
// Title: Malaysian SNR Levels + MTF + Freshness Indicator (Historical Scan v5)
// Description: Detects Malaysian SNR levels (Resistance/Support/Gaps) on the current chart and up to two higher timeframes (HTFs).
//              HTF detection now scans historical HTF data to find older levels.
//              Tracks 'Freshness' state based on wick touches on the current chart.
//              Visually distinguishes levels from different timeframes.
// Features:
// - Detects Resistance (A-Levels), Support (V-Levels), and Gap Levels based on Open/Close prices.
// - Supports levels from Chart TF, HTF1, and HTF2.
// - HTF detection scans N past bars for patterns.
// - Tracks Fresh/Unfresh status based on wick touches on the current chart.
// - Customizable lookback period for level display duration.
// - Customizable historical scan depth for HTF detection.
// - Customizable colors and line styles per timeframe.
// - Option to hide unfresh levels.
// - Robust loop handling for array management.
// - Corrected indentation and related syntax.

indicator("Malaysian SNR Levels + MTF + Freshness", overlay=true, max_lines_count=500, max_boxes_count = 0, max_labels_count = 500) // Optimize drawing objects

// --- Inputs ---
// General Settings
string grpGen = "General Settings"
string storylineStrategy = input.string("Off (Manual TF)", "Storyline Strategy", options=["Off (Manual TF)", "W > D > 4H", "D > 4H > 1H", "4H > 1H > 15m"], group=grpGen, tooltip="Select a pre-defined storyline or 'Off' for manual timeframe selection.")
// Label Settings
string grpLabel = "Label Settings"
bool showLabels = input.bool(true, "Show Labels", group=grpLabel)
string labelSize = input.string(size.normal, "Label Size", options=[size.tiny, size.small, size.normal, size.large, size.huge], group=grpLabel)
bool showPriceInLabel = input.bool(true, "Show Price in Label", group=grpLabel)

int lookbackPeriod = input.int(300, "Level Display Lookback (Chart Bars)", minval=10, tooltip="How many current chart bars back to keep displaying detected levels.", group=grpGen)
int rejectionLookback = input.int(3, "Rejection Lookback Period (HTF Bars)", minval=1, maxval=10, group=grpGen, tooltip="How many past HTF bars to check for a rejection signal.")
int htfScanDepth = input.int(50, "HTF Historical Scan Depth (HTF Bars)", minval=2, maxval=500, tooltip="How many past HTF bars to scan for SNR patterns when a new HTF bar forms. Higher values may impact performance.", group=grpGen)
bool showUnfreshLevels = input.bool(true, "Show Unfresh Levels", tooltip="If unchecked, lines disappear once touched by a wick.", group=grpGen)
color colUnfresh = input.color(color.new(color.gray, 30), "Unfresh Color", group=grpGen)

// Declutter Settings
string grpDeclutter = "Declutter Settings"
bool useProximityFilter = input.bool(true, "Enable Proximity Filter", group=grpDeclutter, tooltip="Hides levels that are far from the current price.")
float atrMultiplier = input.float(2.0, "ATR Proximity Multiplier", minval=0.1, step=0.1, group=grpDeclutter, tooltip="Multiplier for the ATR to determine the relevance zone around the price.")
int invalidationCount = input.int(2, "Passthrough Invalidation Count", minval=1, maxval=10, group=grpDeclutter, tooltip="Number of candle body closes through a level before it's removed.")
bool enableMtfNesting = input.bool(true, "Enable MTF Nesting", group=grpDeclutter, tooltip="Correlates lower timeframe levels with higher timeframe zones.")
bool enableZoneGrouping = input.bool(true, "Enable Zone Grouping", group=grpDeclutter, tooltip="Groups close levels and shows only the highest priority ones.")
float groupingProximityAtr = input.float(0.25, "Grouping Proximity (ATR)", minval=0.05, step=0.05, group=grpDeclutter, tooltip="ATR multiplier to define how close levels must be to form a group.")
int maxLevelsInGroup = input.int(2, "Max Levels in Group", minval=1, maxval=5, group=grpDeclutter, tooltip="The number of highest-priority levels to show from a clustered group. Ineffective if 'Aggressive Decluttering' is on.")
bool enableAggressiveDeclutter = input.bool(true, "Enable Aggressive Decluttering", group=grpDeclutter, tooltip="Hides all but the single highest-priority timeframe level within a clustered zone.")

// Roadblock Intelligence Framework (Placeholder)
string grpRoadblock = "Roadblock Intelligence"
bool enableRoadblockDetection = input.bool(true, "Enable Roadblock Detection", group=grpRoadblock, tooltip="Identifies potential opposing levels (roadblocks) within an active storyline.")
float roadblockSensitivityAtr = input.float(1.0, "Roadblock Sensitivity (ATR)", minval=0.1, step=0.1, group=grpRoadblock, tooltip="ATR multiplier to define how close an opposing level must be to be considered a roadblock. WARNING: Smaller values can be computationally intensive.")

// Status Table Settings
string grpStatus = "Status Table Settings"
bool showStatusTable = input.bool(true, "Show Status Table", group=grpStatus)
string tablePosition = input.string(position.top_right, "Table Position", options=[position.top_left, position.top_center, position.top_right, position.middle_left, position.middle_center, position.middle_right, position.bottom_left, position.bottom_center, position.bottom_right], group=grpStatus)

// Chart Timeframe Settings
string grpChart = "Chart Timeframe Levels"
bool showChartLevels = input.bool(true, "Show Chart TF Levels", group=grpChart)
string showChartA_str = input.string("Show", "Resistance (A-Level)", options=["Show", "Hide"], inline="ChartA", group=grpChart)
color colChartFreshA = input.color(color.red, "", inline="ChartA", group=grpChart)
string showChartV_str = input.string("Show", "Support (V-Level)", options=["Show", "Hide"], inline="ChartV", group=grpChart)
color colChartFreshV = input.color(color.green, "", inline="ChartV", group=grpChart)
string showChartBG_str = input.string("Show", "Bullish Gap", options=["Show", "Hide"], inline="ChartBG", group=grpChart)
color colChartFreshBG = input.color(color.blue, "", inline="ChartBG", group=grpChart)
string showChartXG_str = input.string("Show", "Bearish Gap", options=["Show", "Hide"], inline="ChartXG", group=grpChart)
color colChartFreshXG = input.color(color.purple, "", inline="ChartXG", group=grpChart)
int widthChart = input.int(1, "Line Width", minval=1, maxval=5, group=grpChart)

// Higher Timeframe 1 Settings
string grpHTF1 = "Higher Timeframe 1 (HTF1) Levels"
bool showHTF1Levels = input.bool(true, "Show HTF1 Levels", group=grpHTF1, inline="htf1show")
string htf1_manual = input.string("D", "Timeframe", options=["1", "5", "15", "60", "240", "D", "W"], group=grpHTF1, tooltip="Manual timeframe selection. This is ONLY active when 'Storyline Strategy' is set to 'Off (Manual TF)'.", inline="htf1show")
string showHTF1A_str = input.string("Show", "Resistance (A-Level)", options=["Show", "Hide"], inline="HTF1A", group=grpHTF1)
color colHTF1FreshA = input.color(color.maroon, "", inline="HTF1A", group=grpHTF1)
string showHTF1V_str = input.string("Show", "Support (V-Level)", options=["Show", "Hide"], inline="HTF1V", group=grpHTF1)
color colHTF1FreshV = input.color(color.olive, "", inline="HTF1V", group=grpHTF1)
string showHTF1BG_str = input.string("Show", "Bullish Gap", options=["Show", "Hide"], inline="HTF1BG", group=grpHTF1)
color colHTF1FreshBG = input.color(color.navy, "", inline="HTF1BG", group=grpHTF1)
string showHTF1XG_str = input.string("Show", "Bearish Gap", options=["Show", "Hide"], inline="HTF1XG", group=grpHTF1)
color colHTF1FreshXG = input.color(color.fuchsia, "", inline="HTF1XG", group=grpHTF1)
int widthHTF1 = input.int(2, "Line Width", minval=1, maxval=5, group=grpHTF1)

// Higher Timeframe 2 Settings
string grpHTF2 = "Higher Timeframe 2 (HTF2) Levels"
bool showHTF2Levels = input.bool(true, "Show HTF2 Levels", group=grpHTF2, inline="htf2show")
string htf2_manual = input.string("W", "Timeframe", options=["1", "5", "15", "60", "240", "D", "W"], group=grpHTF2, tooltip="Manual timeframe selection. This is ONLY active when 'Storyline Strategy' is set to 'Off (Manual TF)'.", inline="htf2show")
string showHTF2A_str = input.string("Show", "Resistance (A-Level)", options=["Show", "Hide"], inline="HTF2A", group=grpHTF2)
color colHTF2FreshA = input.color(color.orange, "", inline="HTF2A", group=grpHTF2)
string showHTF2V_str = input.string("Show", "Support (V-Level)", options=["Show", "Hide"], inline="HTF2V", group=grpHTF2)
color colHTF2FreshV = input.color(color.lime, "", inline="HTF2V", group=grpHTF2)
string showHTF2BG_str = input.string("Show", "Bullish Gap", options=["Show", "Hide"], inline="HTF2BG", group=grpHTF2)
color colHTF2FreshBG = input.color(color.teal, "", inline="HTF2BG", group=grpHTF2)
string showHTF2XG_str = input.string("Show", "Bearish Gap", options=["Show", "Hide"], inline="HTF2XG", group=grpHTF2)
color colHTF2FreshXG = input.color(color.aqua, "", inline="HTF2XG", group=grpHTF2)
int widthHTF2 = input.int(3, "Line Width", minval=1, maxval=5, group=grpHTF2)

// --- Boolean Conversion ---
// Convert the string dropdown selections to booleans for use in the script logic.
bool showChartA = showChartA_str == "Show"
bool showChartV = showChartV_str == "Show"
bool showChartBG = showChartBG_str == "Show"
bool showChartXG = showChartXG_str == "Show"

bool showHTF1A = showHTF1A_str == "Show"
bool showHTF1V = showHTF1V_str == "Show"
bool showHTF1BG = showHTF1BG_str == "Show"
bool showHTF1XG = showHTF1XG_str == "Show"

bool showHTF2A = showHTF2A_str == "Show"
bool showHTF2V = showHTF2V_str == "Show"
bool showHTF2BG = showHTF2BG_str == "Show"
bool showHTF2XG = showHTF2XG_str == "Show"

// --- Dynamic Timeframe Logic ---
// Returns the two HTFs for a given storyline.
getStorylineTimeframes(strategy) =>
    switch strategy
        "W > D > 4H"  => ["W", "D"]
        "D > 4H > 1H"  => ["D", "240"]
        "4H > 1H > 15m" => ["240", "60"]
        => [na, na] // Default case for "Off"

// Returns the chart TFs allowed for a given storyline.
getStorylineAllowedChartTFs(strategy) =>
    switch strategy
        "W > D > 4H"  => array.from("D", "240") // Storyline is W > D > 4H, so chart can be D or 4H
        "D > 4H > 1H"  => array.from("240", "60") // Storyline is D > 4H > 1H, so chart can be 4H or 1H
        "4H > 1H > 15m" => array.from("60", "15") // Storyline is 4H > 1H > 15m, so chart can be 1H or 15m
        => array.new_string(0) // Empty array for "Off"

[storyline_htf1, storyline_htf2] = getStorylineTimeframes(storylineStrategy)
string[] allowedChartTFs = getStorylineAllowedChartTFs(storylineStrategy)

// Determine final timeframes to use
string htf1 = storylineStrategy == "Off (Manual TF)" ? htf1_manual : storyline_htf1
string htf2 = storylineStrategy == "Off (Manual TF)" ? htf2_manual : storyline_htf2

// --- Timeframe Validation ---
bool isTimeframeAllowed = storylineStrategy == "Off (Manual TF)" or array.includes(allowedChartTFs, timeframe.period)
var label warningLabel = na

// Function to clear all drawing objects by accepting the array as a parameter.
clearAllDrawings(levels) =>
    if array.size(levels) > 0
        for level in levels
            if not na(level.levelLineID)
                line.delete(level.levelLineID)
            if not na(level.levelLabelID)
                label.delete(level.levelLabelID)
        array.clear(levels)

// Function to display a warning if the timeframe is incorrect.
// It now returns the new label ID instead of modifying the global variable directly.
drawTimeframeWarning(lbl, levels) =>
    clearAllDrawings(levels) // Clear any residual drawings by passing the array
    warningText = "TIMEFRAME MISMATCH\n\n" + "Storyline: '" + storylineStrategy + "'\n" + "Allowed Chart TFs: " + array.join(allowedChartTFs, ", ")
    
    // If an old label exists, delete it.
    if not na(lbl)
        label.delete(lbl)
        
    // Create and return the new label
    newLabel = label.new(bar_index, high, text=warningText, xloc=xloc.bar_index, yloc=yloc.price, color=color.new(color.red, 20), textcolor=color.white, style=label.style_label_center, size=size.large)
    newLabel


// --- Constants & UDT ---
// Level Types
string LEVEL_A = "A"         // Resistance Level (A-shape: 📈📉)
string LEVEL_V = "V"         // Support Level (V-shape: 📉📈)
string LEVEL_BG = "BG"       // Bullish Gap (Support)
string LEVEL_XG = "XG"       // Bearish Gap (Resistance)
string LEVEL_RBS = "RBS"     // Resistance Become Support
string LEVEL_SBR = "SBR"     // Support Become Resistance

// Timeframe Source Identifiers
string TF_CHART = "Chart"
string TF_HTF1 = "HTF1"
string TF_HTF2 = "HTF2"

// Structure to hold all info about a single SNR level
type SnrLevel
    float price
    int detectionTime        // Timestamp where the level was *detected*
    string levelType         // The base pattern (A, V, BG, XG)
    string patternType       // The functional pattern (A, V, SBR, RBS etc.)
    string mtfParentInfo     // Info about the HTF level it's nested in
    bool isFresh
    line levelLineID
    label levelLabelID
    string timeframeSource
    int sourceTime           // The timestamp on the source timeframe (for uniqueness)
    int visualOriginTime     // The timestamp where the line should visually start drawing from
    int priority             // New field for sorting priority (1=HTF2, 2=HTF1, 3=Chart)
    float crossCount = 0
    int touchCount = 0       // How many times a wick has touched this level
    bool storylineStarted = false // Has a storyline been confirmed from this level?
    bool isHidden = false
    bool isRoadblock = false // Is this level a potential roadblock for an active storyline?
    label roadblockLabelID

// --- Global State & Performance (Optimized with var declarations) ---
var allLevels = array.new<SnrLevel>()      // Master array for all levels
var existingLevelKeys = map.new<string, bool>() // For O(1) duplicate checks
var newLevelsThisBar = array.new<SnrLevel>() // Temp array for batch processing
var processedLevels = array.new<SnrLevel>() // Cached processed levels to avoid recomputation
var tempWorkingArray = array.new<SnrLevel>() // Reusable working array to reduce allocations

// --- Storyline State Management (Performance-Optimized) ---
var bool storyline_active = false
var int storyline_direction = 0      // 1=bullish, -1=bearish, 0=neutral
var float storyline_origin_price = na
var int storyline_origin_bar = na
var float storyline_strength = 0.0
var string storyline_status = "INACTIVE" // INACTIVE/ACTIVE/PENDING/INVALIDATED
var SnrLevel storyline_origin_level = na // The level that started the storyline

// --- ATR Calculation (Global Scope) - Optimized with caching ---
var float atrValue = na
var int atrLastUpdate = 0
// Only recalculate ATR every 5 bars to reduce computational load
if bar_index - atrLastUpdate >= 5 or na(atrValue)
    atrValue := ta.atr(14)
    atrLastUpdate := bar_index

// --- Helper Functions (Optimized with caching) ---
// Cached key generation to avoid repeated string operations
var map<string, string> keyCache = map.new<string, string>()
getLevelKey(price, tfSource, srcTime, pattern) =>
    // Create a simple cache key for the input parameters
    cacheKey = str.tostring(price) + tfSource + str.tostring(srcTime) + pattern
    cachedResult = map.get(keyCache, cacheKey)
    if not na(cachedResult)
        cachedResult
    else
        result = str.tostring(price, format.mintick) + ":" + tfSource + ":" + str.tostring(srcTime) + ":" + pattern
        map.put(keyCache, cacheKey, result)
        result

// Enhanced line style with PAH-style progressive visual feedback
getLineStyle(isFresh, crossCount) =>
    // Progressive line style degradation based on invalidation progress
    if crossCount >= invalidationCount
        line.style_dotted  // Fully invalidated levels
    else if crossCount >= invalidationCount - 1
        line.style_dotted  // Near invalidation (1 cross away)
    else if crossCount >= 1 or not isFresh
        line.style_dashed  // Touched or unfresh levels
    else
        line.style_solid   // Fresh, untouched levels

// Cached color lookup to avoid repeated conditional evaluations
var map<string, color> colorCache = map.new<string, color>()
getFreshColor(levelType, timeframeSource) =>
    colorKey = levelType + ":" + timeframeSource
    cachedColor = map.get(colorCache, colorKey)
    if not na(cachedColor)
        cachedColor
    else
        result = timeframeSource == TF_CHART ?
             (levelType == LEVEL_A ? colChartFreshA :
              levelType == LEVEL_V ? colChartFreshV :
              levelType == LEVEL_BG ? colChartFreshBG :
              levelType == LEVEL_XG ? colChartFreshXG :
              color.gray) :
          timeframeSource == TF_HTF1 ?
             (levelType == LEVEL_A ? colHTF1FreshA :
              levelType == LEVEL_V ? colHTF1FreshV :
              levelType == LEVEL_BG ? colHTF1FreshBG :
              levelType == LEVEL_XG ? colHTF1FreshXG :
              color.gray) :
          timeframeSource == TF_HTF2 ?
             (levelType == LEVEL_A ? colHTF2FreshA :
              levelType == LEVEL_V ? colHTF2FreshV :
              levelType == LEVEL_BG ? colHTF2FreshBG :
              levelType == LEVEL_XG ? colHTF2FreshXG :
              color.gray) :
          color.gray
        map.put(colorCache, colorKey, result)
        result

// Determines the correct line width based on source timeframe
getLineWidth(timeframeSource) =>
    width = timeframeSource == TF_CHART ? widthChart :
      timeframeSource == TF_HTF1 ? widthHTF1 :
      timeframeSource == TF_HTF2 ? widthHTF2 :
      1 // Default width
    width

// Dynamic level lifetime calculation for PAH-style responsive rendering
getLevelLifetime(level) =>
    // Base lifetime on timeframe priority (HTF levels live longer)
    baseLifetime = level.timeframeSource == TF_HTF2 ? 150 :
                   level.timeframeSource == TF_HTF1 ? 100 : 50

    // Reduce lifetime based on invalidation (each cross reduces lifetime)
    invalidationPenalty = level.crossCount * 15

    // Fresh levels get longer lifetime bonus
    freshnessBonus = level.isFresh ? 20 : 0

    // Storyline levels get extended lifetime for importance
    storylineBonus = level.storylineStarted ? 50 : 0

    // Calculate final lifetime with bounds
    finalLifetime = math.max(10, baseLifetime - invalidationPenalty + freshnessBonus + storylineBonus)
    math.min(finalLifetime, lookbackPeriod)

// --- Label Helpers ---
// Translates level type to a display string (R/S/Bullish OC/Bearish OC)
getLevelTypeAbbreviation(patternType) =>
    switch patternType
        LEVEL_A => "R"
        LEVEL_V => "S"
        LEVEL_BG => "Bullish OC"
        LEVEL_XG => "Bearish OC"
        LEVEL_RBS => "RBS"
        LEVEL_SBR => "SBR"
        => "?"

// Translates timeframe source to a display string (e.g., D, 4H, Chart)
getTimeframeAbbreviation(timeframeSource) =>
    switch timeframeSource
        TF_CHART => "Chart"
        TF_HTF1  => htf1
        TF_HTF2  => htf2
        => "?"

// Creates the full, detailed label text
getLabelText(patternType, timeframeSource, price, mtfInfo, isSimple) =>
    tfAbbr = getTimeframeAbbreviation(timeframeSource)
    levelAbbr = getLevelTypeAbbreviation(patternType)
    
    if isSimple
        // Simple version for on-chart labels
        tfAbbr + " " + levelAbbr
    else
        // Detailed version for the status table
        priceStr = showPriceInLabel ? " " + str.tostring(price, format.mintick) : ""
        baseLabel = tfAbbr + " " + levelAbbr + priceStr
        // Append MTF info if it exists
        if str.length(mtfInfo) > 0
            baseLabel += " MTF " + mtfInfo
        baseLabel

// --- Fetch HTF Historical Data Arrays ---
// Function to request historical data arrays for a given timeframe
// Returns tuple: [series<float> opens, series<float> closes, series<int> times]
getHistoricalData(tf) =>
    htf_open = request.security(syminfo.tickerid, tf, open, lookahead=barmerge.lookahead_off)
    htf_close = request.security(syminfo.tickerid, tf, close, lookahead=barmerge.lookahead_off)
    htf_time = request.security(syminfo.tickerid, tf, time, lookahead=barmerge.lookahead_off)
    [htf_open, htf_close, htf_time]

// --- MTF Nesting and Grouping Logic ---
// Moved this function here so it's defined before it's called in addLevel()
getTimeframePriority(tf) =>
    tf == TF_HTF2 ? 1 : tf == TF_HTF1 ? 2 : tf == TF_CHART ? 3 : 99

// Optimized insertion sort for small arrays (< 50 elements) - O(n²) but with early breaks
// For larger arrays, we'll use a different strategy to avoid performance issues
sortLevelsByPrice(array<SnrLevel> unsortedArray) =>
    arraySize = array.size(unsortedArray)
    if arraySize <= 1
        unsortedArray
    else if arraySize > 100  // For large arrays, skip sorting to avoid timeout
        unsortedArray
    else
        // Use in-place bubble sort with early termination for better performance
        array<SnrLevel> workingArray = array.copy(unsortedArray)
        for i = 0 to arraySize - 2
            swapped = false
            for j = 0 to arraySize - 2 - i
                current = array.get(workingArray, j)
                next = array.get(workingArray, j + 1)
                if current.price > next.price
                    array.set(workingArray, j, next)
                    array.set(workingArray, j + 1, current)
                    swapped := true
            if not swapped  // Early termination if array is already sorted
                break
        workingArray

// Optimized priority sorting with size limits
sortLevelsByPriority(array<SnrLevel> unsortedArray) =>
    arraySize = array.size(unsortedArray)
    if arraySize <= 1
        unsortedArray
    else if arraySize > 50  // Limit processing for large arrays
        // For large arrays, just return first 50 elements to avoid timeout
        array<SnrLevel> limitedArray = array.new<SnrLevel>()
        for i = 0 to math.min(49, arraySize - 1)
            array.push(limitedArray, array.get(unsortedArray, i))
        limitedArray
    else
        // Use in-place bubble sort for small arrays
        array<SnrLevel> workingArray = array.copy(unsortedArray)
        for i = 0 to arraySize - 2
            swapped = false
            for j = 0 to arraySize - 2 - i
                current = array.get(workingArray, j)
                next = array.get(workingArray, j + 1)
                if current.priority > next.priority
                    array.set(workingArray, j, next)
                    array.set(workingArray, j + 1, current)
                    swapped := true
            if not swapped
                break
        workingArray

// Optimized MTF correlation with reduced complexity and early exits
correlateMtfLevels(array<SnrLevel> levels) =>
    levelCount = array.size(levels)
    if levelCount < 2 or not enableMtfNesting
        levels
    else if levelCount > 200  // Skip correlation for very large arrays to prevent timeout
        levels
    else
        // Reuse global working array instead of creating new one
        array.clear(tempWorkingArray)
        proximity = atrValue * groupingProximityAtr

        for i = 0 to levelCount - 1
            SnrLevel mutableChild = array.get(levels, i)
            mutableChild.mtfParentInfo := "" // Reset before check
            childPriority = getTimeframePriority(mutableChild.timeframeSource)

            // Optimization: If the child is the highest priority (HTF2), it can't have a parent.
            if childPriority > 1
                // Limit search range to improve performance (max 10 levels in each direction)
                searchRange = math.min(10, levelCount)

                // Check backwards for a parent (limited range)
                for j = math.max(0, i - searchRange) to i - 1
                    parent = array.get(levels, j)
                    if mutableChild.price - parent.price > proximity
                        break // Stop searching if price gap is too large
                    parentPriority = getTimeframePriority(parent.timeframeSource)
                    if parentPriority < childPriority
                        mutableChild.mtfParentInfo := getTimeframeAbbreviation(parent.timeframeSource) + " " + getLevelTypeAbbreviation(parent.patternType)
                        break // Found the nearest parent in this direction

                // Check forwards for a parent only if one wasn't found going backwards (limited range)
                if str.length(mutableChild.mtfParentInfo) == 0
                    for j = i + 1 to math.min(levelCount - 1, i + searchRange)
                        parent = array.get(levels, j)
                        if parent.price - mutableChild.price > proximity
                            break // Stop searching if price gap is too large
                        parentPriority = getTimeframePriority(parent.timeframeSource)
                        if parentPriority < childPriority
                            mutableChild.mtfParentInfo := getTimeframeAbbreviation(parent.timeframeSource) + " " + getLevelTypeAbbreviation(parent.patternType)
                            break // Found the nearest parent in this direction

            array.push(tempWorkingArray, mutableChild)

        tempWorkingArray // Return the working array with the modifications

// Processes a cluster of levels, hiding lower-priority ones. Returns a new array with modified isHidden flags.
processCluster(array<SnrLevel> cluster) =>
    if array.size(cluster) <= 1
        cluster
    else
        sortedCluster = sortLevelsByPriority(cluster)
        array<SnrLevel> processedCluster = array.new<SnrLevel>()
        
        if enableAggressiveDeclutter
            // Aggressive mode: Keep the first (highest priority), hide the rest.
            for k = 0 to array.size(sortedCluster) - 1
                level = array.get(sortedCluster, k)
                if k > 0
                    level.isHidden := true
                array.push(processedCluster, level)
        else 
            // Original mode: Keep up to maxLevelsInGroup, hide the rest.
            for k = 0 to array.size(sortedCluster) - 1
                level = array.get(sortedCluster, k)
                if k >= maxLevelsInGroup
                    level.isHidden := true
                array.push(processedCluster, level)
    
        processedCluster

// Optimized grouping with performance limits and reduced array operations
groupAndFilterLevels(array<SnrLevel> levels) =>
    levelCount = array.size(levels)
    if levelCount < 2 or not enableZoneGrouping
        levels
    else if levelCount > 150  // Skip grouping for very large arrays to prevent timeout
        levels
    else
        // In-place modification to avoid array copying
        groupingProximity = atrValue * groupingProximityAtr

        // Reset isHidden flags in-place
        for i = 0 to levelCount - 1
            level = array.get(levels, i)
            level.isHidden := false

        // Simple clustering algorithm with reduced complexity
        for i = 0 to levelCount - 1
            currentLevel = array.get(levels, i)
            if not currentLevel.isHidden
                clusterSize = 1
                clusterLevels = array.new<SnrLevel>()
                array.push(clusterLevels, currentLevel)

                // Find nearby levels (limited search range for performance)
                searchEnd = math.min(levelCount - 1, i + 20)  // Limit search range
                for j = i + 1 to searchEnd
                    otherLevel = array.get(levels, j)
                    if not otherLevel.isHidden and math.abs(currentLevel.price - otherLevel.price) <= groupingProximity
                        array.push(clusterLevels, otherLevel)
                        clusterSize += 1
                        if clusterSize >= 10  // Limit cluster size for performance
                            break

                // Process cluster if it has multiple levels
                if clusterSize > 1
                    processedCluster = processCluster(clusterLevels)
                    // Apply results back to original array
                    for k = 0 to array.size(processedCluster) - 1
                        processedLevel = array.get(processedCluster, k)
                        // Find and update the corresponding level in the original array
                        for m = i to searchEnd
                            originalLevel = array.get(levels, m)
                            if originalLevel.price == processedLevel.price and originalLevel.timeframeSource == processedLevel.timeframeSource
                                originalLevel.isHidden := processedLevel.isHidden
                                break

        levels

// --- Core Logic (runs on every bar) ---

// ① --- Level Detection (Optimized) ---

// Checks if a level key exists in our map for O(1) lookup.
levelExists(key) =>
    map.get(existingLevelKeys, key) == true

// Adds a new level to a temporary array for batch processing, avoiding costly `array.insert` calls.
addLevel(levelType, patternType, price, tfSource, srcTime, visualOrigin) =>
    key = getLevelKey(price, tfSource, srcTime, patternType)
    if not levelExists(key)
        // Get the priority for the new level
        priority = getTimeframePriority(tfSource)
        // Create the new level instance, now including the priority
        newLevel = SnrLevel.new(price, time, levelType, patternType, "", true, na, na, tfSource, srcTime, visualOrigin, priority, 0, 0, false, false)
        array.push(newLevelsThisBar, newLevel)
        map.put(existingLevelKeys, key, true)

// Optimized HTF historical scanning with performance limits and caching
scanHTFHistoryAndAddLevels(tf, depth, tfSource, enableA, enableV, enableBG, enableXG) =>
    // Limit scan depth to prevent timeout - reduce from user input if too high
    effectiveDepth = math.min(depth, 25)  // Cap at 25 bars to prevent timeout

    // Only proceed if patterns are enabled
    if (enableA or enableV or enableBG or enableXG)
        [htf_o, htf_c, htf_time] = getHistoricalData(tf)

        // Process in smaller chunks with early exit on consecutive NA values
        naCount = 0
        maxConsecutiveNA = 5  // Stop scanning if we hit 5 consecutive NA values

        for i = 2 to effectiveDepth + 1
            o1 = htf_o[i]
            c1 = htf_c[i]
            o2 = htf_o[i-1]
            c2 = htf_c[i-1]
            source_time = htf_time[i-1]

            if na(o1) or na(c1) or na(o2) or na(c2) or na(source_time)
                naCount += 1
                if naCount >= maxConsecutiveNA
                    break  // Stop scanning if too many consecutive NA values
                continue
            else
                naCount := 0  // Reset NA counter when we get valid data

            // Batch pattern detection to reduce function calls
            if enableA and c1 > o1 and c2 < o2
                addLevel(LEVEL_A, LEVEL_A, c1, tfSource, source_time, source_time)
            if enableV and c1 < o1 and c2 > o2
                addLevel(LEVEL_V, LEVEL_V, c1, tfSource, source_time, source_time)
            if enableBG and c1 > o1 and c2 > o2
                addLevel(LEVEL_BG, LEVEL_BG, c1, tfSource, source_time, source_time)
            if enableXG and c1 < o1 and c2 < o2
                addLevel(LEVEL_XG, LEVEL_XG, c1, tfSource, source_time, source_time)

// Detects SBR/RBS flips and adds them to the temporary array.
detectSBR_RBS() =>
    if array.size(allLevels) > 0
        for level in allLevels
            isSupport = level.patternType == LEVEL_V or level.patternType == LEVEL_BG or level.patternType == LEVEL_RBS
            if isSupport and close < level.price and open > level.price
                addLevel(level.levelType, LEVEL_SBR, level.price, level.timeframeSource, time, time)
            isResistance = level.patternType == LEVEL_A or level.patternType == LEVEL_XG or level.patternType == LEVEL_SBR
            if isResistance and close > level.price and open < level.price
                addLevel(level.levelType, LEVEL_RBS, level.price, level.timeframeSource, time, time)

// --- Trigger Detections ---
if barstate.isconfirmed
    array.clear(newLevelsThisBar) // Clear the temporary array at the start of each confirmed bar

    // Detect for Chart Timeframe
    if showChartLevels
        patternCompletionTime = time[1]
        if showChartA and close[2] > open[2] and close[1] < open[1]
            addLevel(LEVEL_A, LEVEL_A, close[2], TF_CHART, patternCompletionTime, patternCompletionTime)
        if showChartV and close[2] < open[2] and close[1] > open[1]
            addLevel(LEVEL_V, LEVEL_V, close[2], TF_CHART, patternCompletionTime, patternCompletionTime)
        if showChartBG and close[2] > open[2] and close[1] > open[1]
            addLevel(LEVEL_BG, LEVEL_BG, close[2], TF_CHART, patternCompletionTime, patternCompletionTime)
        if showChartXG and close[2] < open[2] and close[1] < open[1]
            addLevel(LEVEL_XG, LEVEL_XG, close[2], TF_CHART, patternCompletionTime, patternCompletionTime)

    // Check for new HTF bars
    bool isNewHTF1Bar = ta.change(time(htf1)) != 0
    bool isNewHTF2Bar = ta.change(time(htf2)) != 0

    // Scan HTF1 History
    if showHTF1Levels and htf1 != "" and isNewHTF1Bar
        scanHTFHistoryAndAddLevels(htf1, htfScanDepth, TF_HTF1, showHTF1A, showHTF1V, showHTF1BG, showHTF1XG)

    // Scan HTF2 History
    if showHTF2Levels and htf2 != "" and isNewHTF2Bar
        scanHTFHistoryAndAddLevels(htf2, htfScanDepth, TF_HTF2, showHTF2A, showHTF2V, showHTF2BG, showHTF2XG)

    // Detect SBR/RBS Flips
    detectSBR_RBS()

    // Optimized batch processing with size limits
    if array.size(newLevelsThisBar) > 0
        for newLevel in newLevelsThisBar
            array.push(allLevels, newLevel)

        // Only sort if array is manageable size, otherwise skip to prevent timeout
        if array.size(allLevels) <= 200
            allLevels := sortLevelsByPrice(allLevels)
        // If array is too large, remove oldest levels to maintain performance
        else if array.size(allLevels) > 500
            // Keep only the most recent 300 levels
            array<SnrLevel> trimmedLevels = array.new<SnrLevel>()
            startIndex = array.size(allLevels) - 300
            for i = startIndex to array.size(allLevels) - 1
                array.push(trimmedLevels, array.get(allLevels, i))
            allLevels := trimmedLevels

// ② --- Level Processing & Storyline Architecture ---

// Manages the new storyline state machine. Returns the new state as a tuple.
manageStoryline(current_status, current_active, current_origin_level, current_origin_price, current_origin_bar, current_direction) =>
    // Initialize local variables with current global state passed as arguments
    string new_status = current_status
    bool new_active = current_active
    SnrLevel new_origin_level = current_origin_level
    float new_origin_price = current_origin_price
    int new_origin_bar = current_origin_bar
    int new_direction = current_direction

    // --- Roadblock Interaction ---
    if new_active and new_status == "ACTIVE"
        for level in allLevels
            if level.isRoadblock and high >= level.price and low <= level.price
                new_status := "ROADBLOCK"
                break

    // --- Invalidation Logic ---
    if new_active
        isSupport = new_direction == 1
        invalidation_break = (isSupport and close < new_origin_price) or (not isSupport and close > new_origin_price)
        if invalidation_break
            new_active := false
            new_status := "INVALIDATED"

    // --- Activation Logic ---
    if not new_active and storylineStrategy != "Off (Manual TF)"
        new_status := "PENDING"
        SnrLevel bestCandidate = na
        float closestDist = atrValue * atrMultiplier

        if array.size(allLevels) > 0
            for level in allLevels
                if level.isFresh and not level.storylineStarted and level.timeframeSource != TF_CHART
                    dist = math.abs(level.price - close)
                    isSupport = level.patternType == LEVEL_V or level.patternType == LEVEL_BG or level.patternType == LEVEL_RBS
                    isRejected = (isSupport and high >= level.price and close > level.price) or (not isSupport and low <= level.price and close < level.price)
                    
                    if isRejected and dist < closestDist
                        closestDist := dist
                        bestCandidate := level
        
        if not na(bestCandidate)
            new_active := true
            new_origin_level := bestCandidate
            new_origin_price := bestCandidate.price
            new_origin_bar := bar_index
            new_direction := (bestCandidate.patternType == LEVEL_V or bestCandidate.patternType == LEVEL_BG or bestCandidate.patternType == LEVEL_RBS) ? 1 : -1
            new_status := "ACTIVE"
            bestCandidate.storylineStarted := true
    
    // Return all state variables as a tuple
    [new_status, new_active, new_origin_level, new_origin_price, new_origin_bar, new_direction]

// Optimized roadblock detection with early exits and performance limits
detectRoadblocks(array<SnrLevel> levels) =>
    levelCount = array.size(levels)
    if not storyline_active or not enableRoadblockDetection or levelCount == 0
        levels
    else if levelCount > 100  // Skip roadblock detection for large arrays
        levels
    else
        // In-place modification to avoid array copying
        roadblockRange = atrValue * roadblockSensitivityAtr

        for i = 0 to levelCount - 1
            level = array.get(levels, i)
            level.isRoadblock := false // Reset on each bar

            // Early exit if level is too far from current price
            if math.abs(level.price - close) > roadblockRange
                continue

            // Check if the level is an opposing force
            isOpposingResistance = storyline_direction == 1 and (level.patternType == LEVEL_A or level.patternType == LEVEL_XG or level.patternType == LEVEL_SBR)
            isOpposingSupport = storyline_direction == -1 and (level.patternType == LEVEL_V or level.patternType == LEVEL_BG or level.patternType == LEVEL_RBS)

            if isOpposingResistance or isOpposingSupport
                // Check if the roadblock is ahead of the current price
                isAhead = (storyline_direction == 1 and level.price > close) or (storyline_direction == -1 and level.price < close)
                if isAhead
                    level.isRoadblock := true

        levels

// Removes invalidated levels and their keys from the map.
pruneInvalidatedLevels(levelArray, keyMap) =>
    keptLevels = array.new<SnrLevel>()
    if array.size(levelArray) > 0
        // Performance Fix: Calculate the cutoff bar index. Levels older than this will be pruned.
        cutoffBarIndex = bar_index - lookbackPeriod

        for level in levelArray
            // A level is pruned if it's too old OR has been crossed too many times.
            // Storyline-originated levels are protected from pruning.
            isTooOld = level.detectionTime < time[lookbackPeriod] // Prune if detected before the lookback window
            isInvalidated = level.crossCount >= invalidationCount and not level.storylineStarted
            
            prune = isTooOld or isInvalidated

            if not prune
                array.push(keptLevels, level)
            else
                // Clean up drawing objects for the pruned level
                if not na(level.levelLineID)
                    line.delete(level.levelLineID)
                if not na(level.levelLabelID)
                    label.delete(level.levelLabelID)
                // Remove from key map to allow re-detection
                key = getLevelKey(level.price, level.timeframeSource, level.sourceTime, level.patternType)
                map.remove(keyMap, key)
    keptLevels

// Optimized visual updates with reduced drawing operations and batching
updateVisuals(levelArray) =>
    arraySize = array.size(levelArray)
    if arraySize > 0
        // Limit visual updates for performance - only process most relevant levels
        maxVisualLevels = math.min(arraySize, 100)  // Cap at 100 visible levels
        priceRange = atrValue * atrMultiplier

        for i = 0 to maxVisualLevels - 1
            level = array.get(levelArray, i)

            // Batch proximity and visibility calculations
            isFar = useProximityFilter and (level.price > high + priceRange or level.price < low - priceRange)
            level.isHidden := level.isHidden or isFar

            // --- HIERARCHICAL CONTROL ---
            if storyline_active and not na(storyline_origin_level) and level.price != storyline_origin_level.price
                level.isHidden := true

            // Determine if the level should be visible
            bool shouldBeVisible = (showUnfreshLevels or level.isFresh) and not level.isHidden

            // --- Optimized Roadblock Visual Marker ---
            if shouldBeVisible and level.isRoadblock and enableRoadblockDetection
                if na(level.roadblockLabelID)
                    level.roadblockLabelID := label.new(time, level.price, "🛑", xloc=xloc.bar_time, color=color.new(color.white, 100), textcolor=color.orange, style=label.style_label_right, size=size.small)
                else
                    label.set_xy(level.roadblockLabelID, time, level.price)
            else
                if not na(level.roadblockLabelID)
                    label.delete(level.roadblockLabelID)
                    level.roadblockLabelID := na

            // --- Dynamic Drawing Logic with PAH-style responsive endpoints ---
            if shouldBeVisible
                freshColor = getFreshColor(level.levelType, level.timeframeSource)
                lineWidth = getLineWidth(level.timeframeSource)
                lineColor = level.isFresh ? freshColor : colUnfresh
                lineStyle = getLineStyle(level.isFresh, level.crossCount)

                // Calculate dynamic endpoint based on level lifetime (PAH-style)
                extensionBars = getLevelLifetime(level)
                dynamicEndTime = math.round(time + extensionBars * timeframe.in_seconds() * 1000)

                // Create/update line with dynamic endpoints (no more infinite extension)
                if na(level.levelLineID)
                    level.levelLineID := line.new(level.visualOriginTime, level.price, dynamicEndTime, level.price, xloc=xloc.bar_time, extend=extend.none, color=lineColor, style=lineStyle, width=lineWidth)
                else
                    // Dynamic endpoint management with continuous recalculation
                    line.set_x1(level.levelLineID, level.visualOriginTime)
                    line.set_x2(level.levelLineID, dynamicEndTime)
                    line.set_y1(level.levelLineID, level.price)
                    line.set_y2(level.levelLineID, level.price)
                    line.set_color(level.levelLineID, lineColor)
                    line.set_style(level.levelLineID, lineStyle)

                // Dynamic label positioning at line endpoints (PAH-style)
                if showLabels
                    labelText = getLabelText(level.patternType, level.timeframeSource, level.price, level.mtfParentInfo, true)
                    labelTextColor = level.isFresh ? lineColor : colUnfresh

                    // Position label at dynamic endpoint for visual connection (ensure int type)
                    labelPosition = int(dynamicEndTime)

                    // Create or update label with type-safe approach
                    if na(level.levelLabelID)
                        var newLabel = label.new(labelPosition, level.price, text=labelText, xloc=xloc.bar_time, color=color.new(color.white, 100), textcolor=labelTextColor, style=label.style_label_left, size=labelSize)
                        level.levelLabelID := newLabel  // Assignment outside label.new() for type safety
                    // Update existing label with dynamic positioning
                    else
                        label.set_xy(level.levelLabelID, labelPosition, level.price)
                        label.set_text(level.levelLabelID, labelText)
                        label.set_textcolor(level.levelLabelID, labelTextColor)
                else
                    if not na(level.levelLabelID)
                        label.delete(level.levelLabelID)
                        level.levelLabelID := na

            // --- Optimized Cleanup Logic ---
            else
                if not na(level.levelLineID)
                    line.delete(level.levelLineID)
                    level.levelLineID := na
                if not na(level.levelLabelID)
                    label.delete(level.levelLabelID)
                    level.levelLabelID := na

// --- Main processing pipeline ---
var bool hasNewLevels = false
var table statusTable = na

if isTimeframeAllowed
    // If a warning label was visible, delete it now that the timeframe is correct.
    if not na(warningLabel)
        label.delete(warningLabel)
        warningLabel := na

    if barstate.isconfirmed
        hasNewLevels := array.size(newLevelsThisBar) > 0

        // 1. Manage Storyline State Machine
        [temp_status, temp_active, temp_origin_level, temp_origin_price, temp_origin_bar, temp_direction] = manageStoryline(storyline_status, storyline_active, storyline_origin_level, storyline_origin_price, storyline_origin_bar, storyline_direction)
        storyline_status := temp_status
        storyline_active := temp_active
        storyline_origin_level := temp_origin_level
        storyline_origin_price := temp_origin_price
        storyline_origin_bar := temp_origin_bar
        storyline_direction := temp_direction

        // 2. Update individual level states (touches, crosses, freshness)
        // This now returns a new array with the updated states.
        array<SnrLevel> updatedStateLevels = array.new<SnrLevel>()
        if array.size(allLevels) > 0
            for level in allLevels
                SnrLevel mutableLevel = level
                if not storyline_active or mutableLevel.storylineStarted
                    if mutableLevel.isFresh
                        if high >= mutableLevel.price and low <= mutableLevel.price
                            mutableLevel.touchCount += 1
                        if (open < mutableLevel.price and close > mutableLevel.price) or (open > mutableLevel.price and close < mutableLevel.price)
                            mutableLevel.crossCount += 1
                            mutableLevel.isFresh := false
                        if not mutableLevel.storylineStarted
                            if mutableLevel.timeframeSource == TF_CHART and mutableLevel.touchCount > 0
                                mutableLevel.isFresh := false
                            else if mutableLevel.timeframeSource != TF_CHART and mutableLevel.touchCount >= 4
                                mutableLevel.isFresh := false
                array.push(updatedStateLevels, mutableLevel)
            allLevels := updatedStateLevels

    // 3. Prune, group, and draw on every bar to keep visuals responsive
    // Only run expensive logic if there are levels to process.
    if array.size(allLevels) > 0
        // Pruning happens on every bar to remove old levels from view.
        allLevels := pruneInvalidatedLevels(allLevels, existingLevelKeys)
        
        // Enhanced conditional processing with performance guards
        if hasNewLevels or array.size(processedLevels) == 0
            // Only run expensive correlation and grouping if we have a reasonable number of levels
            if array.size(allLevels) <= 300  // Skip if too many levels to prevent timeout
                tempLevels = correlateMtfLevels(allLevels)
                tempLevels := groupAndFilterLevels(tempLevels)
                processedLevels := tempLevels
            else
                // For very large arrays, just use the original levels without processing
                processedLevels := allLevels

        // Conditional roadblock detection and visual updates
        if array.size(processedLevels) <= 200  // Only run if manageable size
            finalLevels = detectRoadblocks(processedLevels)
            updateVisuals(finalLevels)
        else
            // For large arrays, skip roadblock detection and just update visuals
            updateVisuals(processedLevels)

    // --- Status Table Drawing ---
    // --- Storyline Origin Visuals ---
    var label storylineOriginLabel = na
    if storyline_active and not na(storyline_origin_price)
        if na(storylineOriginLabel)
            storylineOriginLabel := label.new(storyline_origin_bar, storyline_origin_price, "★", xloc=xloc.bar_index, yloc=yloc.price, color=color.new(color.yellow, 100), textcolor=color.yellow, style=label.style_label_up, size=size.normal)
        else
            label.set_xy(storylineOriginLabel, storyline_origin_bar, storyline_origin_price)
    else if not na(storylineOriginLabel)
        label.delete(storylineOriginLabel)
        storylineOriginLabel := na

    // --- Status Table Drawing ---
    if showStatusTable and barstate.islast
        if na(statusTable)
            statusTable := table.new(tablePosition, 4, 10, border_width=1)
        else
            table.clear(statusTable, 0, 0)
        
        // Header
        table.cell(statusTable, 0, 0, "Metric", bgcolor=color.gray, text_halign=text.align_left)
        table.cell(statusTable, 1, 0, "Value", bgcolor=color.gray, text_halign=text.align_left)
        table.merge_cells(statusTable, 1, 0, 3, 0)

        // Storyline Status
        storylineColor = storyline_active ? color.new(color.blue, 80) : color.new(color.gray, 80)
        dirText = storyline_direction == 1 ? "Bullish" : storyline_direction == -1 ? "Bearish" : "Neutral"
        table.cell(statusTable, 0, 1, "Storyline", bgcolor=storylineColor, text_halign=text.align_left)
        table.cell(statusTable, 1, 1, storyline_status, bgcolor=storylineColor, text_halign=text.align_left)
        table.cell(statusTable, 2, 1, "Dir: " + dirText, bgcolor=storylineColor, text_halign=text.align_left)
        table.cell(statusTable, 3, 1, "Origin: " + str.tostring(storyline_origin_price, format.mintick), bgcolor=storylineColor, text_halign=text.align_left)

        // Display up to 8 of the most relevant visible levels
        int row = 2
        if array.size(allLevels) > 0
            for level in allLevels
                if not level.isHidden and row < 10
                    string levelText = getLabelText(level.patternType, level.timeframeSource, level.price, level.mtfParentInfo, false)
                    string stateText = level.isFresh ? "Fresh" : "Unfresh"
                    string storyText = level.storylineStarted ? "Origin" : "-"
                    
                    color rowColor = level.isFresh ? color.new(color.green, 80) : color.new(color.red, 80)
                    if level.storylineStarted
                        rowColor := color.new(color.yellow, 80)

                    table.cell(statusTable, 0, row, levelText, bgcolor=rowColor, text_halign=text.align_left)
                    table.cell(statusTable, 1, row, stateText, bgcolor=rowColor, text_halign=text.align_left)
                    table.cell(statusTable, 2, row, "Cross: " + str.tostring(level.crossCount), bgcolor=rowColor, text_halign=text.align_left)
                    table.cell(statusTable, 3, row, storyText, bgcolor=rowColor, text_halign=text.align_left)
                    row += 1
else
    // Timeframe is not allowed, display the warning.
    // Call the function and reassign the return value to the global variable.
    newWarningLabel = drawTimeframeWarning(warningLabel, allLevels)
    warningLabel := newWarningLabel
    
    // Also clear and hide the status table if it exists.
    if not na(statusTable)
        table.clear(statusTable, 0, 0)
