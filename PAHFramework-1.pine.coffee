//@version=6
library("PAHelperFramework", overlay = true, dynamic_requests = true)

export type LevelInfo
    float Price
    float CrossedThroughCount = 0
    array<float> CrossedThroughTimes
    string Timeframe
    int Time
    int PivotDirection
    int LineIndex
    bool IsHidden = false
    bool IsTapped = false
    box ShadeBox
    int BarIndex = bar_index

export type CleanZone
    array<float> Closes
    box Box
    bool IsBullish
    float Open
    float RawOpen
    bool AttemptedTrade = false

export type TimeframeInfo
    string Timeframe
    bool Enabled

    int Duration

    array<int> TimeOpen
    array<int> TimeClose
    bool IsClosing

    array<float> Open
    array<float> Close
    array<float> Low
    array<float> High

    array<float> RawOpen
    array<float> RawClose
    array<float> RawLow
    array<float> RawHigh

    array<LevelInfo> Levels
    array<line> Lines
    array<label> Labels

    float PivotHigh
    float PivotLow
    bool EnableLevelClosureNotifications

    color LineColor
    color LabelTextColor
    int LineWidth
    string LabelStyle
    color LabelColor
    color HeatmapColor

    float Atr

export type StateInfo
    bool HasChanged = false

export type LTARetestInfo
    float Price
    int Time

export type LTAInfo
    box Box
    array<LTARetestInfo> Retests

export type BreakoutInfo
    LevelInfo Level
    int BreakoutDirection
    int SortedLevelIndex
    LTAInfo LTA

export type HardcodedTickerValue
    string Tickers
    float Value

export type Styling
    color HeatmapColor1
    color HeatmapColor2
    color HeatmapColor3
    color HeatmapColor4

    color AlertTextColor

    color CurrentCandleBullish
    color CurrentCandleBearish

export type GapInfo
    bool IsBullish
    box Box

export isLevelInLimits(float price, float levelLimitsPts) =>
    low - syminfo.mintick * levelLimitsPts <= price and price <= high + syminfo.mintick * levelLimitsPts

export showNotificationFlag(string caption) =>
    isBullish = close > open
    label.new(bar_index, isBullish ? high : low, caption, style = isBullish ? label.style_label_down : label.style_label_up)
        
export getExistingCleanZone(int startTime, array<CleanZone> cleanZones) =>
    CleanZone existingCleanZone = na
    int existingIndex = na

    if(cleanZones.size() > 0)
        for i = cleanZones.size() - 1 to 0
            cleanZone = cleanZones.get(i)
            if(cleanZone.Box.get_left() == startTime)
                existingCleanZone := cleanZone
                existingIndex := i
                break
    
    [existingIndex, existingCleanZone]

export detectCleanZones(float _candleOpen, float _candleLow, float _candleHigh, float _candleClose, int minCleanZoneCandles, int maxCleanZoneCandles, array<CleanZone> cleanZones, float levelLimitsPts, color cleanZoneColor, bool cleanZoneAlert, bool showNotificationFlags) =>
    int continuationCandles = 0
    removedCleanZones = array.new<CleanZone>()

    if(barstate.isconfirmed)
        for i = 0 to maxCleanZoneCandles
            if(continuationCandles >= 0 and _candleClose[i] > _candleOpen[i])
                continuationCandles += 1
            else if(continuationCandles <= 0 and _candleClose[i] < _candleOpen[i])
                continuationCandles -= 1
            else
                break

        continuationCandles := math.abs(continuationCandles)

        if(continuationCandles >= minCleanZoneCandles)
            startTime = time[continuationCandles - 1]
            [existingIndex, existingCleanZone] = getExistingCleanZone(startTime, cleanZones)
            if(na(existingIndex))
                cleanZone = CleanZone.new(array.new_float())
                for i = continuationCandles - 1 to 0
                    cleanZone.Closes.push(_candleClose[i])
                cleanZone.Box := box.new(startTime, math.max(_candleClose[continuationCandles], _candleClose), time, math.min(_candleClose[continuationCandles], _candleClose), xloc = xloc.bar_time, bgcolor = cleanZoneColor, border_width = 0)
                cleanZone.IsBullish := _candleClose[continuationCandles] < _candleClose
                cleanZone.Open := _candleOpen[cleanZone.Closes.size() - 1]
                cleanZone.RawOpen := open[cleanZone.Closes.size() - 1]

                cleanZones.push(cleanZone)
    
                if(cleanZoneAlert)
                    cleanZoneAlertMessage = (cleanZone.IsBullish ? "Bullish" : "Bearish") + " clean zone #" + str.tostring(cleanZones.size()) + " created"
                     +  " from "
                     + (cleanZone.IsBullish ? str.tostring(cleanZone.Box.get_bottom()) : str.tostring(cleanZone.Box.get_top()))
                     +  " to "
                     + (cleanZone.IsBullish ? str.tostring(cleanZone.Box.get_top()) : str.tostring(cleanZone.Box.get_bottom()))
                     +  " (" + str.tostring((cleanZone.Box.get_top() - cleanZone.Box.get_bottom()) / syminfo.mintick) + "pts)"

                    alert(cleanZoneAlertMessage, alert.freq_once_per_bar)
                    if(showNotificationFlags)
                        showNotificationFlag(cleanZoneAlertMessage)
            else
                if(continuationCandles <= maxCleanZoneCandles)
                    existingCleanZone.Box.set_top(math.max(existingCleanZone.Open, _candleClose[continuationCandles], _candleClose))
                    existingCleanZone.Box.set_bottom(math.min(existingCleanZone.Open, _candleClose[continuationCandles], _candleClose))

        if(cleanZones.size() > 0)
            for i = cleanZones.size() - 1 to 0
                cleanZone = cleanZones.get(i)
                cleanZoneTop = cleanZone.Box.get_top()
                if(cleanZone.Box.get_bottom() <= _candleClose and _candleClose <= cleanZone.Box.get_top() and time[cleanZone.Closes.size()]  >= cleanZone.Box.get_left())
                    for j = cleanZone.Closes.size() - 1 to 0
                        if(cleanZone.IsBullish)
                            if(_candleClose < _candleOpen and cleanZone.Closes.get(j) >= _candleLow)
                                cleanZone.Closes.remove(j)
                        else
                            if(_candleClose > _candleOpen and cleanZone.Closes.get(j) <= _candleHigh)
                                cleanZone.Closes.remove(j)

                if(cleanZone.Closes.size() < minCleanZoneCandles)
                    removedCleanZones.push(cleanZone)
                    cleanZones.remove(i)
                else
                    if(isLevelInLimits(cleanZone.Box.get_top(), levelLimitsPts) or isLevelInLimits(cleanZone.Box.get_bottom(), levelLimitsPts))
                        cleanZone.Box.set_right(time_close)
    
    removedCleanZones

export getExistingCleanZone(float price, array<CleanZone> cleanZones) =>
    CleanZone existingCleanZone = na
    if(cleanZones.size() > 0)
        for i = cleanZones.size() - 1 to 0
            cleanZone = cleanZones.get(i)
            if(cleanZone.Box.get_bottom() < price and price < cleanZone.Box.get_top())
                existingCleanZone := cleanZone
                break
    existingCleanZone

export getTimeframeTitle(string timeframe, int direction = 0) =>
    string tfTitle =
     str.tonumber(timeframe) < 5 ? timeframe + "m" :
     str.tonumber(timeframe) < 60 ? timeframe + (direction == 0 ? "m" : "") :
     str.tonumber(timeframe) < 60 * 24 ? str.tostring(str.tonumber(timeframe) / 60) + (direction == 0 ? "h" : "") :
     timeframe == "1D" ? "D" :
     timeframe == "1W" ? "W" :
     timeframe == "1M" ? "M" :
     timeframe
    
    if(direction != 0)
        tfTitle := tfTitle + (direction == 1 ? "R" : "S")
    
    tfTitle

export pairingFunction(float a, float b) =>
    a + b

export getLast(array<float> arr) =>
    float last = na
    if(not na(arr))
        if(arr.size() > 0)
            last := arr.last()
    last

export getLast(array<int> arr) =>
    int last = na
    if(not na(arr))
        if(arr.size() > 0)
            last := arr.last()
    last

export isLastBar(string timeframe, bool isRecentBar, bool withBarConfirmed = false) =>
     isRecentBar ? (barstate.isconfirmed or not withBarConfirmed) and time_close(timeframe) == time_close : false

export getDayOfWeek(int t, string tzone) =>
    switch dayofweek(t, tzone)
        1 => "Sun"
        2 => "Mon"
        3 => "Tue"
        4 => "Wed"
        5 => "Thu"
        6 => "Fri"
        7 => "Sat"

export toTimeString(int t, bool includeDay = true, string tzone = "GMT+3", int hourDisplay = 1) =>
    str.format_time(math.floor(t / 60.0) * 60, (includeDay ? ((year(t) == year(timenow) or math.abs(timenow - t) < 1000 * 60 * 60 * 24 * 30 * 6) ? "dd/MMM" : "dd/MMM/yy") : "") + (hourDisplay == 2 ? " HH:mm" : (hourDisplay == 1 ? " HHmm" : "")), tzone)

export toFriendlyDateString(int t, string tzone) =>
    getDayOfWeek(t, tzone) + " " + str.format_time(t, "dd MMM ''yy  HH:mm", tzone) + " (" + tzone + ")"

export getResistance(float src, int leftBars, int rightBars) =>
    leftBars + rightBars == 0 ? na : math.round_to_mintick(ta.pivothigh(src, leftBars, rightBars))

export getSupport(float src, int leftBars, int rightBars) =>
    leftBars + rightBars == 0 ? na : math.round_to_mintick(ta.pivotlow(src, leftBars, rightBars))

export roundToInterval(float value, float roundingInterval, int roundingMethod = 0) =>
    roundingInterval == 0 ? value : roundingInterval == syminfo.mintick ? math.round_to_mintick(value) : (roundingMethod == -1 ? math.floor(value / roundingInterval) : roundingMethod == 1 ? math.ceil(value / roundingInterval) : math.round(value / roundingInterval)) * roundingInterval

getCurrentTFData(int index = 0) =>
    candleOpen = open[index]
    candleClose = close[index]
    candleLow = low[index]
    candleHigh = high[index]

    [
     candleOpen, 
     candleClose, 
     candleLow,
     candleHigh,
     getResistance(candleClose, 1, 1),
     getSupport(candleClose, 1, 1),
     time,
     time_close,
     ta.atr(100)
     ]

getHigherTFData(string tf) =>
    request.security(syminfo.tickerid, tf, getCurrentTFData(index = 0), lookahead = barmerge.lookahead_on)

getData(TimeframeInfo timeframeInfo, bool isRecentBar, bool withBarConfirmed = false) =>
    if(timeframe.period == timeframeInfo.Timeframe and barstate.isconfirmed and timeframeInfo.Enabled and isRecentBar)
        getCurrentTFData(0)
    else if (timeframeInfo.Enabled and isLastBar(timeframeInfo.Timeframe, isRecentBar, withBarConfirmed))
        getHigherTFData(timeframeInfo.Timeframe)

export updateTimeframeDetails(TimeframeInfo timeFrame, bool isRecentBar, bool withBarConfirmed = false, float roundingInterval = syminfo.mintick) =>
    [o, c, l, h, r, s, t, tc, atr] = getData(timeFrame, isRecentBar, withBarConfirmed)

    if(nz(getLast(timeFrame.TimeOpen)) != t)
        timeFrame.RawOpen.push(nz(getLast(timeFrame.RawClose), o))
        timeFrame.RawLow.push(l)
        timeFrame.RawHigh.push(h)
        timeFrame.RawClose.push(c)

        o := roundToInterval(o, roundingInterval)
        c := roundToInterval(c, roundingInterval)
        l := roundToInterval(l, roundingInterval)
        h := roundToInterval(h, roundingInterval)
        r := roundToInterval(r, roundingInterval)
        s := roundToInterval(s, roundingInterval)

        timeFrame.TimeOpen.push(t)
        timeFrame.TimeClose.push(tc)
        timeFrame.Open.push(nz(getLast(timeFrame.Close), o))
        timeFrame.Low.push(l)
        timeFrame.High.push(h)
        timeFrame.Close.push(c)
        timeFrame.IsClosing := true
        timeFrame.PivotHigh := r
        timeFrame.PivotLow := s
        timeFrame.Atr := atr
        
    if(timeFrame.Close.size() > 10)
        timeFrame.TimeOpen.shift()
        timeFrame.TimeClose.shift()
        timeFrame.Open.shift()
        timeFrame.High.shift()
        timeFrame.Low.shift()
        timeFrame.Close.shift()
        timeFrame.RawOpen.shift()
        timeFrame.RawHigh.shift()
        timeFrame.RawLow.shift()
        timeFrame.RawClose.shift()

export inSession(string sess, string timezone) => 
    t = barstate.isrealtime 
     ? time(timeframe.period, sess + ":1234567", timezone) 
     : time_close(timeframe.period, sess + ":1234567", timezone)
    na(t) == false

export isTimeAtOffset(int t, int offset, string timezone) =>
    offsetTime = timenow + offset * 60 * 1000
    str.tonumber(toTimeString(offsetTime, includeDay = false, tzone = timezone)) == t

export getDefaultPtsMultiplier() =>
    syminfo.type == "stock" ? 1
  : syminfo.type == "futures" ? 10
  : syminfo.type == "forex" ? 10
  : syminfo.type == "crypto" ? 10
  : syminfo.type == "fund" ? 1
  : syminfo.type == "cfd" ? 10
  : syminfo.type == "economic" ? 1
  : syminfo.type == "dr" ? 1 
  : 1

export getDefaultUnitsPerLot() =>
    syminfo.type == "stock" ? 1
  : syminfo.type == "futures" ? 1
  : syminfo.type == "forex" ? 100000
  : syminfo.type == "crypto" ? 1
  : syminfo.type == "fund" ? 1
  : syminfo.type == "cfd" ? 100
  : syminfo.type == "economic" ? 1
  : syminfo.type == "dr" ? 1 
  : 1

export getDefaultPsychologicalLevelsInterval() =>
    close >= 50000 ? 1000
  : close >= 30000 ? 500
  : close >= 10000 ? 100
  : close >= 5000 ? 50
  : close >= 3000 ? 10
  : close >= 1000 ? 5
  : close >= 300 ? 1
  : close >= 100 ? 0.5
  : close >= 50 ? 0.1
  : close >= 10 ? 0.05
  : close >= 5 ? 0.01
  : close >= 0.5 ? 0.005
  : 0.001

export getHardcodedTickerValue(array<HardcodedTickerValue> hardcodedTickerValues) =>
    float value = na
    for hardcodedTickerValue in hardcodedTickerValues
        for hardcodedTicker in str.split(hardcodedTickerValue.Tickers, ",")
            if(str.startswith(str.upper(syminfo.ticker), hardcodedTicker))
                value := hardcodedTickerValue.Value
    value

export getExistingPsychoLine(float psychoLevelPrice, array<line> psychoLevelLines, float levelLimitsPts) =>
    int existingPsychoLineIndex = na
    line existingPsychoLine = na

    if(psychoLevelLines.size() > 0)
        for i = psychoLevelLines.size() - 1 to 0
            psychoLevelLine = psychoLevelLines.get(i)
            psychoLevelY1 = psychoLevelLine.get_y1()

            if(not isLevelInLimits(psychoLevelY1, levelLimitsPts))
                psychoLevelLine.delete()
                psychoLevelLines.remove(i)
            else if(psychoLevelY1 == psychoLevelPrice)
                existingPsychoLineIndex := i
                existingPsychoLine := psychoLevelLine
                break
    
    [existingPsychoLineIndex, existingPsychoLine]

export drawPsychologicalLevels(float psychologicalLevelsInterval, array<line> psychoLevelLines, color psychologicalLevelsColor, float levelLimitsPts) =>
    fromPsychoLevel =  math.floor((close - syminfo.mintick * levelLimitsPts) / psychologicalLevelsInterval) * psychologicalLevelsInterval
    toPsychoLevel =  math.ceil((close + syminfo.mintick * levelLimitsPts) / psychologicalLevelsInterval) * psychologicalLevelsInterval
    
    for psychoLevelPrice = fromPsychoLevel to toPsychoLevel by psychologicalLevelsInterval
        [existingPsychoLineIndex, existingPsychoLine] = getExistingPsychoLine(psychoLevelPrice, psychoLevelLines, levelLimitsPts)
          
        if(na(existingPsychoLineIndex) and isLevelInLimits(psychoLevelPrice, levelLimitsPts))
            psychoLevelLines.push(line.new(time, psychoLevelPrice, time + 1, psychoLevelPrice, xloc = xloc.bar_time, extend = extend.both, color = psychologicalLevelsColor, style = line.style_dashed, width = 1))

export addLTA(int left, float top, int right, float bottom, color ltaFillColor, array<LTAInfo> ltas) =>
    LTAInfo lta = na
    ltaExists = false
    if(ltas.size() > 0)
        for i = ltas.size() - 1 to 0
            lta := ltas.get(i)
            if(lta.Box.get_top() == top or lta.Box.get_bottom() == bottom or lta.Box.get_top() == bottom or lta.Box.get_bottom() == top)
                lta.Box.set_right(right)
                ltaExists := true
                break

    if(not ltaExists)
        lta := LTAInfo.new()
        lta.Box := box.new(left, top, right, bottom, bgcolor = ltaFillColor, border_color = na, border_width = 0, text_size = size.small,  xloc = xloc.bar_time)
        ltas.push(lta)
        
    [ltaExists, lta]

export removeLTAInfoEntry(LTAInfo lta, int i, array<LTAInfo> ltas, bool removeOldLTAs, bool ltaAlert, bool showNotificationFlags, array<BreakoutInfo> levelBreakouts) =>
    lta.Box.set_right(time)
    ltas.remove(i)
    if(ltaAlert)
        caption = "LTA #" + str.tostring(i) + " invalidated between " + str.tostring(lta.Box.get_bottom(), format.mintick) + " - " + str.tostring(lta.Box.get_top(), format.mintick)
        alert(caption)
        if(showNotificationFlags)
            showNotificationFlag(caption)
    if(removeOldLTAs or lta.Box.get_left() == lta.Box.get_right())
        lta.Box.delete()
    
    for breakout in levelBreakouts
        breakout.LTA := lta

export removeLTAFromLevels(array<string> invalidatedLtaLevels, array<LTAInfo> ltas, bool removeOldLTAs, bool ltaAlert, bool showNotificationFlags, array<BreakoutInfo> levelBreakouts) =>
    if(ltas.size() > 0 and invalidatedLtaLevels.size() > 0)
        for i = ltas.size() - 1 to 0
            lta = ltas.get(i)
            top = math.max(lta.Box.get_top(), lta.Box.get_bottom())
            btm = math.min(lta.Box.get_top(), lta.Box.get_bottom())
            if(invalidatedLtaLevels.includes(str.tostring(top) + "-1") or invalidatedLtaLevels.includes(str.tostring(top) + "-" + str.tostring(btm) + "-1") or invalidatedLtaLevels.includes(str.tostring(top)) or invalidatedLtaLevels.includes(str.tostring(btm)))
                removeLTAInfoEntry(lta, i, ltas, removeOldLTAs, ltaAlert, showNotificationFlags, levelBreakouts)
                break

export showGaps(array<GapInfo> gaps, color gapsColor, int currentTFDuration, bool hideOldGaps, float levelLimitsPts) =>
    float gapFrom = na
    float gapTo = na
    gapFrom := math.min(open, close[1])
    gapTo := math.max(open, close[1])
    if(gapFrom != gapTo)
        gaps.push(GapInfo.new(open > close[1], box.new(time, gapTo, time, gapFrom, border_width = 0, bgcolor = gapsColor, xloc = xloc.bar_time)))
    
    if(gaps.size() > 0)
        for i = gaps.size() - 1 to 0
            gap = gaps.get(i)
    
            float gapBoxTop = gap.Box.get_top()
            float gapBoxBtm = gap.Box.get_bottom()
            int gapBoxLeft = gap.Box.get_left()

            isValid = isLevelInLimits(gapBoxTop, levelLimitsPts) or isLevelInLimits(gapBoxBtm, levelLimitsPts) or (high >= gapBoxBtm and low <= gapBoxTop) or gapBoxLeft > time[100]

            if(isValid and (low <= gapBoxBtm and gapBoxBtm <= high))
                if(high >= gapBoxTop)
                    if(time - gapBoxLeft <= currentTFDuration or hideOldGaps)
                        gap.Box.delete()
                        gaps.remove(i)
                    else
                        gap.Box.set_right(time)
                        gaps.remove(i)
                    isValid := false
                else
                    gap.Box.set_bottom(high)
            
            if(isValid and (low <= gapBoxTop and gapBoxTop <= high))
                if(low <= gapBoxBtm)
                    if(time - gapBoxLeft <= currentTFDuration or hideOldGaps)
                        gap.Box.delete()
                        gaps.remove(i)
                    else
                        gap.Box.set_right(time)
                        gaps.remove(i)
                    isValid := false
                else
                    gap.Box.set_top(low)
            
            if(isValid and (gapBoxBtm <= low and high <= gapBoxTop))
                if(gap.IsBullish)
                    gap.Box.set_top(low)
                else
                    gap.Box.set_bottom(high)

            if(isValid)
                gap.Box.set_right(time + currentTFDuration * 10)

export depictBiasCell(table biasTable, int col, int row, float price, color color, bool showBiasDepictionPrices, string depictionRowSize) =>
    if(row > 0)
        biasTable.cell(col, row + 1, showBiasDepictionPrices and not na(price) ? str.tostring(price, format.mintick) : " ", bgcolor = color, text_size = depictionRowSize, text_color = showBiasDepictionPrices and not na(price) ?color.white : color.black)

export getDepictionRow(float price, float biasMin, float biasMax, int depictionLevels) =>
    biasLimits = biasMax - biasMin
    priceDifference = price - biasMin
    math.round(depictionLevels - priceDifference * depictionLevels / biasLimits) + 1

export depictBiasCandle(table biasTable, int col, TimeframeInfo biasTf, int depictionLevels, float biasMin, float biasMax, bool showBiasDepictionPrices, string depictionRowSize) =>
    biasTfOpen = getLast(biasTf.Open)
    biasTfClose = getLast(biasTf.Close)

    depictionOpen = getDepictionRow(biasTfOpen, biasMin, biasMax, depictionLevels)
    depictionClose = getDepictionRow(biasTfClose, biasMin, biasMax, depictionLevels)
    depictionMin = math.min(depictionOpen, depictionClose)
    depictionMax = math.max(depictionOpen, depictionClose)
    directionColor = biasTfOpen < biasTfClose ? color.rgb(0, 137, 123, 43) : color.rgb(255, 82, 82, 39)

    for i = 1 to depictionLevels + 1
        if(depictionMin < i and i < depictionMax)
            depictBiasCell(biasTable, col, i, na, directionColor, showBiasDepictionPrices, depictionRowSize)
        else
            depictBiasCell(biasTable, col, i, na, na, showBiasDepictionPrices, depictionRowSize)

    depictBiasCell(biasTable, col, depictionOpen, biasTfOpen, directionColor, showBiasDepictionPrices, depictionRowSize)
    depictBiasCell(biasTable, col, depictionClose, biasTfClose, directionColor, showBiasDepictionPrices, depictionRowSize)


drawSessionLine(line sessionLine, int t, color c, int currentTfDuration, line fillFromLine = na) =>
    isNew = na(sessionLine)
    if(not isNew)
        isNew := sessionLine.get_x1() < t - currentTfDuration * 2

    l = sessionLine
    if(isNew)
        l := line.new(t, high, t, low, width = 3, color = c, extend = extend.both, style = line.style_dotted, xloc = xloc.bar_time)
        if(not na(fillFromLine))
            linefill.new(fillFromLine, l, c)

    l

export isInSession(string session1, string session2, string session3, string session4, bool enableSession1, bool enableSession2, bool enableSession3, bool enableSession4, bool isRecentBar, string timezone, int currentTfDuration, color color1, color color2, color color3, color color4, bool fill1, bool fill2, bool fill3, bool fill4, int futurePeriod, int futureBars) =>
    var session1Start = math.round(str.tonumber(str.split(session1, "-").get(0)))
    var session1End = math.round(str.tonumber(str.split(session1, "-").get(1)))

    var session2Start = math.round(str.tonumber(str.split(session2, "-").get(0)))
    var session2End = math.round(str.tonumber(str.split(session2, "-").get(1)))

    var session3Start = math.round(str.tonumber(str.split(session3, "-").get(0)))
    var session3End = math.round(str.tonumber(str.split(session3, "-").get(1)))

    var session4Start = math.round(str.tonumber(str.split(session4, "-").get(0)))
    var session4End = math.round( str.tonumber(str.split(session4, "-").get(1)))

    pEnableSession1 = enableSession1
    pEnableSession2 = enableSession2
    pEnableSession3 = enableSession3
    pEnableSession4 = enableSession4

    var line sessionStartL1 = na
    var line sessionEndL1 = na

    var line sessionStartL2 = na
    var line sessionEndL2 = na

    var line sessionStartL3 = na
    var line sessionEndL3 = na

    var line sessionStartL4 = na
    var line sessionEndL4 = na

    if((pEnableSession1 or pEnableSession2 or pEnableSession3 or pEnableSession4) and isRecentBar)
        t = time
        i = 0
        while(t < futurePeriod or i < futureBars)
            prevTimeNumber = str.tonumber(toTimeString(t - currentTfDuration, includeDay = false, tzone = timezone))
            timeNumber = str.tonumber(toTimeString(t, includeDay = false, tzone = timezone))

            sessionStartL1 := pEnableSession1 and timeNumber >= session1Start and prevTimeNumber < session1Start ? drawSessionLine(sessionStartL1, t, color1, currentTfDuration) : sessionStartL1
            sessionEndL1 := pEnableSession1 and timeNumber >= session1End and prevTimeNumber < session1End ? drawSessionLine(sessionEndL1, t - currentTfDuration, color1, currentTfDuration, fill1 ? sessionStartL1 : na) : sessionEndL1
            sessionStartL2 := pEnableSession2 and timeNumber >= session2Start and prevTimeNumber < session2Start ? drawSessionLine(sessionStartL2, t, color2, currentTfDuration) : sessionStartL2
            sessionEndL2 := pEnableSession2 and timeNumber >= session2End and prevTimeNumber < session2End ? drawSessionLine(sessionEndL2, t - currentTfDuration, color2, currentTfDuration, fill2 ? sessionStartL2 : na) : sessionEndL2
            sessionStartL3 := pEnableSession3 and timeNumber >= session3Start and prevTimeNumber < session3Start ? drawSessionLine(sessionStartL3, t, color3, currentTfDuration) : sessionStartL3
            sessionEndL3 := pEnableSession3 and timeNumber >= session3End and prevTimeNumber < session3End ? drawSessionLine(sessionEndL3, t - currentTfDuration, color3, currentTfDuration, fill3 ? sessionStartL3 : na) : sessionEndL3
            sessionStartL4 := pEnableSession4 and timeNumber >= session4Start and prevTimeNumber < session4Start ? drawSessionLine(sessionStartL4, t, color4, currentTfDuration) : sessionStartL4
            sessionEndL4 := pEnableSession4 and timeNumber >= session4End and prevTimeNumber < session4End ? drawSessionLine(sessionEndL4, t - currentTfDuration, color4, currentTfDuration, fill4 ? sessionStartL4 : na) : sessionEndL4
            t := t + currentTfDuration
            i := i + 1

    if(na(sessionStartL1) and na(sessionEndL1))
        pEnableSession1 := false

    if(na(sessionStartL2) and na(sessionEndL2))
        pEnableSession2 := false
        
    if(na(sessionStartL3) and na(sessionEndL3))
        pEnableSession3 := false
        
    if(na(sessionStartL4) and na(sessionEndL4))
        pEnableSession4 := false

    isInSession1 = pEnableSession1 ? inSession(session1, timezone) : false
    isInSession2 = pEnableSession2 ? inSession(session2, timezone) : false
    isInSession3 = pEnableSession3 ? inSession(session3, timezone) : false
    isInSession4 = pEnableSession4 ? inSession(session4, timezone) : false
    isInSession = (pEnableSession1 or pEnableSession2 or pEnableSession3 or pEnableSession4) ? (isInSession1 or isInSession2 or isInSession3 or isInSession4) : true
    [isInSession, isInSession1, isInSession2, isInSession3, isInSession4]

export raiseCustomBreakoutAlerts(string customClosureNotificationLevels, float proximitySize, int proximityTime, array<box> alertLevelBoxes, bool showNotificationFlags, color alertTextColor, bool canRaise, float candleOpen, float candleClose) =>
    alertLevelPrices = str.split(customClosureNotificationLevels, "\n")
    alertSet = false
    for [i, rawLevel] in alertLevelPrices
        if(rawLevel == "")
            continue
        rawLevelPrice = str.split(rawLevel, " ").get(0)
        rawLevelInfo = str.replace(rawLevel, rawLevelPrice + " ", "")
        if(rawLevel == rawLevelInfo)
            rawLevelInfo := ""

        alertLevelPrice = rawLevelPrice != "" ? str.tonumber(rawLevelPrice) : na

        if(barstate.islastconfirmedhistory and alertLevelBoxes.size() <= i)
            alertLevelBoxes.push(box.new(bar_index + 5, alertLevelPrice - proximitySize, bar_index + 6, alertLevelPrice + proximitySize, extend = extend.right, border_style = line.style_dashed, border_width = 1, border_color = color.rgb(12, 178, 255), bgcolor = color.rgb(12, 178, 255, 90), text = "⏰ " + rawLevelInfo, text_color = alertTextColor, text_size = size.small, text_halign = text.align_left))
        
        if(canRaise)
            closingAbove = candleOpen < alertLevelPrice - proximitySize * 0.1 and alertLevelPrice - proximitySize * 0.6 < candleClose and candleClose < alertLevelPrice + proximitySize * 0.6
            closingBelow = candleOpen > alertLevelPrice + proximitySize * 0.1 and alertLevelPrice + proximitySize * 0.6 > candleClose and candleClose > alertLevelPrice - proximitySize * 0.6

            closedAbove = candleOpen < alertLevelPrice - proximitySize * 0.1 and alertLevelPrice < candleClose and candleClose < alertLevelPrice + proximitySize
            closedBelow = candleOpen > alertLevelPrice + proximitySize * 0.1 and alertLevelPrice > candleClose and candleClose > alertLevelPrice - proximitySize

            inProximityTime = timenow + proximityTime > time_close
        
            if(barstate.isconfirmed and (closedAbove or closedBelow))
                alertSet := true
                caption = getTimeframeTitle(timeframe.period) + " candle closed " + (closedAbove ? "above" : "below") + " " + rawLevelPrice + " | " + rawLevelInfo
                alert(caption, alert.freq_once_per_bar_close)
                    
                if(showNotificationFlags)
                    showNotificationFlag(caption)

            else if(not barstate.isconfirmed and inProximityTime and (closingAbove or closingBelow))
                alertSet := true
                caption = getTimeframeTitle(timeframe.period) + " candle approaching " + rawLevelPrice + " and closing in " + (proximityTime >= 60000 ? (str.tostring(proximityTime / 60000) + " min") : (str.tostring(proximityTime / 1000) + " sec")) + " | " + rawLevelInfo
                alert(caption, alert.freq_once_per_bar)
                    
                if(showNotificationFlags)
                    showNotificationFlag(caption)
    alertSet

export hideLevel(LevelInfo level, map<string, TimeframeInfo> timeframes, bool showLines, bool isRecentBar) =>
    hidden = false
    if(not level.IsHidden)
        levelTimeframeInfo = timeframes.get(level.Timeframe)
        level.IsHidden := true
        if(showLines)
            levelLine = levelTimeframeInfo.Lines.size() > level.LineIndex ? levelTimeframeInfo.Lines.get(level.LineIndex) : na
            if(not na(levelLine) and isRecentBar)
                levelLine.set_x2(levelLine.get_x1())
                levelLabel = levelTimeframeInfo.Labels.get(level.LineIndex)
                levelLabel.set_x(na)
            
            hidden := true
    hidden

export getTimeframeRatioFactor(TimeframeInfo timeframeInfo) =>
    float diffRatio = timeframeInfo.Duration / (timeframe.in_seconds() * 1000)
    diffRatio := 
     diffRatio >= 40000 ? 22 :
     diffRatio >= 5000 ? 21 :
     diffRatio >= 1000 ? 20 :
     diffRatio >= 240 ? 19 :
     diffRatio >= 90 ? 18 :
     diffRatio >= 40 ? 17 :
     diffRatio >= 20 ? 16 :
     diffRatio >= 12 ? 15 :
     diffRatio >= 8 ? 14 :
     diffRatio >= 4 ? 13 :
     diffRatio >= 2 ? 12 :
     10

export MinickToInt(float minTick) => 
 minTick >= 0.1 ? 1 : minTick == 0.01 ? 10 : minTick == 0.001 ? 100 : 
 minTick == 0.0001 ? 1000 : minTick == 0.00001 ? 10000 : minTick == 0.000001 ? 100000 : 
 minTick == 0.0000001 ? 1000000 : minTick == 0.00000001 ? 10000000 : na


export getParamOverrideValue(simple string paramName, string overridenValues) =>
    string paramValue = na
    if(str.contains(overridenValues, "#"))
        var parts = str.split(overridenValues, "#")
        if(parts.first() == "Override")
            for [i, part] in parts
                if(part == paramName)
                    paramValue := parts.get(i + 1)
                    break
    paramValue

export overrideOrDefault(simple string paramName, string default, string overridenValues, bool firstRun) =>
    if(firstRun)
        string paramValue = getParamOverrideValue(paramName, overridenValues)
        if(not na(paramValue))
            if(paramValue == "-")
                paramValue := ""
            paramValue
        else
            default
    else
        default

export overrideOrDefault(simple string paramName, float default, string overridenValues, bool firstRun) =>
    if(firstRun)
        string paramValue = getParamOverrideValue(paramName, overridenValues)
        if(not na(paramValue))
            str.tonumber(paramValue)
        else
            default
    else
        default

export overrideOrDefault(simple string paramName, int default, string overridenValues, bool firstRun) =>
    if(firstRun)
        string paramValue = getParamOverrideValue(paramName, overridenValues)
        if(not na(paramValue))
            math.round(str.tonumber(paramValue))
        else
            default
    else
        default

export overrideOrDefault(simple string paramName, color default, string overridenValues, bool firstRun) =>
    if(firstRun)
        string paramValue = getParamOverrideValue(paramName, overridenValues)
        if(not na(paramValue))
            rgbt = str.split(paramValue, " ")
            color.rgb(str.tonumber(rgbt.get(0)), str.tonumber(rgbt.get(1)), str.tonumber(rgbt.get(2)), str.tonumber(rgbt.get(3)))
        else
            default
    else
        default

export overrideOrDefault(simple string paramName, bool default, string overridenValues, bool firstRun) =>
    if(firstRun)
        string paramValue = getParamOverrideValue(paramName, overridenValues)
        if(not na(paramValue))
            paramValue == "true"
        else
            default
    else
        default
