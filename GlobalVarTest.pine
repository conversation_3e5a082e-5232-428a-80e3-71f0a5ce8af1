//@version=6
indicator("Global Variable Modification Test", overlay=true)

// 1. REPLICATE THE PROBLEM SETUP
// A simple custom type to mimic the SnrLevel UDT
type TestType
    bool flag

// Global variables to hold the script's state
var TestType test_level = TestType.new(false)
var string test_status = "Initial"
var int test_time = 0

// 2. IMPLEMENT THE FIX
// This function takes the current state as parameters,
// performs its logic, and returns the new state as a tuple.
modifyGlobals_fixed(current_status, current_time, current_level) =>
    // Perform logic to determine the new state
    new_status = current_status == "Initial" ? "Modified" : "Reset"
    new_time = current_time + 1
    
    // To modify the UDT, create a new instance with the changed value
    TestType new_level = TestType.new(not current_level.flag)

    // Return all new values in a tuple
    [new_status, new_time, new_level]

// 3. DEMONSTRATE THE SOLUTION
// We only run this logic once to see the change
if barstate.isfirst
    // Display initial state
    label.new(bar_index, high, 
     "Initial State:\n" + 
     "Status: " + test_status + "\n" +
     "Time: " + str.tostring(test_time) + "\n" +
     "Flag: " + str.tostring(test_level.flag),
     yloc=yloc.abovebar, style=label.style_label_down)

    // Call the function and assign the returned tuple to the global variables
    [test_status, test_time, test_level] = modifyGlobals_fixed(test_status, test_time, test_level)

// On the last bar, display the final state to confirm the modification
if barstate.islast
    label.new(bar_index, low,
     "Final State:\n" +
     "Status: " + test_status + "\n" +
     "Time: " + str.tostring(test_time) + "\n" +
     "Flag: " + str.tostring(test_level.flag),
     yloc=yloc.belowbar, style=label.style_label_up, textcolor=color.white, color=color.green)
