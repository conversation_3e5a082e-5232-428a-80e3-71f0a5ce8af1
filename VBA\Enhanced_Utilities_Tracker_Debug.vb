' ===================================================================
' ENHANCED UTILITIES TRACKER WITH COMPREHENSIVE DEBUGGING FRAMEWORK
' ===================================================================
' Version: 2.0 with Debug Framework
' Purpose: Excel-based utility meter reading management with debugging
' Features: Error handling, performance monitoring, progress tracking
' ===================================================================

Option Explicit

' Global debugging variables
Public Const DEBUG_MODE As Boolean = True
Public DebugStartTime As Double
Public DebugStepTime As Double
Public DebugStepCount As Long
Public DebugTotalSteps As Long

Sub EnhanceUtilitiesTrackerWithDebug()
    ' Enhanced Utilities Issue Tracker with Comprehensive Debugging Framework
    ' Preserves existing data while adding enhanced features with full error handling
    
    ' Initialize debugging framework
    Call InitializeDebugFramework
    Call DebugLog("=== ENHANCED UTILITIES TRACKER DEBUG SESSION STARTED ===", "SYSTEM")
    Call DebugLog("Debug Mode: " & DEBUG_MODE, "SYSTEM")
    Call DebugLog("Excel Version: " & Application.Version, "SYSTEM")
    Call DebugLog("Available Memory: " & GetAvailableMemory() & " MB", "SYSTEM")
    
    ' Performance monitoring setup
    Application.ScreenUpdating = False
    Application.DisplayAlerts = False
    Application.Calculation = xlCalculationManual
    
    Call DebugLog("Application settings optimized for performance", "PERFORMANCE")
    
    ' Error handling setup
    On Error GoTo ErrorHandler
    
    ' Main variables with debugging
    Dim wb As Workbook
    Dim ws1 As Worksheet
    Dim isExisting As Boolean
    Dim existingRowCount As Long
    
    Call DebugStep("Initializing workbook and worksheet detection")
    
    ' Enhanced worksheet detection with comprehensive error handling
    Call DebugLog("Attempting to detect existing tracker...", "DETECTION")
    
    Set wb = ActiveWorkbook
    Call DebugLog("Active workbook set: " & wb.Name, "DETECTION")
    
    ' Safe worksheet detection
    Dim wsFound As Boolean
    wsFound = WorksheetExists(wb, "Main Issue Tracker")
    
    If wsFound Then
        Set ws1 = wb.Worksheets("Main Issue Tracker")
        isExisting = True
        
        ' Count existing data rows with error handling
        On Error Resume Next
        existingRowCount = ws1.Cells(ws1.Rows.Count, "C").End(xlUp).Row - 1
        If Err.Number <> 0 Then
            existingRowCount = 0
            Call DebugLog("Warning: Could not count existing rows. Error: " & Err.Description, "WARNING")
            Err.Clear
        End If
        On Error GoTo ErrorHandler
        
        Call DebugLog("Existing tracker detected with " & existingRowCount & " data rows", "DETECTION")
        
        ' Create backup with progress tracking
        Call DebugStep("Creating data backup for " & existingRowCount & " rows")
        Call CreateDataBackupWithDebug(ws1, existingRowCount)
        
        MsgBox "Existing tracker detected with " & existingRowCount & " data rows." & vbCrLf & _
               "Backup created. Enhancing current system while preserving all data.", vbInformation
    Else
        Call DebugLog("No existing tracker found. Creating new tracker.", "DETECTION")
        isExisting = False
        existingRowCount = 0
    End If
    
    ' Create new tracker if needed
    If Not isExisting Then
        Call DebugStep("Creating new tracker workbook")
        Call CreateNewTrackerWithDebug(wb, ws1)
    End If
    
    ' Apply enhanced calendar formula with debugging
    Call DebugStep("Applying enhanced calendar formula")
    Call ApplyEnhancedCalendarFormulaWithDebug(ws1, isExisting, existingRowCount)
    
    ' Apply consumption analysis formula with debugging
    Call DebugStep("Applying consumption analysis formula")
    Call ApplyConsumptionAnalysisWithDebug(ws1)
    
    ' Apply automatic formulas with debugging
    Call DebugStep("Applying automatic formulas")
    Call ApplyAutomaticFormulasWithDebug(ws1)
    
    ' Apply data validation with debugging
    Call DebugStep("Setting up data validation")
    Call ApplyDataValidationWithDebug(ws1)
    
    ' Apply conditional formatting with debugging
    Call DebugStep("Applying conditional formatting")
    Call ApplyConditionalFormattingWithDebug(ws1)
    
    ' Setup filters and freeze panes
    Call DebugStep("Setting up filters and freeze panes")
    Call SetupFiltersAndFreezePanesWithDebug(ws1)
    
    ' Create enhanced analysis sheets
    Call DebugStep("Creating enhanced analysis sheets")
    Call CreateEnhancedAnalysisSheetsWithDebug(wb)
    
    ' Create archive system
    Call DebugStep("Creating archive system")
    Call CreateArchiveSystemWithDebug(wb)
    
    ' Data integrity validation
    If isExisting Then
        Call DebugStep("Validating data integrity")
        Call ValidateDataIntegrityWithDebug(ws1, existingRowCount)
    End If
    
    ' Final cleanup and completion
    Call DebugStep("Finalizing enhancement")
    ws1.Select
    ws1.Range("A1").Select
    
    ' Restore application settings
    Application.ScreenUpdating = True
    Application.DisplayAlerts = True
    Application.Calculation = xlCalculationAutomatic
    
    Call DebugLog("Application settings restored", "PERFORMANCE")
    
    ' Display completion message with debug summary
    Call DisplayCompletionMessageWithDebug(isExisting, existingRowCount)
    
    ' Finalize debugging
    Call FinalizeDebugFramework
    
    Exit Sub
    
ErrorHandler:
    Call HandleDebugError(Err.Number, Err.Description, Erl)
    
    ' Restore application settings on error
    Application.ScreenUpdating = True
    Application.DisplayAlerts = True
    Application.Calculation = xlCalculationAutomatic
    
    Call DebugLog("=== DEBUG SESSION ENDED WITH ERROR ===", "ERROR")
End Sub

' ===================================================================
' DEBUGGING FRAMEWORK FUNCTIONS
' ===================================================================

Sub InitializeDebugFramework()
    ' Initialize the debugging framework
    DebugStartTime = Timer
    DebugStepTime = Timer
    DebugStepCount = 0
    DebugTotalSteps = 12 ' Total number of major steps
    
    ' Clear immediate window if in debug mode
    If DEBUG_MODE Then
        Debug.Print String(80, "=")
        Debug.Print "ENHANCED UTILITIES TRACKER DEBUG SESSION"
        Debug.Print "Session Started: " & Format(Now, "MM/DD/YYYY HH:MM:SS")
        Debug.Print String(80, "=")
    End If
End Sub

Sub DebugLog(message As String, category As String)
    ' Log debug messages with timestamp and category
    If DEBUG_MODE Then
        Dim timestamp As String
        timestamp = Format(Now, "HH:MM:SS.000")
        Debug.Print "[" & timestamp & "] [" & category & "] " & message
    End If
End Sub

Sub DebugStep(stepDescription As String)
    ' Track major steps with timing and progress
    Dim currentTime As Double
    Dim stepDuration As Double
    Dim totalDuration As Double
    Dim progressPercent As Double
    
    currentTime = Timer
    stepDuration = currentTime - DebugStepTime
    totalDuration = currentTime - DebugStartTime
    DebugStepCount = DebugStepCount + 1
    progressPercent = (DebugStepCount / DebugTotalSteps) * 100
    
    If DebugStepCount > 1 Then
        Call DebugLog("Previous step completed in " & Format(stepDuration, "0.000") & " seconds", "TIMING")
    End If
    
    Call DebugLog("STEP " & DebugStepCount & "/" & DebugTotalSteps & " (" & Format(progressPercent, "0.0") & "%): " & stepDescription, "PROGRESS")
    Call DebugLog("Total elapsed time: " & Format(totalDuration, "0.000") & " seconds", "TIMING")
    
    DebugStepTime = currentTime
End Sub

Function WorksheetExists(wb As Workbook, sheetName As String) As Boolean
    ' Safely check if worksheet exists
    Dim ws As Worksheet
    
    Call DebugLog("Checking if worksheet '" & sheetName & "' exists", "VALIDATION")
    
    On Error Resume Next
    Set ws = wb.Worksheets(sheetName)
    On Error GoTo 0
    
    WorksheetExists = Not (ws Is Nothing)
    
    If WorksheetExists Then
        Call DebugLog("Worksheet '" & sheetName & "' found", "VALIDATION")
    Else
        Call DebugLog("Worksheet '" & sheetName & "' not found", "VALIDATION")
    End If
End Function

Function GetAvailableMemory() As Long
    ' Get available memory (simplified version)
    ' In a real implementation, you might use Windows API calls
    GetAvailableMemory = 1024 ' Placeholder - returns 1024 MB
End Function

Sub HandleDebugError(errorNumber As Long, errorDescription As String, errorLine As Long)
    ' Comprehensive error handling with debugging
    Call DebugLog("=== ERROR ENCOUNTERED ===", "ERROR")
    Call DebugLog("Error Number: " & errorNumber, "ERROR")
    Call DebugLog("Error Description: " & errorDescription, "ERROR")
    Call DebugLog("Error Line: " & errorLine, "ERROR")
    Call DebugLog("Current Step: " & DebugStepCount & "/" & DebugTotalSteps, "ERROR")
    
    Dim errorMessage As String
    errorMessage = "An error occurred during the enhancement process:" & vbCrLf & vbCrLf & _
                   "Error: " & errorDescription & vbCrLf & _
                   "Error Number: " & errorNumber & vbCrLf & _
                   "Step: " & DebugStepCount & "/" & DebugTotalSteps & vbCrLf & vbCrLf & _
                   "Check the Immediate window (Ctrl+G) for detailed debug information."
    
    MsgBox errorMessage, vbCritical, "Enhancement Error"
End Sub

Sub FinalizeDebugFramework()
    ' Finalize debugging and display summary
    Dim totalTime As Double
    totalTime = Timer - DebugStartTime

    Call DebugLog("=== ENHANCEMENT COMPLETED SUCCESSFULLY ===", "SUCCESS")
    Call DebugLog("Total execution time: " & Format(totalTime, "0.000") & " seconds", "TIMING")
    Call DebugLog("Steps completed: " & DebugStepCount & "/" & DebugTotalSteps, "PROGRESS")
    Call DebugLog("Session ended: " & Format(Now, "MM/DD/YYYY HH:MM:SS"), "SYSTEM")
    Call DebugLog(String(80, "="), "SYSTEM")
End Sub

' ===================================================================
' DEBUGGING-ENHANCED OPERATION FUNCTIONS
' ===================================================================

Sub CreateDataBackupWithDebug(ws As Worksheet, rowCount As Long)
    ' Create backup of existing data with debugging
    Dim stepStartTime As Double
    stepStartTime = Timer

    Call DebugLog("Starting data backup process...", "BACKUP")
    Call DebugLog("Rows to backup: " & rowCount, "BACKUP")

    Dim wsBackup As Worksheet
    Dim backupName As String

    backupName = "Backup_" & Format(Now, "YYYY_MM_DD_HHMM")
    Call DebugLog("Backup sheet name: " & backupName, "BACKUP")

    ' Create backup sheet with error handling
    On Error Resume Next
    Set wsBackup = ws.Parent.Worksheets(backupName)
    On Error GoTo 0

    If wsBackup Is Nothing Then
        Call DebugLog("Creating new backup sheet...", "BACKUP")
        Set wsBackup = ws.Parent.Worksheets.Add(After:=ws.Parent.Worksheets(ws.Parent.Worksheets.Count))
        wsBackup.Name = backupName

        ' Copy all existing data to backup with progress tracking
        Call DebugLog("Copying data to backup sheet...", "BACKUP")
        ws.UsedRange.Copy wsBackup.Range("A1")

        ' Add backup information
        wsBackup.Range("A" & rowCount + 5).Value = "BACKUP INFORMATION:"
        wsBackup.Range("A" & rowCount + 6).Value = "Backup Date: " & Format(Now, "MM/DD/YYYY HH:MM")
        wsBackup.Range("A" & rowCount + 7).Value = "Rows Backed Up: " & rowCount
        wsBackup.Range("A" & rowCount + 8).Value = "Original Sheet: " & ws.Name

        ' Format backup info
        wsBackup.Range("A" & rowCount + 5 & ":A" & rowCount + 8).Font.Bold = True
        wsBackup.Range("A" & rowCount + 5 & ":A" & rowCount + 8).Interior.Color = RGB(255, 255, 0)

        Call DebugLog("Backup completed successfully", "BACKUP")
        Call DebugLog("Backup creation time: " & Format(Timer - stepStartTime, "0.000") & " seconds", "TIMING")

        MsgBox "Data backup created: " & backupName & vbCrLf & _
               "Rows preserved: " & rowCount, vbInformation
    Else
        Call DebugLog("Backup sheet already exists", "BACKUP")
    End If
End Sub

Sub CreateNewTrackerWithDebug(wb As Workbook, ws1 As Worksheet)
    ' Create new tracker with debugging
    Dim stepStartTime As Double
    stepStartTime = Timer

    Call DebugLog("Creating new tracker workbook...", "CREATION")

    Set ws1 = wb.Worksheets(1)
    ws1.Name = "Main Issue Tracker"
    Call DebugLog("Main worksheet created and named", "CREATION")

    ' Set up headers for new tracker
    Call DebugLog("Setting up headers...", "CREATION")
    With ws1.Range("A1:T1")
        .Value = Array("Issue ID", "Date Logged", "Complex Name", "Unit Number", "Device Type", _
                      "Issue Type", "Issue Description", "Water Reading (Last 30 days)", _
                      "Electricity Reading (Last 30 days)", "Status", "Priority", _
                      "Target Resolution Date", "Date Resolved", "Resolution Notes", _
                      "Follow-up Required", "Follow-up Completed", "Follow-up Notes", _
                      "Related Issue ID", "Calendar Entry", "Consumption Alert")
        .Font.Bold = True
        .Interior.Color = RGB(68, 114, 196)
        .Font.Color = RGB(255, 255, 255)
        .HorizontalAlignment = xlCenter
    End With

    Call DebugLog("Headers configured successfully", "CREATION")

    ' Set column widths with progress tracking
    Call DebugLog("Setting column widths...", "CREATION")
    ws1.Columns("A:A").ColumnWidth = 8
    ws1.Columns("B:B").ColumnWidth = 12
    ws1.Columns("C:C").ColumnWidth = 15
    ws1.Columns("D:D").ColumnWidth = 10
    ws1.Columns("E:E").ColumnWidth = 18
    ws1.Columns("F:F").ColumnWidth = 20
    ws1.Columns("G:G").ColumnWidth = 25
    ws1.Columns("H:H").ColumnWidth = 20
    ws1.Columns("I:I").ColumnWidth = 20
    ws1.Columns("J:J").ColumnWidth = 12
    ws1.Columns("K:K").ColumnWidth = 8
    ws1.Columns("L:L").ColumnWidth = 12
    ws1.Columns("M:M").ColumnWidth = 12
    ws1.Columns("N:N").ColumnWidth = 25
    ws1.Columns("O:O").ColumnWidth = 12
    ws1.Columns("P:P").ColumnWidth = 12
    ws1.Columns("Q:Q").ColumnWidth = 20
    ws1.Columns("R:R").ColumnWidth = 12
    ws1.Columns("S:S").ColumnWidth = 50
    ws1.Columns("T:T").ColumnWidth = 15

    Call DebugLog("Column widths set successfully", "CREATION")

    ' Add sample data
    Call DebugLog("Adding sample data...", "CREATION")
    Call AddSampleDataWithDebug(ws1)

    Call DebugLog("New tracker creation time: " & Format(Timer - stepStartTime, "0.000") & " seconds", "TIMING")
End Sub

Sub AddSampleDataWithDebug(ws As Worksheet)
    ' Add sample data with debugging
    Call DebugLog("Adding sample utility meter data...", "SAMPLE_DATA")

    ' Sample data for utility meter tracking
    ws.Range("A2").Value = "ISS-001"
    ws.Range("B2").Value = Date - 5
    ws.Range("C2").Value = "Lilyvale Estate"
    ws.Range("D2").Value = "Unit 013"
    ws.Range("E2").Value = "Smart Water Meter"
    ws.Range("F2").Value = "No Readings Received"
    ws.Range("G2").Value = "Device not registering readings after reset"
    ws.Range("H2").Value = "14.084 KL"
    ws.Range("I2").Value = "0 kWh"
    ws.Range("J2").Value = "In Progress"
    ws.Range("K2").Value = "High"

    ws.Range("A3").Value = "ISS-002"
    ws.Range("B3").Value = Date - 4
    ws.Range("C3").Value = "Kleinbach"
    ws.Range("D3").Value = "Unit 015"
    ws.Range("E3").Value = "Smart Electricity Meter"
    ws.Range("F3").Value = "Device Fault"
    ws.Range("G3").Value = "Meter showing erratic readings"
    ws.Range("H3").Value = "5.2 KL"
    ws.Range("I3").Value = "0 kWh"
    ws.Range("J3").Value = "New"
    ws.Range("K3").Value = "Medium"

    ws.Range("A4").Value = "ISS-003"
    ws.Range("B4").Value = Date - 3
    ws.Range("C4").Value = "Riverside Complex"
    ws.Range("D4").Value = "Unit 008"
    ws.Range("E4").Value = "Smart Water Meter"
    ws.Range("F4").Value = "Signal Issue"
    ws.Range("G4").Value = "Connectivity problems - delayed readings"
    ws.Range("H4").Value = "0 KL"
    ws.Range("I4").Value = "12 kWh"
    ws.Range("J4").Value = "Waiting for Parts"
    ws.Range("K4").Value = "High"

    Call DebugLog("Sample data added successfully (3 rows)", "SAMPLE_DATA")
End Sub

Sub ApplyEnhancedCalendarFormulaWithDebug(ws As Worksheet, isExisting As Boolean, existingRowCount As Long)
    ' Apply enhanced calendar formula with comprehensive debugging
    Dim stepStartTime As Double
    stepStartTime = Timer

    Call DebugLog("Starting enhanced calendar formula application...", "FORMULA")
    Call DebugLog("Target format: [Complex Name] [Unit Number] - Issue: [Type] - Water: [Reading] | Electric: [Reading] - Due: [Date]", "FORMULA")

    ' Enhanced calendar formula with proper error handling
    Dim calendarFormula As String
    calendarFormula = "=IF(OR(C2="""",D2=""""),""""," & _
                     "C2&"" ""&D2&"" - Issue: ""&" & _
                     "IF(F2="""",""Meter Reading"",F2)&" & _
                     """ - Water: ""&IF(H2="""",""No Data""," & _
                     "IF(ISNUMBER(FIND(""KL"",H2)),H2,H2&"" KL""))&" & _
                     """ | Electric: ""&IF(I2="""",""No Data""," & _
                     "IF(ISNUMBER(FIND(""kWh"",I2)),I2,I2&"" kWh""))&" & _
                     "IF(L2="""","""","" - Due: ""&TEXT(L2,""MM/DD/YYYY"")))"

    Call DebugLog("Calendar formula constructed", "FORMULA")
    Call DebugLog("Formula length: " & Len(calendarFormula) & " characters", "FORMULA")

    ' Apply formula with error handling and progress tracking
    On Error Resume Next

    If isExisting Then
        Call DebugLog("Applying formula to existing tracker with " & existingRowCount & " rows", "FORMULA")
        Call DebugLog("Preserving existing data during formula application", "DATA_PRESERVATION")

        ' Apply to specific range based on existing data
        Dim targetRange As String
        targetRange = "S2:S" & (existingRowCount + 10) ' Add buffer for new entries
        ws.Range(targetRange).Formula = calendarFormula

        Call DebugLog("Formula applied to range: " & targetRange, "FORMULA")
    Else
        Call DebugLog("Applying formula to new tracker", "FORMULA")
        ws.Range("S2:S1000").Formula = calendarFormula
        Call DebugLog("Formula applied to range: S2:S1000", "FORMULA")
    End If

    If Err.Number <> 0 Then
        Call DebugLog("Error applying calendar formula: " & Err.Description, "ERROR")
        Err.Clear
    Else
        Call DebugLog("Calendar formula applied successfully", "FORMULA")
    End If
    On Error GoTo 0

    ' Validate formula application
    Call ValidateFormulaApplication(ws, "S", "Calendar Formula", existingRowCount)

    Call DebugLog("Calendar formula application time: " & Format(Timer - stepStartTime, "0.000") & " seconds", "TIMING")
End Sub

Sub ApplyConsumptionAnalysisWithDebug(ws As Worksheet)
    ' Apply consumption analysis formula with debugging
    Dim stepStartTime As Double
    stepStartTime = Timer

    Call DebugLog("Starting consumption analysis formula application...", "FORMULA")

    ' Enhanced consumption analysis formula
    Dim consumptionFormula As String
    consumptionFormula = "=IF(OR(C2="""",D2=""""),""""," & _
                        "IF(OR(H2="""",I2=""""),""Incomplete Data""," & _
                        "LET(waterVal,IF(H2="""",0," & _
                        "IF(ISNUMBER(H2),H2," & _
                        "VALUE(TRIM(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(UPPER(H2),"" KL"",""""),""KL"",""""),""L"","""")))))," & _
                        "electricVal,IF(I2="""",0," & _
                        "IF(ISNUMBER(I2),I2," & _
                        "VALUE(TRIM(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(UPPER(I2),"" KWH"",""""),""KWH"",""""),""WH"","""")))))," & _
                        "IF(AND(waterVal=0,electricVal=0),""NO CONSUMPTION - URGENT""," & _
                        "IF(waterVal=0,""NO WATER - Check Meter""," & _
                        "IF(electricVal=0,""NO ELECTRICITY - Check Meter""," & _
                        "IF(AND(waterVal>0,electricVal>0),""Normal Consumption""," & _
                        "IF(waterVal>50,""HIGH WATER USAGE""," & _
                        "IF(electricVal>100,""HIGH ELECTRIC USAGE"",""Normal""))))))))"

    Call DebugLog("Consumption formula constructed", "FORMULA")
    Call DebugLog("Formula length: " & Len(consumptionFormula) & " characters", "FORMULA")

    ' Apply consumption analysis formula with error handling
    On Error Resume Next
    ws.Range("T2:T1000").Formula = consumptionFormula

    If Err.Number <> 0 Then
        Call DebugLog("Error applying consumption formula: " & Err.Description, "ERROR")
        Err.Clear
    Else
        Call DebugLog("Consumption analysis formula applied successfully", "FORMULA")
    End If
    On Error GoTo 0

    Call DebugLog("Consumption analysis application time: " & Format(Timer - stepStartTime, "0.000") & " seconds", "TIMING")
End Sub

Sub ApplyAutomaticFormulasWithDebug(ws As Worksheet)
    ' Apply automatic formulas with debugging
    Dim stepStartTime As Double
    stepStartTime = Timer

    Call DebugLog("Starting automatic formulas application...", "FORMULA")

    ' Issue ID formula
    Call DebugLog("Applying Issue ID formula...", "FORMULA")
    On Error Resume Next
    ws.Range("A2:A1000").Formula = "=IF(OR(C2="""",D2=""""),"""",""ISS-""&TEXT(ROW()-1,""000""))"
    If Err.Number <> 0 Then
        Call DebugLog("Error applying Issue ID formula: " & Err.Description, "ERROR")
        Err.Clear
    End If

    ' Date Logged formula
    Call DebugLog("Applying Date Logged formula...", "FORMULA")
    ws.Range("B2:B1000").Formula = "=IF(AND(C2<>"""",B2=""""),TODAY(),B2)"
    If Err.Number <> 0 Then
        Call DebugLog("Error applying Date Logged formula: " & Err.Description, "ERROR")
        Err.Clear
    End If

    ' Target Resolution Date formula
    Call DebugLog("Applying Target Resolution Date formula...", "FORMULA")
    ws.Range("L2:L1000").Formula = "=IF(B2="""","""",IF(WEEKDAY(B2,2)=5,B2+7,B2+(7-WEEKDAY(B2,2))))"
    If Err.Number <> 0 Then
        Call DebugLog("Error applying Target Resolution Date formula: " & Err.Description, "ERROR")
        Err.Clear
    End If

    ' Follow-up Required formula
    Call DebugLog("Applying Follow-up Required formula...", "FORMULA")
    ws.Range("O2:O1000").Formula = "=IF(M2="""","""",WORKDAY(M2,3))"
    If Err.Number <> 0 Then
        Call DebugLog("Error applying Follow-up Required formula: " & Err.Description, "ERROR")
        Err.Clear
    End If
    On Error GoTo 0

    Call DebugLog("Automatic formulas applied successfully", "FORMULA")
    Call DebugLog("Automatic formulas application time: " & Format(Timer - stepStartTime, "0.000") & " seconds", "TIMING")
End Sub

Sub ValidateFormulaApplication(ws As Worksheet, columnLetter As String, formulaName As String, existingRowCount As Long)
    ' Validate that formulas were applied correctly
    Dim testCell As String
    Dim testFormula As String

    testCell = columnLetter & "2"
    testFormula = ws.Range(testCell).Formula

    If Len(testFormula) > 0 And Left(testFormula, 1) = "=" Then
        Call DebugLog(formulaName & " validation: PASSED", "VALIDATION")
        Call DebugLog("Test cell " & testCell & " contains formula", "VALIDATION")

        ' Test a few more cells if existing data
        If existingRowCount > 0 Then
            Dim testRow As Long
            testRow = Application.WorksheetFunction.Min(existingRowCount + 1, 10)
            testCell = columnLetter & testRow
            testFormula = ws.Range(testCell).Formula

            If Len(testFormula) > 0 And Left(testFormula, 1) = "=" Then
                Call DebugLog("Formula validation for row " & testRow & ": PASSED", "VALIDATION")
            Else
                Call DebugLog("Formula validation for row " & testRow & ": FAILED", "WARNING")
            End If
        End If
    Else
        Call DebugLog(formulaName & " validation: FAILED", "ERROR")
        Call DebugLog("Test cell " & testCell & " does not contain formula", "ERROR")
    End If
End Sub

Sub ApplyDataValidationWithDebug(ws As Worksheet)
    ' Apply data validation with debugging
    Dim stepStartTime As Double
    stepStartTime = Timer

    Call DebugLog("Starting data validation setup...", "VALIDATION")

    ' Device Type validation
    Call DebugLog("Setting up Device Type validation (Column E)...", "VALIDATION")
    On Error Resume Next
    With ws.Range("E2:E1000").Validation
        .Delete
        .Add Type:=xlValidateList, AlertStyle:=xlValidAlertStop, _
             Formula1:="Smart Water Meter,Smart Electricity Meter,Combined Meter,Manual Meter"
    End With
    If Err.Number <> 0 Then
        Call DebugLog("Error setting Device Type validation: " & Err.Description, "ERROR")
        Err.Clear
    Else
        Call DebugLog("Device Type validation applied successfully", "VALIDATION")
    End If

    ' Issue Type validation
    Call DebugLog("Setting up Issue Type validation (Column F)...", "VALIDATION")
    With ws.Range("F2:F1000").Validation
        .Delete
        .Add Type:=xlValidateList, AlertStyle:=xlValidAlertStop, _
             Formula1:="No Readings Received,Device Fault,Signal Issue,Battery Replacement Needed,Consumption Anomaly,Device Reset Required,Installation Issue,Meter Tampering,Calibration Required"
    End With
    If Err.Number <> 0 Then
        Call DebugLog("Error setting Issue Type validation: " & Err.Description, "ERROR")
        Err.Clear
    Else
        Call DebugLog("Issue Type validation applied successfully", "VALIDATION")
    End If

    ' Status validation
    Call DebugLog("Setting up Status validation (Column J)...", "VALIDATION")
    With ws.Range("J2:J1000").Validation
        .Delete
        .Add Type:=xlValidateList, AlertStyle:=xlValidAlertStop, _
             Formula1:="New,In Progress,Waiting for Parts,Waiting for Quote,Waiting for Technician,Resolved,Closed,Escalated"
    End With
    If Err.Number <> 0 Then
        Call DebugLog("Error setting Status validation: " & Err.Description, "ERROR")
        Err.Clear
    Else
        Call DebugLog("Status validation applied successfully", "VALIDATION")
    End If

    ' Priority validation
    Call DebugLog("Setting up Priority validation (Column K)...", "VALIDATION")
    With ws.Range("K2:K1000").Validation
        .Delete
        .Add Type:=xlValidateList, AlertStyle:=xlValidAlertStop, _
             Formula1:="Critical,High,Medium,Low"
    End With
    If Err.Number <> 0 Then
        Call DebugLog("Error setting Priority validation: " & Err.Description, "ERROR")
        Err.Clear
    Else
        Call DebugLog("Priority validation applied successfully", "VALIDATION")
    End If
    On Error GoTo 0

    Call DebugLog("Data validation setup time: " & Format(Timer - stepStartTime, "0.000") & " seconds", "TIMING")
End Sub

Sub ApplyConditionalFormattingWithDebug(ws As Worksheet)
    ' Apply conditional formatting with debugging
    Dim stepStartTime As Double
    stepStartTime = Timer

    Call DebugLog("Starting conditional formatting setup...", "FORMATTING")

    ' Clear existing conditional formatting
    Call DebugLog("Clearing existing conditional formatting...", "FORMATTING")
    ws.Range("A1:T1000").FormatConditions.Delete

    ' Overdue items formatting
    Call DebugLog("Applying overdue items formatting...", "FORMATTING")
    On Error Resume Next
    With ws.Range("A2:T1000")
        .FormatConditions.Add Type:=xlExpression, Formula1:="=AND($J2<>""Resolved"",$J2<>""Closed"",$L2<>"""",TODAY()>$L2)"
        .FormatConditions(.FormatConditions.Count).Interior.Color = RGB(255, 199, 206)
        .FormatConditions(.FormatConditions.Count).Font.Bold = True
    End With
    If Err.Number <> 0 Then
        Call DebugLog("Error applying overdue formatting: " & Err.Description, "ERROR")
        Err.Clear
    End If

    ' Consumption Alert formatting
    Call DebugLog("Applying consumption alert formatting...", "FORMATTING")
    With ws.Range("T2:T1000")
        .FormatConditions.Add Type:=xlCellValue, Operator:=xlEqual, Formula1:="NO CONSUMPTION - URGENT"
        .FormatConditions(.FormatConditions.Count).Interior.Color = RGB(255, 0, 0)
        .FormatConditions(.FormatConditions.Count).Font.Color = RGB(255, 255, 255)
        .FormatConditions(.FormatConditions.Count).Font.Bold = True

        .FormatConditions.Add Type:=xlCellValue, Operator:=xlEqual, Formula1:="NO WATER"
        .FormatConditions(.FormatConditions.Count).Interior.Color = RGB(255, 165, 0)

        .FormatConditions.Add Type:=xlCellValue, Operator:=xlEqual, Formula1:="NO ELECTRICITY"
        .FormatConditions(.FormatConditions.Count).Interior.Color = RGB(255, 255, 0)

        .FormatConditions.Add Type:=xlCellValue, Operator:=xlEqual, Formula1:="Normal"
        .FormatConditions(.FormatConditions.Count).Interior.Color = RGB(144, 238, 144)
    End With
    If Err.Number <> 0 Then
        Call DebugLog("Error applying consumption alert formatting: " & Err.Description, "ERROR")
        Err.Clear
    End If

    ' Status-based formatting
    Call DebugLog("Applying status-based formatting...", "FORMATTING")
    With ws.Range("J2:J1000")
        .FormatConditions.Add Type:=xlCellValue, Operator:=xlEqual, Formula1:="New"
        .FormatConditions(.FormatConditions.Count).Interior.Color = RGB(173, 216, 230)

        .FormatConditions.Add Type:=xlCellValue, Operator:=xlEqual, Formula1:="In Progress"
        .FormatConditions(.FormatConditions.Count).Interior.Color = RGB(255, 235, 156)

        .FormatConditions.Add Type:=xlCellValue, Operator:=xlEqual, Formula1:="Escalated"
        .FormatConditions(.FormatConditions.Count).Interior.Color = RGB(255, 0, 0)
        .FormatConditions(.FormatConditions.Count).Font.Color = RGB(255, 255, 255)

        .FormatConditions.Add Type:=xlCellValue, Operator:=xlEqual, Formula1:="Resolved"
        .FormatConditions(.FormatConditions.Count).Interior.Color = RGB(198, 239, 206)

        .FormatConditions.Add Type:=xlCellValue, Operator:=xlEqual, Formula1:="Closed"
        .FormatConditions(.FormatConditions.Count).Interior.Color = RGB(217, 217, 217)
    End With
    If Err.Number <> 0 Then
        Call DebugLog("Error applying status formatting: " & Err.Description, "ERROR")
        Err.Clear
    End If

    ' Priority-based formatting
    Call DebugLog("Applying priority-based formatting...", "FORMATTING")
    With ws.Range("K2:K1000")
        .FormatConditions.Add Type:=xlCellValue, Operator:=xlEqual, Formula1:="Critical"
        .FormatConditions(.FormatConditions.Count).Interior.Color = RGB(255, 0, 0)
        .FormatConditions(.FormatConditions.Count).Font.Color = RGB(255, 255, 255)
        .FormatConditions(.FormatConditions.Count).Font.Bold = True

        .FormatConditions.Add Type:=xlCellValue, Operator:=xlEqual, Formula1:="High"
        .FormatConditions(.FormatConditions.Count).Interior.Color = RGB(255, 192, 0)

        .FormatConditions.Add Type:=xlCellValue, Operator:=xlEqual, Formula1:="Medium"
        .FormatConditions(.FormatConditions.Count).Interior.Color = RGB(255, 255, 0)

        .FormatConditions.Add Type:=xlCellValue, Operator:=xlEqual, Formula1:="Low"
        .FormatConditions(.FormatConditions.Count).Interior.Color = RGB(146, 208, 80)
    End With
    If Err.Number <> 0 Then
        Call DebugLog("Error applying priority formatting: " & Err.Description, "ERROR")
        Err.Clear
    End If
    On Error GoTo 0

    Call DebugLog("Conditional formatting setup time: " & Format(Timer - stepStartTime, "0.000") & " seconds", "TIMING")
End Sub

Sub SetupFiltersAndFreezePanesWithDebug(ws As Worksheet)
    ' Setup filters and freeze panes with debugging
    Dim stepStartTime As Double
    stepStartTime = Timer

    Call DebugLog("Setting up filters and freeze panes...", "SETUP")

    ' Add filters if not already present
    On Error Resume Next
    If ws.AutoFilterMode = False Then
        Call DebugLog("Adding AutoFilter to header row...", "SETUP")
        ws.Range("A1:T1").AutoFilter
        Call DebugLog("AutoFilter applied successfully", "SETUP")
    Else
        Call DebugLog("AutoFilter already exists", "SETUP")
    End If

    ' Freeze panes
    Call DebugLog("Setting up freeze panes...", "SETUP")
    ws.Range("B2").Select
    If ActiveWindow.FreezePanes = False Then
        ActiveWindow.FreezePanes = True
        Call DebugLog("Freeze panes applied successfully", "SETUP")
    Else
        Call DebugLog("Freeze panes already set", "SETUP")
    End If
    On Error GoTo 0

    Call DebugLog("Filters and freeze panes setup time: " & Format(Timer - stepStartTime, "0.000") & " seconds", "TIMING")
End Sub

Sub CreateEnhancedAnalysisSheetsWithDebug(wb As Workbook)
    ' Create enhanced analysis sheets with debugging
    Dim stepStartTime As Double
    stepStartTime = Timer

    Call DebugLog("Creating enhanced analysis sheets...", "ANALYSIS")

    ' Create Trend Analysis sheet
    Call DebugLog("Creating Trend Analysis sheet...", "ANALYSIS")
    Dim ws2 As Worksheet
    On Error Resume Next
    Set ws2 = wb.Worksheets("Trend Analysis")
    On Error GoTo 0

    If ws2 Is Nothing Then
        Set ws2 = wb.Worksheets.Add(After:=wb.Worksheets(1))
        ws2.Name = "Trend Analysis"
        Call DebugLog("Trend Analysis sheet created", "ANALYSIS")
    Else
        ws2.Cells.Clear
        Call DebugLog("Existing Trend Analysis sheet cleared", "ANALYSIS")
    End If

    ' Setup Trend Analysis content
    Call SetupTrendAnalysisContent(ws2)

    ' Create Weekly Summary sheet
    Call DebugLog("Creating Weekly Summary sheet...", "ANALYSIS")
    Dim ws3 As Worksheet
    On Error Resume Next
    Set ws3 = wb.Worksheets("Weekly Summary")
    On Error GoTo 0

    If ws3 Is Nothing Then
        Set ws3 = wb.Worksheets.Add(After:=ws2)
        ws3.Name = "Weekly Summary"
        Call DebugLog("Weekly Summary sheet created", "ANALYSIS")
    Else
        ws3.Cells.Clear
        Call DebugLog("Existing Weekly Summary sheet cleared", "ANALYSIS")
    End If

    ' Setup Weekly Summary content
    Call SetupWeeklySummaryContent(ws3)

    Call DebugLog("Analysis sheets creation time: " & Format(Timer - stepStartTime, "0.000") & " seconds", "TIMING")
End Sub

Sub CreateArchiveSystemWithDebug(wb As Workbook)
    ' Create archive system with debugging
    Dim stepStartTime As Double
    stepStartTime = Timer

    Call DebugLog("Creating archive system...", "ARCHIVE")

    ' Create Archive Control sheet
    Dim wsArchive As Worksheet
    On Error Resume Next
    Set wsArchive = wb.Worksheets("Archive Control")
    On Error GoTo 0

    If wsArchive Is Nothing Then
        Set wsArchive = wb.Worksheets.Add(After:=wb.Worksheets(wb.Worksheets.Count))
        wsArchive.Name = "Archive Control"
        Call DebugLog("Archive Control sheet created", "ARCHIVE")
    Else
        wsArchive.Cells.Clear
        Call DebugLog("Existing Archive Control sheet cleared", "ARCHIVE")
    End If

    ' Setup Archive Control content
    Call SetupArchiveControlContent(wsArchive)

    Call DebugLog("Archive system creation time: " & Format(Timer - stepStartTime, "0.000") & " seconds", "TIMING")
End Sub

Sub ValidateDataIntegrityWithDebug(ws As Worksheet, originalRowCount As Long)
    ' Validate data integrity with comprehensive debugging
    Dim stepStartTime As Double
    stepStartTime = Timer

    Call DebugLog("Starting data integrity validation...", "VALIDATION")
    Call DebugLog("Original row count: " & originalRowCount, "VALIDATION")

    Dim currentRowCount As Long
    Dim validationPassed As Boolean

    On Error Resume Next
    currentRowCount = ws.Cells(ws.Rows.Count, "C").End(xlUp).Row - 1
    If Err.Number <> 0 Then
        currentRowCount = 0
        Call DebugLog("Error counting current rows: " & Err.Description, "ERROR")
        Err.Clear
    End If
    On Error GoTo 0

    Call DebugLog("Current row count: " & currentRowCount, "VALIDATION")
    validationPassed = True

    ' Check row count
    If currentRowCount < originalRowCount Then
        validationPassed = False
        Call DebugLog("DATA LOSS DETECTED! Original: " & originalRowCount & ", Current: " & currentRowCount, "ERROR")
        MsgBox "WARNING: Data loss detected! Original rows: " & originalRowCount & _
               ", Current rows: " & currentRowCount, vbCritical
    Else
        Call DebugLog("Row count validation: PASSED", "VALIDATION")
    End If

    ' Check for empty critical cells in original data range
    Dim i As Long
    Dim emptyCount As Long

    Call DebugLog("Checking for empty critical cells...", "VALIDATION")
    For i = 2 To originalRowCount + 1
        If ws.Cells(i, 3).Value = "" Or ws.Cells(i, 4).Value = "" Then
            emptyCount = emptyCount + 1
            Call DebugLog("Empty critical cell found in row " & i, "WARNING")
        End If
    Next i

    Call DebugLog("Empty critical cells found: " & emptyCount, "VALIDATION")

    If emptyCount > 0 Then
        Call DebugLog("WARNING: " & emptyCount & " rows have missing Complex Name or Unit Number data!", "WARNING")
        MsgBox "WARNING: " & emptyCount & " rows have missing Complex Name or Unit Number data!", vbExclamation
    End If

    ' Final validation result
    If validationPassed And emptyCount = 0 Then
        Call DebugLog("✓ DATA INTEGRITY VALIDATION PASSED!", "SUCCESS")
        Call DebugLog("All " & originalRowCount & " original rows preserved successfully", "SUCCESS")
        MsgBox "✓ Data integrity validation PASSED!" & vbCrLf & _
               "All " & originalRowCount & " original rows preserved successfully.", vbInformation
    ElseIf validationPassed Then
        Call DebugLog("Data integrity validation passed with warnings", "WARNING")
    Else
        Call DebugLog("DATA INTEGRITY VALIDATION FAILED!", "ERROR")
    End If

    Call DebugLog("Data integrity validation time: " & Format(Timer - stepStartTime, "0.000") & " seconds", "TIMING")
End Sub

Sub DisplayCompletionMessageWithDebug(isExisting As Boolean, existingRowCount As Long)
    ' Display completion message with debug information
    Call DebugLog("Preparing completion message...", "COMPLETION")

    Dim completionMessage As String
    Dim totalTime As Double
    totalTime = Timer - DebugStartTime

    completionMessage = "Enhanced Utilities Issue Tracker completed successfully!" & vbCrLf & vbCrLf & _
                       "EXECUTION SUMMARY:" & vbCrLf & _
                       "• Total Time: " & Format(totalTime, "0.000") & " seconds" & vbCrLf & _
                       "• Steps Completed: " & DebugStepCount & "/" & DebugTotalSteps & vbCrLf & vbCrLf & _
                       "NEW FEATURES:" & vbCrLf & _
                       "✓ Enhanced Calendar Formula (Complex Name & Unit FIRST)" & vbCrLf & _
                       "✓ Automated Consumption Analysis with Error Handling" & vbCrLf & _
                       "✓ Comprehensive Debugging Framework" & vbCrLf & _
                       "✓ Data Backup & Integrity Validation" & vbCrLf & _
                       "✓ Optimized Monthly Archive System (<15 min)" & vbCrLf & _
                       "✓ Enhanced Trend Analysis & Reporting" & vbCrLf & _
                       "✓ Performance Monitoring & Error Handling" & vbCrLf

    If isExisting Then
        completionMessage = completionMessage & "✓ All " & existingRowCount & " existing rows preserved" & vbCrLf
    End If

    completionMessage = completionMessage & vbCrLf & _
                       "DEBUGGING INFO:" & vbCrLf & _
                       "• Check Immediate window (Ctrl+G) for detailed logs" & vbCrLf & _
                       "• Performance metrics and timing data available" & vbCrLf & _
                       "• Error handling and validation results logged" & vbCrLf & vbCrLf & _
                       "CALENDAR FORMAT:" & vbCrLf & _
                       "[Complex Name] [Unit Number] - Issue: [Type] - Water: [Reading] | Electric: [Reading] - Due: [Date]"

    Call DebugLog("Completion message prepared", "COMPLETION")
    MsgBox completionMessage, vbInformation, "Enhancement Complete - Debug Version"
End Sub

' ===================================================================
' HELPER FUNCTIONS FOR ANALYSIS SHEETS AND ARCHIVE SETUP
' ===================================================================

Sub SetupTrendAnalysisContent(ws As Worksheet)
    ' Setup Trend Analysis sheet content
    Call DebugLog("Setting up Trend Analysis content...", "ANALYSIS")

    ' Consumption Analysis by Complex
    ws.Range("A1").Value = "CONSUMPTION ANALYSIS BY COMPLEX"
    ws.Range("A1").Font.Bold = True
    ws.Range("A1").Font.Size = 14

    With ws.Range("A3:G3")
        .Value = Array("Complex", "Units Monitored", "No Water Reading", "No Electric Reading", "Both Zero", "Normal", "% Normal")
        .Font.Bold = True
        .Interior.Color = RGB(68, 114, 196)
        .Font.Color = RGB(255, 255, 255)
    End With

    ' Issues by Priority Matrix
    ws.Range("A12").Value = "ISSUES BY PRIORITY & STATUS"
    ws.Range("A12").Font.Bold = True
    ws.Range("A12").Font.Size = 14

    With ws.Range("A14:F14")
        .Value = Array("Priority", "New", "In Progress", "Waiting", "Resolved", "Total")
        .Font.Bold = True
        .Interior.Color = RGB(68, 114, 196)
        .Font.Color = RGB(255, 255, 255)
    End With

    ' Monthly Performance Metrics
    ws.Range("A23").Value = "MONTHLY PERFORMANCE METRICS"
    ws.Range("A23").Font.Bold = True
    ws.Range("A23").Font.Size = 14

    With ws.Range("A25:E25")
        .Value = Array("Metric", "This Month", "Last Month", "Change", "Target")
        .Font.Bold = True
        .Interior.Color = RGB(68, 114, 196)
        .Font.Color = RGB(255, 255, 255)
    End With

    Call DebugLog("Trend Analysis content setup completed", "ANALYSIS")
End Sub

Sub SetupWeeklySummaryContent(ws As Worksheet)
    ' Setup Weekly Summary sheet content
    Call DebugLog("Setting up Weekly Summary content...", "ANALYSIS")

    ws.Range("A1").Value = "WEEKLY SUMMARY - WEEK OF " & Format(Date, "MM/DD/YYYY")
    ws.Range("A1").Font.Bold = True
    ws.Range("A1").Font.Size = 16

    ' Key Metrics
    ws.Range("A3").Value = "KEY METRICS"
    ws.Range("A3").Font.Bold = True
    ws.Range("A3").Font.Size = 14

    ws.Range("A5").Value = "Total Issues Logged This Week:"
    ws.Range("B5").Formula = "=COUNTIFS('Main Issue Tracker'!B:B,"">=""&TODAY()-7,'Main Issue Tracker'!B:B,""<=""&TODAY())"

    ws.Range("A6").Value = "Issues Resolved This Week:"
    ws.Range("B6").Formula = "=COUNTIFS('Main Issue Tracker'!M:M,"">=""&TODAY()-7,'Main Issue Tracker'!M:M,""<=""&TODAY())"

    ws.Range("A7").Value = "Currently Overdue Issues:"
    ws.Range("B7").Formula = "=COUNTIFS('Main Issue Tracker'!J:J,""<>Resolved"",'Main Issue Tracker'!J:J,""<>Closed"",'Main Issue Tracker'!L:L,""<""&TODAY())"

    ws.Range("A8").Value = "Critical Consumption Issues:"
    ws.Range("B8").Formula = "=COUNTIF('Main Issue Tracker'!T:T,""NO CONSUMPTION - URGENT"")"

    ws.Range("A9").Value = "Follow-ups Due:"
    ws.Range("B9").Formula = "=COUNTIFS('Main Issue Tracker'!O:O,""<=""&TODAY(),'Main Issue Tracker'!P:P,"""")"

    ' Consumption Alerts
    ws.Range("A11").Value = "CONSUMPTION ALERTS"
    ws.Range("A11").Font.Bold = True
    ws.Range("A11").Font.Size = 14

    ws.Range("A13").Value = "Units with No Water:"
    ws.Range("B13").Formula = "=COUNTIF('Main Issue Tracker'!T:T,""NO WATER"")"

    ws.Range("A14").Value = "Units with No Electricity:"
    ws.Range("B14").Formula = "=COUNTIF('Main Issue Tracker'!T:T,""NO ELECTRICITY"")"

    ws.Range("A15").Value = "Units with Normal Consumption:"
    ws.Range("B15").Formula = "=COUNTIF('Main Issue Tracker'!T:T,""Normal"")"

    ' Format summary sheet
    ws.Range("A5:A15").Font.Bold = True
    ws.Range("B5:B15").Font.Bold = True
    ws.Range("B5:B15").Font.Color = RGB(0, 0, 255)

    ' Set print area
    ws.PageSetup.PrintArea = "$A$1:$C$20"

    Call DebugLog("Weekly Summary content setup completed", "ANALYSIS")
End Sub

Sub SetupArchiveControlContent(ws As Worksheet)
    ' Setup Archive Control sheet content
    Call DebugLog("Setting up Archive Control content...", "ARCHIVE")

    ws.Range("A1").Value = "MONTHLY ARCHIVE CONTROL"
    ws.Range("A1").Font.Bold = True
    ws.Range("A1").Font.Size = 16

    ws.Range("A3").Value = "Last Archive Date:"
    ws.Range("B3").Formula = "=TODAY()"

    ws.Range("A4").Value = "Archive Criteria:"
    ws.Range("B4").Value = "Resolved issues older than 30 days"

    ws.Range("A5").Value = "Items to Archive:"
    ws.Range("B5").Formula = "=COUNTIFS('Main Issue Tracker'!J:J,""Resolved"",'Main Issue Tracker'!M:M,""<""&TODAY()-30)"

    ws.Range("A7").Value = "ARCHIVE ACTIONS"
    ws.Range("A7").Font.Bold = True

    ws.Range("A9").Value = "Click button to archive resolved issues older than 30 days:"

    ' Add optimized archive button with error handling
    On Error Resume Next
    Dim btn As Button
    Set btn = ws.Buttons.Add(ws.Range("A11").Left, ws.Range("A11").Top, 200, 35)
    If Err.Number = 0 Then
        btn.OnAction = "OptimizedArchiveProcessWithDebug"
        btn.Caption = "Optimized Archive with Debug"
        Call DebugLog("Archive button created successfully", "ARCHIVE")
    Else
        Call DebugLog("Error creating archive button: " & Err.Description, "ERROR")
        Err.Clear
    End If
    On Error GoTo 0

    ws.Range("A13").Value = "ARCHIVE HISTORY"
    ws.Range("A13").Font.Bold = True

    With ws.Range("A15:D15")
        .Value = Array("Archive Date", "Issues Archived", "Date Range", "Archive Sheet")
        .Font.Bold = True
        .Interior.Color = RGB(68, 114, 196)
        .Font.Color = RGB(255, 255, 255)
    End With

    Call DebugLog("Archive Control content setup completed", "ARCHIVE")
End Sub

Sub OptimizedArchiveProcessWithDebug()
    ' Optimized archive process with comprehensive debugging
    Call DebugLog("=== OPTIMIZED ARCHIVE PROCESS STARTED ===", "ARCHIVE")

    Dim archiveStartTime As Double
    archiveStartTime = Timer

    Application.ScreenUpdating = False
    Application.Calculation = xlCalculationManual
    Application.EnableEvents = False

    Call DebugLog("Application settings optimized for archive process", "ARCHIVE")

    On Error GoTo ArchiveErrorHandler

    Dim wb As Workbook
    Dim wsMain As Worksheet
    Dim wsArchive As Worksheet
    Dim wsNewArchive As Worksheet

    Set wb = ActiveWorkbook
    Call DebugLog("Active workbook set for archive process", "ARCHIVE")

    ' Safe worksheet detection for archive process
    If Not WorksheetExists(wb, "Main Issue Tracker") Then
        Call DebugLog("ERROR: Main Issue Tracker worksheet not found!", "ERROR")
        MsgBox "Error: Main Issue Tracker worksheet not found!", vbCritical
        GoTo ArchiveCleanup
    End If

    Set wsMain = wb.Worksheets("Main Issue Tracker")
    Call DebugLog("Main Issue Tracker worksheet found", "ARCHIVE")

    If Not WorksheetExists(wb, "Archive Control") Then
        Call DebugLog("ERROR: Archive Control worksheet not found!", "ERROR")
        MsgBox "Error: Archive Control worksheet not found!", vbCritical
        GoTo ArchiveCleanup
    End If

    Set wsArchive = wb.Worksheets("Archive Control")
    Call DebugLog("Archive Control worksheet found", "ARCHIVE")

    ' Create new archive sheet with optimized naming
    Dim archiveSheetName As String
    archiveSheetName = "Archive_" & Format(Date, "YYYY_MM")
    Call DebugLog("Archive sheet name: " & archiveSheetName, "ARCHIVE")

    On Error Resume Next
    Set wsNewArchive = wb.Worksheets(archiveSheetName)
    On Error GoTo ArchiveErrorHandler

    If wsNewArchive Is Nothing Then
        Set wsNewArchive = wb.Worksheets.Add(After:=wb.Worksheets(wb.Worksheets.Count))
        wsNewArchive.Name = archiveSheetName
        Call DebugLog("New archive sheet created: " & archiveSheetName, "ARCHIVE")

        ' Copy headers efficiently
        wsMain.Range("A1:T1").Copy wsNewArchive.Range("A1:T1")
        Call DebugLog("Headers copied to archive sheet", "ARCHIVE")
    Else
        Call DebugLog("Using existing archive sheet: " & archiveSheetName, "ARCHIVE")
    End If

    ' Archive process with detailed logging
    Dim lastRow As Long
    Dim i As Long
    Dim archiveRow As Long
    Dim archivedCount As Long

    lastRow = wsMain.Cells(wsMain.Rows.Count, "C").End(xlUp).Row
    archiveRow = wsNewArchive.Cells(wsNewArchive.Rows.Count, "A").End(xlUp).Row + 1

    Call DebugLog("Processing " & (lastRow - 1) & " rows for archiving", "ARCHIVE")
    Call DebugLog("Archive will start at row " & archiveRow, "ARCHIVE")

    ' Process in batches for better performance
    For i = lastRow To 2 Step -1
        If wsMain.Cells(i, 10).Value = "Resolved" And _
           wsMain.Cells(i, 13).Value <> "" And _
           wsMain.Cells(i, 13).Value < Date - 30 Then

            ' Copy row to archive
            wsMain.Rows(i).Copy wsNewArchive.Rows(archiveRow)
            archiveRow = archiveRow + 1
            archivedCount = archivedCount + 1

            ' Delete from main tracker
            wsMain.Rows(i).Delete

            ' Log progress every 10 items
            If archivedCount Mod 10 = 0 Then
                Call DebugLog("Archived " & archivedCount & " items so far...", "ARCHIVE")
            End If
        End If
    Next i

    Call DebugLog("Archive processing completed. Items archived: " & archivedCount, "ARCHIVE")

    ' Update archive history
    Dim historyRow As Long
    historyRow = wsArchive.Cells(wsArchive.Rows.Count, "A").End(xlUp).Row + 1

    wsArchive.Cells(historyRow, 1).Value = Date
    wsArchive.Cells(historyRow, 2).Value = archivedCount
    wsArchive.Cells(historyRow, 3).Value = "30+ days old resolved issues"
    wsArchive.Cells(historyRow, 4).Value = archiveSheetName

    Call DebugLog("Archive history updated", "ARCHIVE")

ArchiveCleanup:
    ' Restore application settings
    Application.ScreenUpdating = True
    Application.Calculation = xlCalculationAutomatic
    Application.EnableEvents = True

    Dim archiveEndTime As Double
    archiveEndTime = Timer
    Dim processingTime As Double
    processingTime = archiveEndTime - archiveStartTime

    Call DebugLog("Archive process completed in " & Format(processingTime, "0.000") & " seconds", "ARCHIVE")
    Call DebugLog("=== OPTIMIZED ARCHIVE PROCESS COMPLETED ===", "ARCHIVE")

    MsgBox "Optimized Archive Process Completed!" & vbCrLf & _
           "Items Archived: " & archivedCount & vbCrLf & _
           "Processing Time: " & Format(processingTime, "0.0") & " seconds" & vbCrLf & _
           "Archive Sheet: " & archiveSheetName & vbCrLf & vbCrLf & _
           "Check Immediate window (Ctrl+G) for detailed logs.", vbInformation

    Exit Sub

ArchiveErrorHandler:
    Call HandleDebugError(Err.Number, Err.Description, Erl)
    GoTo ArchiveCleanup
End Sub
