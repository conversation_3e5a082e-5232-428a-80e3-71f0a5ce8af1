' ===================================================================
' ENHANCED UTILITIES TRACKER WITH COMPREHENSIVE DEBUGGING FRAMEWORK
' ===================================================================
' Version: 2.0 with Debug Framework
' Purpose: Excel-based utility meter reading management with debugging
' Features: Error handling, performance monitoring, progress tracking
' ===================================================================

Option Explicit

' Global debugging variables
Public Const DEBUG_MODE As Boolean = True
Public DebugStartTime As Double
Public DebugStepTime As Double
Public DebugStepCount As Long
Public DebugTotalSteps As Long

Sub EnhanceUtilitiesTrackerWithDebug()
    ' Enhanced Utilities Issue Tracker with Comprehensive Debugging Framework
    ' Preserves existing data while adding enhanced features with full error handling
    
    ' Initialize debugging framework
    Call InitializeDebugFramework
    Call DebugLog("=== ENHANCED UTILITIES TRACKER DEBUG SESSION STARTED ===", "SYSTEM")
    Call DebugLog("Debug Mode: " & DEBUG_MODE, "SYSTEM")
    Call DebugLog("Excel Version: " & Application.Version, "SYSTEM")
    Call DebugLog("Available Memory: " & GetAvailableMemory() & " MB", "SYSTEM")
    
    ' Performance monitoring setup
    Application.ScreenUpdating = False
    Application.DisplayAlerts = False
    Application.Calculation = xlCalculationManual
    
    Call DebugLog("Application settings optimized for performance", "PERFORMANCE")
    
    ' Error handling setup
    On Error GoTo ErrorHandler
    
    ' Main variables with debugging
    Dim wb As Workbook
    Dim ws1 As Worksheet
    Dim isExisting As Boolean
    Dim existingRowCount As Long
    
    Call DebugStep("Initializing workbook and worksheet detection")
    
    ' Enhanced worksheet detection with comprehensive error handling
    Call DebugLog("Attempting to detect existing tracker...", "DETECTION")
    
    Set wb = ActiveWorkbook
    Call DebugLog("Active workbook set: " & wb.Name, "DETECTION")
    
    ' Safe worksheet detection
    Dim wsFound As Boolean
    wsFound = WorksheetExists(wb, "Main Issue Tracker")
    
    If wsFound Then
        Set ws1 = wb.Worksheets("Main Issue Tracker")
        isExisting = True
        
        ' Count existing data rows with error handling
        On Error Resume Next
        existingRowCount = ws1.Cells(ws1.Rows.Count, "C").End(xlUp).Row - 1
        If Err.Number <> 0 Then
            existingRowCount = 0
            Call DebugLog("Warning: Could not count existing rows. Error: " & Err.Description, "WARNING")
            Err.Clear
        End If
        On Error GoTo ErrorHandler
        
        Call DebugLog("Existing tracker detected with " & existingRowCount & " data rows", "DETECTION")
        
        ' Create backup with progress tracking
        Call DebugStep("Creating data backup for " & existingRowCount & " rows")
        Call CreateDataBackupWithDebug(ws1, existingRowCount)
        
        MsgBox "Existing tracker detected with " & existingRowCount & " data rows." & vbCrLf & _
               "Backup created. Enhancing current system while preserving all data.", vbInformation
    Else
        Call DebugLog("No existing tracker found. Creating new tracker.", "DETECTION")
        isExisting = False
        existingRowCount = 0
    End If
    
    ' Create new tracker if needed
    If Not isExisting Then
        Call DebugStep("Creating new tracker workbook")
        Call CreateNewTrackerWithDebug(wb, ws1)
    End If
    
    ' Apply enhanced calendar formula with debugging
    Call DebugStep("Applying enhanced calendar formula")
    Call ApplyEnhancedCalendarFormulaWithDebug(ws1, isExisting, existingRowCount)
    
    ' Apply consumption analysis formula with debugging
    Call DebugStep("Applying consumption analysis formula")
    Call ApplyConsumptionAnalysisWithDebug(ws1)
    
    ' Apply automatic formulas with debugging
    Call DebugStep("Applying automatic formulas")
    Call ApplyAutomaticFormulasWithDebug(ws1)
    
    ' Apply data validation with debugging
    Call DebugStep("Setting up data validation")
    Call ApplyDataValidationWithDebug(ws1)
    
    ' Apply conditional formatting with debugging
    Call DebugStep("Applying conditional formatting")
    Call ApplyConditionalFormattingWithDebug(ws1)
    
    ' Setup filters and freeze panes
    Call DebugStep("Setting up filters and freeze panes")
    Call SetupFiltersAndFreezePanesWithDebug(ws1)
    
    ' Create enhanced analysis sheets
    Call DebugStep("Creating enhanced analysis sheets")
    Call CreateEnhancedAnalysisSheetsWithDebug(wb)
    
    ' Create archive system
    Call DebugStep("Creating archive system")
    Call CreateArchiveSystemWithDebug(wb)
    
    ' Data integrity validation
    If isExisting Then
        Call DebugStep("Validating data integrity")
        Call ValidateDataIntegrityWithDebug(ws1, existingRowCount)
    End If
    
    ' Final cleanup and completion
    Call DebugStep("Finalizing enhancement")
    ws1.Select
    ws1.Range("A1").Select
    
    ' Restore application settings
    Application.ScreenUpdating = True
    Application.DisplayAlerts = True
    Application.Calculation = xlCalculationAutomatic
    
    Call DebugLog("Application settings restored", "PERFORMANCE")
    
    ' Display completion message with debug summary
    Call DisplayCompletionMessageWithDebug(isExisting, existingRowCount)
    
    ' Finalize debugging
    Call FinalizeDebugFramework
    
    Exit Sub
    
ErrorHandler:
    Call HandleDebugError(Err.Number, Err.Description, Erl)
    
    ' Restore application settings on error
    Application.ScreenUpdating = True
    Application.DisplayAlerts = True
    Application.Calculation = xlCalculationAutomatic
    
    Call DebugLog("=== DEBUG SESSION ENDED WITH ERROR ===", "ERROR")
End Sub

' ===================================================================
' DEBUGGING FRAMEWORK FUNCTIONS
' ===================================================================

Sub InitializeDebugFramework()
    ' Initialize the debugging framework
    DebugStartTime = Timer
    DebugStepTime = Timer
    DebugStepCount = 0
    DebugTotalSteps = 12 ' Total number of major steps
    
    ' Clear immediate window if in debug mode
    If DEBUG_MODE Then
        Debug.Print String(80, "=")
        Debug.Print "ENHANCED UTILITIES TRACKER DEBUG SESSION"
        Debug.Print "Session Started: " & Format(Now, "MM/DD/YYYY HH:MM:SS")
        Debug.Print String(80, "=")
    End If
End Sub

Sub DebugLog(message As String, category As String)
    ' Log debug messages with timestamp and category
    If DEBUG_MODE Then
        Dim timestamp As String
        timestamp = Format(Now, "HH:MM:SS.000")
        Debug.Print "[" & timestamp & "] [" & category & "] " & message
    End If
End Sub