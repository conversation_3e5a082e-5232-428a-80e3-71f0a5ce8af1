//@version=6
indicator("Function Syntax Test", overlay=true)

// This is a minimal script to test the correct function declaration syntax in Pine Script v6.
// Based on the previous errors, the hypothesis is that the return type should precede the function name.
// Correct Syntax Hypothesis: [return_type] [function_name]([parameters])

// --- Test Function ---
// Declares a function that takes an integer and returns a string.
string testFunction(int inputNumber)
    return "Input was: " + str.tostring(inputNumber)

// --- Main Logic ---
if barstate.islastconfirmed
    // Call the function and store the result.
    string result = testFunction(123)
    
    // If this compiles, it proves the syntax is correct.
    label.new(bar_index, high, result, color=color.green, textcolor=color.white)
