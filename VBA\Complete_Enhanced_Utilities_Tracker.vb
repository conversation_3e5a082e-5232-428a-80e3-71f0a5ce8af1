' ===================================================================
' COMPLETE ENHANCED UTILITIES TRACKER - STANDALONE SCRIPT
' ===================================================================
' Version: 5.0 - Complete Standalone Implementation
' Purpose: Create Enhanced Utilities Tracker from scratch on blank workbook
' Features: All worksheets, formulas, formatting, validation, sample data
' ===================================================================

Option Explicit

Sub CreateCompleteEnhancedUtilitiesTracker()
    ' Master function to create complete Enhanced Utilities Tracker from scratch
    ' Run this on a blank Excel workbook to create the full system
    
    ' Initialize
    Application.ScreenUpdating = False
    Application.DisplayAlerts = False
    Application.Calculation = xlCalculationManual
    
    Debug.Print String(80, "=")
    Debug.Print "CREATING COMPLETE ENHANCED UTILITIES TRACKER"
    Debug.Print "Started: " & Format(Now, "MM/DD/YYYY HH:MM:SS")
    Debug.Print String(80, "=")
    
    Dim wb As Workbook
    Set wb = ActiveWorkbook
    
    ' Clear any existing worksheets except the first one
    Call ClearExistingWorksheets(wb)
    
    ' Step 1: Create Main Issue Tracker
    Debug.Print "Step 1: Creating Main Issue Tracker worksheet..."
    Dim wsMain As Worksheet
    Set wsMain = CreateMainIssueTracker(wb)
    
    ' Step 2: Create supporting worksheets
    Debug.Print "Step 2: Creating supporting worksheets..."
    Call CreateSupportingWorksheets(wb)
    
    ' Step 3: Apply all formulas
    Debug.Print "Step 3: Applying enhanced formulas..."
    Call ApplyAllFormulas(wsMain)
    
    ' Step 4: Apply conditional formatting
    Debug.Print "Step 4: Applying conditional formatting..."
    Call ApplyAllConditionalFormatting(wsMain)
    
    ' Step 5: Set up data validation
    Debug.Print "Step 5: Setting up data validation..."
    Call SetupAllDataValidation(wsMain)
    
    ' Step 6: Configure layout and formatting
    Debug.Print "Step 6: Configuring layout and formatting..."
    Call ConfigureLayoutAndFormatting(wsMain)
    
    ' Step 7: Add sample data
    Debug.Print "Step 7: Adding sample utility meter data..."
    Call AddSampleUtilityMeterData(wsMain)
    
    ' Step 8: Final setup
    Debug.Print "Step 8: Final setup and optimization..."
    Call FinalSetupAndOptimization(wsMain)
    
    ' Restore application settings
    Application.ScreenUpdating = True
    Application.DisplayAlerts = True
    Application.Calculation = xlCalculationAutomatic
    
    Debug.Print String(80, "=")
    Debug.Print "ENHANCED UTILITIES TRACKER CREATION COMPLETED"
    Debug.Print "Completed: " & Format(Now, "MM/DD/YYYY HH:MM:SS")
    Debug.Print String(80, "=")
    
    ' Display completion message
    Call DisplayCreationCompletionMessage
End Sub

Sub ClearExistingWorksheets(wb As Workbook)
    ' Clear existing worksheets except the first one
    Dim ws As Worksheet
    Dim wsCount As Long
    wsCount = wb.Worksheets.Count
    
    ' Delete all worksheets except the first one
    Do While wb.Worksheets.Count > 1
        wb.Worksheets(wb.Worksheets.Count).Delete
    Loop
    
    ' Rename the remaining worksheet
    wb.Worksheets(1).Name = "Main Issue Tracker"
    Debug.Print "Cleared existing worksheets, prepared Main Issue Tracker"
End Sub

Function CreateMainIssueTracker(wb As Workbook) As Worksheet
    ' Create the main issue tracker worksheet with headers
    Dim ws As Worksheet
    Set ws = wb.Worksheets("Main Issue Tracker")
    
    ' Clear any existing content
    ws.Cells.Clear
    
    ' Create 20-column header structure
    With ws.Range("A1:T1")
        .Value = Array("Issue ID", "Date Logged", "Complex Name", "Unit Number", "Device Type", _
                      "Issue Type", "Issue Description", "Water Reading (Last 30 days)", _
                      "Electricity Reading (Last 30 days)", "Status", "Priority", _
                      "Target Resolution Date", "Date Resolved", "Resolution Notes", _
                      "Follow-up Required", "Follow-up Completed", "Follow-up Notes", _
                      "Related Issue ID", "Calendar Entry", "Consumption Alert")
        
        ' Apply header formatting
        .Font.Bold = True
        .Interior.Color = RGB(68, 114, 196) ' Blue background
        .Font.Color = RGB(255, 255, 255) ' White text
        .HorizontalAlignment = xlCenter
        .VerticalAlignment = xlCenter
        .WrapText = True
        .RowHeight = 35
    End With
    
    Debug.Print "Main Issue Tracker headers created with proper formatting"
    Set CreateMainIssueTracker = ws
End Function

Sub CreateSupportingWorksheets(wb As Workbook)
    ' Create all supporting worksheets
    
    ' Create Archive Control worksheet
    Dim wsArchive As Worksheet
    Set wsArchive = wb.Worksheets.Add(After:=wb.Worksheets(wb.Worksheets.Count))
    wsArchive.Name = "Archive Control"
    
    ' Setup Archive Control content
    wsArchive.Range("A1").Value = "MONTHLY ARCHIVE CONTROL"
    wsArchive.Range("A1").Font.Bold = True
    wsArchive.Range("A1").Font.Size = 16
    
    wsArchive.Range("A3").Value = "Archive Behavior: MOVES data (removes from main tracker)"
    wsArchive.Range("A4").Value = "Resolved issues older than 30 days are transferred to archive sheets"
    wsArchive.Range("A5").Value = "Original data is DELETED from Main Issue Tracker after copying"
    
    wsArchive.Range("A7").Value = "ARCHIVE HISTORY"
    wsArchive.Range("A7").Font.Bold = True
    
    With wsArchive.Range("A9:D9")
        .Value = Array("Archive Date", "Issues Archived", "Date Range", "Archive Sheet")
        .Font.Bold = True
        .Interior.Color = RGB(68, 114, 196)
        .Font.Color = RGB(255, 255, 255)
    End With
    
    ' Create Trend Analysis worksheet
    Dim wsTrend As Worksheet
    Set wsTrend = wb.Worksheets.Add(After:=wsArchive)
    wsTrend.Name = "Trend Analysis"
    
    wsTrend.Range("A1").Value = "CONSUMPTION ANALYSIS BY COMPLEX"
    wsTrend.Range("A1").Font.Bold = True
    wsTrend.Range("A1").Font.Size = 14
    
    With wsTrend.Range("A3:G3")
        .Value = Array("Complex", "Units Monitored", "No Water Reading", "No Electric Reading", "Both Zero", "Normal", "% Normal")
        .Font.Bold = True
        .Interior.Color = RGB(68, 114, 196)
        .Font.Color = RGB(255, 255, 255)
    End With
    
    ' Create Weekly Summary worksheet
    Dim wsSummary As Worksheet
    Set wsSummary = wb.Worksheets.Add(After:=wsTrend)
    wsSummary.Name = "Weekly Summary"
    
    wsSummary.Range("A1").Value = "WEEKLY SUMMARY - WEEK OF " & Format(Date, "MM/DD/YYYY")
    wsSummary.Range("A1").Font.Bold = True
    wsSummary.Range("A1").Font.Size = 16
    
    wsSummary.Range("A3").Value = "KEY METRICS"
    wsSummary.Range("A3").Font.Bold = True
    wsSummary.Range("A3").Font.Size = 14
    
    wsSummary.Range("A5").Value = "Total Issues Logged This Week:"
    wsSummary.Range("B5").Formula = "=COUNTIFS('Main Issue Tracker'!B:B,"">=""&TODAY()-7,'Main Issue Tracker'!B:B,""<=""&TODAY())"
    
    wsSummary.Range("A6").Value = "Issues Resolved This Week:"
    wsSummary.Range("B6").Formula = "=COUNTIFS('Main Issue Tracker'!M:M,"">=""&TODAY()-7,'Main Issue Tracker'!M:M,""<=""&TODAY())"
    
    wsSummary.Range("A7").Value = "Currently Overdue Issues:"
    wsSummary.Range("B7").Formula = "=COUNTIFS('Main Issue Tracker'!J:J,""<>Resolved"",'Main Issue Tracker'!J:J,""<>Closed"",'Main Issue Tracker'!L:L,""<""&TODAY())"
    
    Debug.Print "Supporting worksheets created: Archive Control, Trend Analysis, Weekly Summary"
End Sub

Sub ApplyAllFormulas(ws As Worksheet)
    ' Apply all formulas to the main tracker
    
    ' Issue ID formula (Column A)
    Dim issueIdFormula As String
    issueIdFormula = "=IF(OR(C2="""",D2=""""),"""",""ISS-""&TEXT(ROW()-1,""000""))"
    ws.Range("A2:A1000").Formula = issueIdFormula
    Debug.Print "Issue ID formula applied to Column A"
    
    ' Target Resolution Date formula (Column L) - 7 days from date logged
    Dim targetDateFormula As String
    targetDateFormula = "=IF(B2="""","""",IF(WEEKDAY(B2,2)=5,B2+7,B2+(7-WEEKDAY(B2,2))))"
    ws.Range("L2:L1000").Formula = targetDateFormula
    Debug.Print "Target Resolution Date formula applied to Column L"

    ' Follow-up Required formula (Column O) - 3 business days after resolution
    Dim followUpFormula As String
    followUpFormula = "=IF(M2="""","""",WORKDAY(M2,3))"
    ws.Range("O2:O1000").Formula = followUpFormula
    Debug.Print "Follow-up Required formula applied to Column O"
    
    ' Enhanced Calendar Entry formula (Column S) - Built in manageable pieces
    Dim calendarFormula As String
    Dim calendarPart1 As String
    Dim calendarPart2 As String
    Dim calendarPart3 As String
    Dim calendarPart4 As String

    ' Build calendar formula in parts to avoid line continuation issues
    calendarPart1 = "=IF(OR(C2="""",D2=""""),"""","
    calendarPart2 = "C2&"" ""&D2&"" - Issue: ""&IF(F2="""",""Meter Reading"",F2)"
    calendarPart3 = "&"" - Water: ""&IF(H2="""",""No Data"",IF(ISNUMBER(FIND(""KL"",H2)),H2,H2&"" KL""))"
    calendarPart4 = "&"" | Electric: ""&IF(I2="""",""No Data"",IF(ISNUMBER(FIND(""kWh"",I2)),I2,I2&"" kWh""))"

    ' Combine all parts
    calendarFormula = calendarPart1 & calendarPart2 & calendarPart3 & calendarPart4 & "&IF(L2="""","""","" - Due: ""&TEXT(L2,""MM/DD/YYYY"")))"

    ws.Range("S2:S1000").Formula = calendarFormula
    Debug.Print "Enhanced Calendar Entry formula applied to Column S"
    
    ' FIXED Enhanced Consumption Analysis formula (Column T) - Built in manageable pieces
    Dim consumptionFormula As String
    Dim consumptionPart1 As String
    Dim consumptionPart2 As String
    Dim consumptionPart3 As String
    Dim consumptionPart4 As String
    Dim consumptionPart5 As String
    Dim waterZeroConditions As String
    Dim electricZeroConditions As String

    ' Define zero detection conditions separately for clarity
    waterZeroConditions = "OR(H2=""0 KL"",H2=""0"",H2=0,H2=""0KL"")"
    electricZeroConditions = "OR(I2=""0 kWh"",I2=""0"",I2=0,I2=""0kWh"",I2=""0kwh"",I2=""0KWH"")"

    ' Build consumption formula in logical parts
    consumptionPart1 = "=IF(OR(C2="""",D2=""""),"""","
    consumptionPart2 = "IF(OR(H2="""",I2=""""),""Incomplete Data"","
    consumptionPart3 = "IF(AND(" & waterZeroConditions & "," & electricZeroConditions & "),""NO CONSUMPTION - URGENT"","
    consumptionPart4 = "IF(" & waterZeroConditions & ",""NO WATER - Check Meter"","
    consumptionPart5 = "IF(" & electricZeroConditions & ",""NO ELECTRICITY - Check Meter"",""Normal Consumption"")))))"

    ' Combine all parts
    consumptionFormula = consumptionPart1 & consumptionPart2 & consumptionPart3 & consumptionPart4 & consumptionPart5

    ws.Range("T2:T1000").Formula = consumptionFormula
    Debug.Print "FIXED Enhanced Consumption Analysis formula applied to Column T (handles 0kWh format)"
    
    Debug.Print "All formulas applied successfully"
End Sub

Sub ApplyAllConditionalFormatting(ws As Worksheet)
    ' Apply comprehensive conditional formatting

    ' Clear any existing conditional formatting
    ws.Range("A1:T1000").FormatConditions.Delete

    ' === OVERDUE ITEMS FORMATTING (ENTIRE ROWS) ===
    With ws.Range("A2:T1000")
        .FormatConditions.Add Type:=xlExpression, Formula1:="=AND($J2<>""Resolved"",$J2<>""Closed"",$L2<>"""",TODAY()>$L2)"
        With .FormatConditions(.FormatConditions.Count)
            .Interior.Color = RGB(255, 199, 206) ' Light Red
            .Font.Bold = True
        End With
    End With
    Debug.Print "Overdue items formatting applied (Light Red background)"

    ' === STATUS COLUMN FORMATTING (Column J) ===
    With ws.Range("J2:J1000")
        ' New status
        .FormatConditions.Add Type:=xlCellValue, Operator:=xlEqual, Formula1:="New"
        With .FormatConditions(.FormatConditions.Count)
            .Interior.Color = RGB(173, 216, 230) ' Light Blue
            .Font.Bold = True
        End With

        ' In Progress status
        .FormatConditions.Add Type:=xlCellValue, Operator:=xlEqual, Formula1:="In Progress"
        With .FormatConditions(.FormatConditions.Count)
            .Interior.Color = RGB(255, 235, 156) ' Light Yellow
            .Font.Bold = True
        End With

        ' Escalated status
        .FormatConditions.Add Type:=xlCellValue, Operator:=xlEqual, Formula1:="Escalated"
        With .FormatConditions(.FormatConditions.Count)
            .Interior.Color = RGB(255, 0, 0) ' Red
            .Font.Color = RGB(255, 255, 255) ' White text
            .Font.Bold = True
        End With

        ' Resolved status
        .FormatConditions.Add Type:=xlCellValue, Operator:=xlEqual, Formula1:="Resolved"
        With .FormatConditions(.FormatConditions.Count)
            .Interior.Color = RGB(198, 239, 206) ' Light Green
        End With

        ' Closed status
        .FormatConditions.Add Type:=xlCellValue, Operator:=xlEqual, Formula1:="Closed"
        With .FormatConditions(.FormatConditions.Count)
            .Interior.Color = RGB(217, 217, 217) ' Light Gray
        End With
    End With
    Debug.Print "Status formatting applied (New=Blue, InProgress=Yellow, Escalated=Red, etc.)"

    ' === PRIORITY COLUMN FORMATTING (Column K) ===
    With ws.Range("K2:K1000")
        ' Critical priority
        .FormatConditions.Add Type:=xlCellValue, Operator:=xlEqual, Formula1:="Critical"
        With .FormatConditions(.FormatConditions.Count)
            .Interior.Color = RGB(255, 0, 0) ' Red
            .Font.Color = RGB(255, 255, 255) ' White text
            .Font.Bold = True
        End With

        ' High priority
        .FormatConditions.Add Type:=xlCellValue, Operator:=xlEqual, Formula1:="High"
        With .FormatConditions(.FormatConditions.Count)
            .Interior.Color = RGB(255, 192, 0) ' Orange
            .Font.Bold = True
        End With

        ' Medium priority
        .FormatConditions.Add Type:=xlCellValue, Operator:=xlEqual, Formula1:="Medium"
        With .FormatConditions(.FormatConditions.Count)
            .Interior.Color = RGB(255, 255, 0) ' Yellow
        End With

        ' Low priority
        .FormatConditions.Add Type:=xlCellValue, Operator:=xlEqual, Formula1:="Low"
        With .FormatConditions(.FormatConditions.Count)
            .Interior.Color = RGB(146, 208, 80) ' Light Green
        End With
    End With
    Debug.Print "Priority formatting applied (Critical=Red, High=Orange, Medium=Yellow, Low=Green)"

    ' === CONSUMPTION ALERT FORMATTING (Column T) ===
    With ws.Range("T2:T1000")
        ' NO CONSUMPTION - URGENT
        .FormatConditions.Add Type:=xlCellValue, Operator:=xlEqual, Formula1:="NO CONSUMPTION - URGENT"
        With .FormatConditions(.FormatConditions.Count)
            .Interior.Color = RGB(255, 0, 0) ' Red
            .Font.Color = RGB(255, 255, 255) ' White text
            .Font.Bold = True
        End With

        ' NO WATER - Check Meter
        .FormatConditions.Add Type:=xlCellValue, Operator:=xlEqual, Formula1:="NO WATER - Check Meter"
        With .FormatConditions(.FormatConditions.Count)
            .Interior.Color = RGB(255, 165, 0) ' Orange
        End With

        ' NO ELECTRICITY - Check Meter
        .FormatConditions.Add Type:=xlCellValue, Operator:=xlEqual, Formula1:="NO ELECTRICITY - Check Meter"
        With .FormatConditions(.FormatConditions.Count)
            .Interior.Color = RGB(255, 255, 0) ' Yellow
        End With

        ' Normal Consumption
        .FormatConditions.Add Type:=xlCellValue, Operator:=xlEqual, Formula1:="Normal Consumption"
        With .FormatConditions(.FormatConditions.Count)
            .Interior.Color = RGB(144, 238, 144) ' Light Green
        End With
    End With
    Debug.Print "Consumption alert formatting applied (Urgent=Red, NoWater=Orange, NoElectric=Yellow, Normal=Green)"

    ' === DATE-BASED FORMATTING (Column L - Target Resolution Date) ===
    With ws.Range("L2:L1000")
        ' Overdue dates (past due)
        .FormatConditions.Add Type:=xlExpression, Formula1:="=AND(L2<>"""",L2<TODAY())"
        With .FormatConditions(.FormatConditions.Count)
            .Interior.Color = RGB(255, 199, 206) ' Light Red
            .Font.Bold = True
        End With

        ' Due today
        .FormatConditions.Add Type:=xlExpression, Formula1:="=L2=TODAY()"
        With .FormatConditions(.FormatConditions.Count)
            .Interior.Color = RGB(255, 255, 0) ' Yellow
            .Font.Bold = True
        End With

        ' Due within 3 days
        .FormatConditions.Add Type:=xlExpression, Formula1:="=AND(L2>TODAY(),L2<=TODAY()+3)"
        With .FormatConditions(.FormatConditions.Count)
            .Interior.Color = RGB(255, 235, 156) ' Light Yellow
        End With
    End With
    Debug.Print "Date formatting applied (Overdue=Red, Today=Yellow, Soon=Light Yellow)"

    Debug.Print "All conditional formatting applied successfully"
End Sub

Sub SetupAllDataValidation(ws As Worksheet)
    ' Set up data validation dropdowns for all relevant columns

    ' Define validation lists as variables to avoid long lines
    Dim deviceTypeList As String
    Dim issueTypeList As String
    Dim statusList As String
    Dim priorityList As String

    ' Build validation lists
    deviceTypeList = "Smart Water Meter,Smart Electricity Meter,Combined Meter,Manual Meter,IoT Water Sensor,IoT Electric Sensor"

    issueTypeList = "No Readings Received,Device Fault,Signal Issue,Battery Replacement Needed,Consumption Anomaly," & _
                   "Device Reset Required,Installation Issue,Meter Tampering,Calibration Required,Connectivity Problem,Data Corruption"

    statusList = "New,In Progress,Waiting for Parts,Waiting for Quote,Waiting for Technician,Resolved,Closed,Escalated,On Hold"

    priorityList = "Critical,High,Medium,Low"

    ' Device Type validation (Column E)
    With ws.Range("E2:E1000").Validation
        .Delete
        .Add Type:=xlValidateList, AlertStyle:=xlValidAlertStop, Formula1:=deviceTypeList
    End With
    Debug.Print "Device Type validation applied to Column E"

    ' Issue Type validation (Column F)
    With ws.Range("F2:F1000").Validation
        .Delete
        .Add Type:=xlValidateList, AlertStyle:=xlValidAlertStop, Formula1:=issueTypeList
    End With
    Debug.Print "Issue Type validation applied to Column F"

    ' Status validation (Column J)
    With ws.Range("J2:J1000").Validation
        .Delete
        .Add Type:=xlValidateList, AlertStyle:=xlValidAlertStop, Formula1:=statusList
    End With
    Debug.Print "Status validation applied to Column J"

    ' Priority validation (Column K)
    With ws.Range("K2:K1000").Validation
        .Delete
        .Add Type:=xlValidateList, AlertStyle:=xlValidAlertStop, Formula1:=priorityList
    End With
    Debug.Print "Priority validation applied to Column K"

    Debug.Print "All data validation dropdowns configured"
End Sub

Sub ConfigureLayoutAndFormatting(ws As Worksheet)
    ' Configure optimal column widths and layout

    ' Set optimal column widths for utility tracking
    ws.Columns("A:A").ColumnWidth = 8   ' Issue ID
    ws.Columns("B:B").ColumnWidth = 12  ' Date Logged
    ws.Columns("C:C").ColumnWidth = 18  ' Complex Name
    ws.Columns("D:D").ColumnWidth = 12  ' Unit Number
    ws.Columns("E:E").ColumnWidth = 20  ' Device Type
    ws.Columns("F:F").ColumnWidth = 22  ' Issue Type
    ws.Columns("G:G").ColumnWidth = 30  ' Issue Description
    ws.Columns("H:H").ColumnWidth = 25  ' Water Reading
    ws.Columns("I:I").ColumnWidth = 25  ' Electricity Reading
    ws.Columns("J:J").ColumnWidth = 15  ' Status
    ws.Columns("K:K").ColumnWidth = 10  ' Priority
    ws.Columns("L:L").ColumnWidth = 15  ' Target Resolution Date
    ws.Columns("M:M").ColumnWidth = 15  ' Date Resolved
    ws.Columns("N:N").ColumnWidth = 25  ' Resolution Notes
    ws.Columns("O:O").ColumnWidth = 15  ' Follow-up Required
    ws.Columns("P:P").ColumnWidth = 15  ' Follow-up Completed
    ws.Columns("Q:Q").ColumnWidth = 20  ' Follow-up Notes
    ws.Columns("R:R").ColumnWidth = 12  ' Related Issue ID
    ws.Columns("S:S").ColumnWidth = 60  ' Calendar Entry
    ws.Columns("T:T").ColumnWidth = 20  ' Consumption Alert

    ' Set up AutoFilter
    ws.Range("A1:T1").AutoFilter

    ' Freeze panes at row 2
    ws.Range("A2").Select
    ActiveWindow.FreezePanes = True

    ' Set zoom to 85% for better overview
    ActiveWindow.Zoom = 85

    Debug.Print "Column widths optimized, AutoFilter and freeze panes configured"
End Sub

Sub AddSampleUtilityMeterData(ws As Worksheet)
    ' Add comprehensive sample utility meter data for demonstration

    Debug.Print "Adding sample utility meter data..."

    ' Sample Row 1 - Zero electricity issue (tests the fixed formula)
    ws.Range("B2").Value = "MANUAL ENTRY" ' Date Logged - manual entry instruction
    ws.Range("C2").Value = "Lilyvale Estate"
    ws.Range("D2").Value = "Unit 013"
    ws.Range("E2").Value = "Smart Electricity Meter"
    ws.Range("F2").Value = "No Readings Received"
    ws.Range("G2").Value = "Device not registering readings after reset"
    ws.Range("H2").Value = "14.084 KL"
    ws.Range("I2").Value = "0kWh" ' This tests the fixed consumption formula
    ws.Range("J2").Value = "In Progress"
    ws.Range("K2").Value = "High"

    ' Sample Row 2 - No water issue
    ws.Range("B3").Value = "MANUAL ENTRY"
    ws.Range("C3").Value = "Kleinbach"
    ws.Range("D3").Value = "Unit 015"
    ws.Range("E3").Value = "Smart Water Meter"
    ws.Range("F3").Value = "Device Fault"
    ws.Range("G3").Value = "Meter showing erratic readings"
    ws.Range("H3").Value = "0 KL"
    ws.Range("I3").Value = "25 kWh"
    ws.Range("J3").Value = "New"
    ws.Range("K3").Value = "Medium"

    ' Sample Row 3 - No consumption (both zero)
    ws.Range("B4").Value = "MANUAL ENTRY"
    ws.Range("C4").Value = "Riverside Complex"
    ws.Range("D4").Value = "Unit 008"
    ws.Range("E4").Value = "Combined Meter"
    ws.Range("F4").Value = "Signal Issue"
    ws.Range("G4").Value = "Complete connectivity failure - no readings"
    ws.Range("H4").Value = "0KL"
    ws.Range("I4").Value = "0kWh"
    ws.Range("J4").Value = "Escalated"
    ws.Range("K4").Value = "Critical"

    ' Sample Row 4 - Normal consumption
    ws.Range("B5").Value = "MANUAL ENTRY"
    ws.Range("C5").Value = "Garden Heights"
    ws.Range("D5").Value = "Unit 022"
    ws.Range("E5").Value = "Smart Water Meter"
    ws.Range("F5").Value = "Calibration Required"
    ws.Range("G5").Value = "Readings appear slightly high, needs calibration"
    ws.Range("H5").Value = "8.5 KL"
    ws.Range("I5").Value = "45 kWh"
    ws.Range("J5").Value = "Waiting for Technician"
    ws.Range("K5").Value = "Low"

    ' Sample Row 5 - Resolved issue
    ws.Range("B6").Value = "MANUAL ENTRY"
    ws.Range("C6").Value = "Sunset Towers"
    ws.Range("D6").Value = "Unit 101"
    ws.Range("E6").Value = "IoT Electric Sensor"
    ws.Range("F6").Value = "Battery Replacement Needed"
    ws.Range("G6").Value = "Low battery warning, replaced successfully"
    ws.Range("H6").Value = "12.2 KL"
    ws.Range("I6").Value = "38 kWh"
    ws.Range("J6").Value = "Resolved"
    ws.Range("K6").Value = "Medium"
    ws.Range("M6").Value = Date - 2 ' Resolved 2 days ago
    ws.Range("N6").Value = "Battery replaced, meter functioning normally"

    ' Format the manual entry notes
    ws.Range("B2:B6").Font.Italic = True
    ws.Range("B2:B6").Font.Color = RGB(128, 128, 128)

    Debug.Print "Sample utility data added (5 rows) - includes test cases for consumption formula"
End Sub

Sub FinalSetupAndOptimization(ws As Worksheet)
    ' Final setup and optimization

    ' Select cell A1 to start
    ws.Range("A1").Select

    ' Force calculation to update all formulas
    Application.Calculate

    ' Add borders to header row
    With ws.Range("A1:T1").Borders
        .LineStyle = xlContinuous
        .Weight = xlMedium
        .ColorIndex = xlAutomatic
    End With

    ' Add light borders to data area
    With ws.Range("A2:T1000").Borders
        .LineStyle = xlContinuous
        .Weight = xlThin
        .ColorIndex = xlAutomatic
    End With

    Debug.Print "Final setup completed - borders added, calculations updated"
End Sub

Sub DisplayCreationCompletionMessage()
    ' Display comprehensive completion message - built in sections to avoid line continuation issues

    Dim completionMessage As String
    Dim msgPart1 As String
    Dim msgPart2 As String
    Dim msgPart3 As String
    Dim msgPart4 As String
    Dim msgPart5 As String

    ' Build message in manageable parts
    msgPart1 = "🎉 ENHANCED UTILITIES TRACKER CREATED SUCCESSFULLY!" & vbCrLf & vbCrLf
    msgPart1 = msgPart1 & "✅ WHAT'S BEEN CREATED:" & vbCrLf
    msgPart1 = msgPart1 & "• Main Issue Tracker (20 columns with full functionality)" & vbCrLf
    msgPart1 = msgPart1 & "• Archive Control (30-day archive management)" & vbCrLf
    msgPart1 = msgPart1 & "• Trend Analysis (consumption patterns by complex)" & vbCrLf
    msgPart1 = msgPart1 & "• Weekly Summary (key metrics and alerts)" & vbCrLf & vbCrLf

    msgPart2 = "🔧 FEATURES IMPLEMENTED:" & vbCrLf
    msgPart2 = msgPart2 & "• FIXED consumption formula (detects '0kWh' format)" & vbCrLf
    msgPart2 = msgPart2 & "• Manual date entry system (no circular references)" & vbCrLf
    msgPart2 = msgPart2 & "• Comprehensive conditional formatting" & vbCrLf
    msgPart2 = msgPart2 & "• Data validation dropdowns" & vbCrLf
    msgPart2 = msgPart2 & "• Overdue task highlighting" & vbCrLf
    msgPart2 = msgPart2 & "• Sample utility meter data" & vbCrLf & vbCrLf

    msgPart3 = "📋 HOW TO USE:" & vbCrLf
    msgPart3 = msgPart3 & "1. MANUALLY enter dates in Column B (e.g., 7/14/2024)" & vbCrLf
    msgPart3 = msgPart3 & "2. Enter Complex Name and Unit Number" & vbCrLf
    msgPart3 = msgPart3 & "3. Use dropdowns for Device Type, Issue Type, Status, Priority" & vbCrLf
    msgPart3 = msgPart3 & "4. Enter water/electricity readings" & vbCrLf
    msgPart3 = msgPart3 & "5. Calendar Entry and Consumption Alert auto-populate" & vbCrLf & vbCrLf

    msgPart4 = "🚨 CONSUMPTION ALERTS:" & vbCrLf
    msgPart4 = msgPart4 & "• Red: NO CONSUMPTION - URGENT" & vbCrLf
    msgPart4 = msgPart4 & "• Orange: NO WATER - Check Meter" & vbCrLf
    msgPart4 = msgPart4 & "• Yellow: NO ELECTRICITY - Check Meter" & vbCrLf
    msgPart4 = msgPart4 & "• Green: Normal Consumption" & vbCrLf & vbCrLf

    msgPart5 = "💡 TIPS:" & vbCrLf
    msgPart5 = msgPart5 & "• Use Ctrl+; to quickly enter today's date" & vbCrLf
    msgPart5 = msgPart5 & "• Overdue items automatically highlight in red" & vbCrLf
    msgPart5 = msgPart5 & "• Check Immediate window (Ctrl+G) for creation logs" & vbCrLf & vbCrLf
    msgPart5 = msgPart5 & "Your Enhanced Utilities Tracker is ready for production use!"

    ' Combine all message parts
    completionMessage = msgPart1 & msgPart2 & msgPart3 & msgPart4 & msgPart5

    MsgBox completionMessage, vbInformation, "Enhanced Utilities Tracker Created"
End Sub

' ===================================================================
' TESTING FUNCTION
' ===================================================================

Sub TestConsumptionFormulaInNewTracker()
    ' Test the consumption formula in the newly created tracker

    Debug.Print String(60, "=")
    Debug.Print "TESTING CONSUMPTION FORMULA IN NEW TRACKER"
    Debug.Print String(60, "=")

    Dim ws As Worksheet
    Set ws = ActiveSheet

    If ws.Name <> "Main Issue Tracker" Then
        Debug.Print "❌ Please run this test from the Main Issue Tracker worksheet"
        Exit Sub
    End If

    ' Test the sample data results
    Debug.Print "Testing sample data consumption analysis results..."
    Debug.Print ""

    ' Test Row 2: Water="14.084 KL", Electric="0kWh" (should be NO ELECTRICITY)
    Debug.Print "Row 2 Test - Water: " & ws.Range("H2").Value & ", Electric: " & ws.Range("I2").Value
    Debug.Print "Expected: NO ELECTRICITY - Check Meter"
    Debug.Print "Actual: " & ws.Range("T2").Value
    If ws.Range("T2").Value = "NO ELECTRICITY - Check Meter" Then
        Debug.Print "✅ PASSED"
    Else
        Debug.Print "❌ FAILED"
    End If
    Debug.Print ""

    ' Test Row 3: Water="0 KL", Electric="25 kWh" (should be NO WATER)
    Debug.Print "Row 3 Test - Water: " & ws.Range("H3").Value & ", Electric: " & ws.Range("I3").Value
    Debug.Print "Expected: NO WATER - Check Meter"
    Debug.Print "Actual: " & ws.Range("T3").Value
    If ws.Range("T3").Value = "NO WATER - Check Meter" Then
        Debug.Print "✅ PASSED"
    Else
        Debug.Print "❌ FAILED"
    End If
    Debug.Print ""

    ' Test Row 4: Water="0KL", Electric="0kWh" (should be NO CONSUMPTION)
    Debug.Print "Row 4 Test - Water: " & ws.Range("H4").Value & ", Electric: " & ws.Range("I4").Value
    Debug.Print "Expected: NO CONSUMPTION - URGENT"
    Debug.Print "Actual: " & ws.Range("T4").Value
    If ws.Range("T4").Value = "NO CONSUMPTION - URGENT" Then
        Debug.Print "✅ PASSED"
    Else
        Debug.Print "❌ FAILED"
    End If
    Debug.Print ""

    Debug.Print String(60, "=")
    Debug.Print "CONSUMPTION FORMULA TESTING COMPLETED"
    Debug.Print String(60, "=")

    MsgBox "Consumption formula testing completed. Check Immediate window (Ctrl+G) for detailed results.", vbInformation
End Sub

' ===================================================================
' REFACTORING VERIFICATION FUNCTION
' ===================================================================

Sub VerifyRefactoredFormulas()
    ' Verify that all formulas work correctly after refactoring

    Debug.Print String(60, "=")
    Debug.Print "VERIFYING REFACTORED FORMULAS"
    Debug.Print String(60, "=")

    Dim ws As Worksheet
    Set ws = ActiveSheet

    If ws.Name <> "Main Issue Tracker" Then
        Debug.Print "❌ Please run this test from the Main Issue Tracker worksheet"
        MsgBox "Please run this test from the Main Issue Tracker worksheet", vbCritical
        Exit Sub
    End If

    Dim allTestsPassed As Boolean
    allTestsPassed = True

    ' Test 1: Issue ID Formula (Column A)
    Debug.Print "Testing Issue ID formula (Column A)..."
    If ws.Range("A2").HasFormula Then
        If InStr(ws.Range("A2").Formula, "ISS-") > 0 Then
            Debug.Print "✅ Issue ID formula working correctly"
        Else
            Debug.Print "❌ Issue ID formula incorrect"
            allTestsPassed = False
        End If
    Else
        Debug.Print "❌ Issue ID formula missing"
        allTestsPassed = False
    End If

    ' Test 2: Target Resolution Date Formula (Column L)
    Debug.Print "Testing Target Resolution Date formula (Column L)..."
    If ws.Range("L2").HasFormula Then
        If InStr(ws.Range("L2").Formula, "WEEKDAY") > 0 Then
            Debug.Print "✅ Target Resolution Date formula working correctly"
        Else
            Debug.Print "❌ Target Resolution Date formula incorrect"
            allTestsPassed = False
        End If
    Else
        Debug.Print "❌ Target Resolution Date formula missing"
        allTestsPassed = False
    End If

    ' Test 3: Calendar Entry Formula (Column S)
    Debug.Print "Testing Calendar Entry formula (Column S)..."
    If ws.Range("S2").HasFormula Then
        If InStr(ws.Range("S2").Formula, "Issue:") > 0 And InStr(ws.Range("S2").Formula, "Water:") > 0 Then
            Debug.Print "✅ Calendar Entry formula working correctly"
        Else
            Debug.Print "❌ Calendar Entry formula incorrect"
            allTestsPassed = False
        End If
    Else
        Debug.Print "❌ Calendar Entry formula missing"
        allTestsPassed = False
    End If

    ' Test 4: Consumption Analysis Formula (Column T)
    Debug.Print "Testing Consumption Analysis formula (Column T)..."
    If ws.Range("T2").HasFormula Then
        If InStr(ws.Range("T2").Formula, "0kWh") > 0 And InStr(ws.Range("T2").Formula, "NO ELECTRICITY") > 0 Then
            Debug.Print "✅ Consumption Analysis formula working correctly (includes 0kWh fix)"
        Else
            Debug.Print "❌ Consumption Analysis formula incorrect or missing 0kWh fix"
            allTestsPassed = False
        End If
    Else
        Debug.Print "❌ Consumption Analysis formula missing"
        allTestsPassed = False
    End If

    ' Test 5: Data Validation
    Debug.Print "Testing Data Validation..."
    On Error Resume Next
    Dim validationTest As String
    validationTest = ws.Range("E2").Validation.Formula1
    If Err.Number = 0 And Len(validationTest) > 0 Then
        Debug.Print "✅ Data validation working correctly"
    Else
        Debug.Print "❌ Data validation not working"
        allTestsPassed = False
    End If
    On Error GoTo 0

    ' Test 6: Conditional Formatting
    Debug.Print "Testing Conditional Formatting..."
    If ws.Range("A2").FormatConditions.Count > 0 Then
        Debug.Print "✅ Conditional formatting applied"
    Else
        Debug.Print "❌ Conditional formatting missing"
        allTestsPassed = False
    End If

    ' Final Result
    Debug.Print ""
    Debug.Print String(60, "=")
    If allTestsPassed Then
        Debug.Print "🎉 ALL REFACTORED FORMULAS VERIFIED SUCCESSFULLY!"
        Debug.Print "The refactoring maintained all functionality while improving code structure."
        MsgBox "✅ All refactored formulas verified successfully!" & vbCrLf & vbCrLf & _
               "The refactoring was successful - all functionality maintained while improving code structure.", vbInformation
    Else
        Debug.Print "⚠️ SOME TESTS FAILED - REVIEW ISSUES ABOVE"
        MsgBox "⚠️ Some formula tests failed." & vbCrLf & vbCrLf & _
               "Check the Immediate window (Ctrl+G) for detailed results.", vbCritical
    End If
    Debug.Print String(60, "=")
End Sub
