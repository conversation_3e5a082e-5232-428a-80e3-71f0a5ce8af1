//@version=6
indicator("Function Syntax Test v7 Final", overlay=true)

// This script uses the definitive syntax learned from PAHFramework-1.pine (a v6 library).
// The syntax is a direct declaration, not a variable assignment.
// SYNTAX: functionName(type paramName) => body

// --- Test Function with correct v6 direct declaration syntax ---
testFunction(int inputNumber) =>
    prefix = "Input was: "
    result = prefix + str.tostring(inputNumber)
    result // Implicit return

// --- Main Logic ---
if barstate.islastconfirmed
    // Call the function.
    string result = testFunction(123)
    
    // This must compile.
    label.new(bar_index, high, result, color=color.green, textcolor=color.white)
