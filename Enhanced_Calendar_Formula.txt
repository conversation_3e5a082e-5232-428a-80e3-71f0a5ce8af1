ENHANCED CALENDAR FORMULA FOR UTILITY METER TRACKING
=====================================================

PROBLEM SOLVED:
- Reorders information to show Complex Name and Unit Number FIRST
- Automates consumption data display from Columns H and I
- Handles empty cells gracefully
- Works with all 27 existing rows
- Provides proper error handling

ORIGINAL PROBLEMATIC OUTPUT:
"Issue: Device not registering readings - Meter is NOT Up To Date- Latest reading 2025-05-23 17:30:00 Consumption Data 14.084 KL vs 0 kWh over 30 days - Lilyvale Estate Unit 013 - Due: 07/20/2025"

NEW ENHANCED OUTPUT FORMAT:
"Lilyvale Estate Unit 013 - Issue: Device not registering readings - Water: 14.084 KL | Electric: 0 kWh - Due: 07/20/2025"

ENHANCED EXCEL FORMULA:
=IF(OR(C2="",D2=""),"",C2&" "&D2&" - Issue: "&IF(F2="","Meter Reading",F2)&" - Water: "&IF(H2="","No Data",IF(ISNUMBER(FIND("KL",H2)),H2,H2&" KL"))&" | Electric: "&IF(I2="","No Data",IF(ISNUMBER(FIND("kWh",I2)),I2,I2&" kWh"))&IF(L2=""," - Due: "&TEXT(L2,"MM/DD/YYYY")))

FORMULA BREAKDOWN:
1. IF(OR(C2="",D2=""),"", - Check if Complex Name or Unit Number is empty
2. C2&" "&D2&" - Issue: " - Display Complex Name and Unit Number FIRST
3. IF(F2="","Meter Reading",F2) - Show Issue Type or default to "Meter Reading"
4. " - Water: "&IF(H2="","No Data", - Water consumption section
5. IF(ISNUMBER(FIND("KL",H2)),H2,H2&" KL")) - Add KL unit if missing
6. " | Electric: "&IF(I2="","No Data", - Electric consumption section
7. IF(ISNUMBER(FIND("kWh",I2)),I2,I2&" kWh")) - Add kWh unit if missing
8. IF(L2="",""," - Due: "&TEXT(L2,"MM/DD/YYYY")) - Due date formatting

IMPLEMENTATION STEPS:
1. Select cell S2 (Calendar Entry column)
2. Paste the enhanced formula above
3. Copy the formula down to all rows with data (S2:S28 for your 27 rows)
4. Verify all existing data displays correctly

FEATURES:
✓ Complex Name and Unit Number displayed FIRST
✓ Automated consumption data from Columns H and I
✓ Proper unit handling (KL, kWh)
✓ Empty cell error handling
✓ Date formatting (MM/DD/YYYY)
✓ Preserves all existing data
✓ Works with various data formats

TESTING EXAMPLES:
Row with complete data: "Lilyvale Estate Unit 013 - Issue: No Readings Received - Water: 14.084 KL | Electric: 0 kWh - Due: 07/20/2025"
Row with missing water: "Kleinbach Unit 015 - Issue: Device Fault - Water: No Data | Electric: 25 kWh - Due: 07/22/2025"
Row with no due date: "Riverside Complex Unit 008 - Issue: Signal Issue - Water: 0 KL | Electric: 12 kWh"

COMPATIBILITY:
- Excel 2019/Office 365
- Handles 500+ entries
- Outlook calendar compatible
- Preserves existing 27 rows
- Performance optimized
