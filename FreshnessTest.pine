//@version=6
indicator("Freshness and Storyline Test", overlay=true)

// --- Mock Inputs and Constants ---
int rejectionLookback = 3
string htf1 = "D"
string htf2 = "W"
string TF_CHART = "Chart"
string TF_HTF1 = "HTF1"
string TF_HTF2 = "HTF2"
string LEVEL_V = "V"
string LEVEL_A = "A"
string LEVEL_BG = "BG"
string LEVEL_XG = "XG"
string LEVEL_RBS = "RBS"
string LEVEL_SBR = "SBR"

// --- Mock UDT ---
type SnrLevel
    float price
    string patternType
    string timeframeSource

// --- Global State ---
var string htf1_storyline_status = "Idle"
var string htf2_storyline_status = "Idle"
var int htf1_rejection_time = na
var int htf2_rejection_time = na

// --- Function Dependencies ---
getLowerTimeframe(htf) =>
    switch htf
        "W"  => "D"
        "D"  => "240"
        => ""

getTfString(tfSource) =>
    switch tfSource
        TF_HTF1 => htf1
        TF_HTF2 => htf2
        => ""

// --- Final, Corrected Function ---
// This function now returns a tuple: [bool, string, int] (isConfirmed, statusMessage, rejectionTimestamp)
checkStorylineConfirmation(level, rejectionTimeMemory) =>
    storylineConfirmed = false
    statusMessage = "Idle"
    rejectionTimestamp = rejectionTimeMemory // Pass memory in, return it back out

    if level.timeframeSource != TF_CHART
        actualTf = getTfString(level.timeframeSource)
        if actualTf != ""
            isSupport = level.patternType == LEVEL_V or level.patternType == LEVEL_BG or level.patternType == LEVEL_RBS
            isResistance = level.patternType == LEVEL_A or level.patternType == LEVEL_XG or level.patternType == LEVEL_SBR
            lowerTf = getLowerTimeframe(actualTf)

            // If a rejection has already been recorded, we are awaiting a breakout
            if not na(rejectionTimestamp)
                statusMessage := "Awaiting " + lowerTf + " Breakout"
                if lowerTf != "" and timeframe.in_seconds(timeframe.period) >= timeframe.in_seconds(lowerTf)
                    ltf_closes = request.security_lower_tf(syminfo.tickerid, lowerTf, close)
                    if array.size(ltf_closes) > 0
                        for ltf_c in ltf_closes
                            // Check for breakout confirmation
                            if (isSupport and ltf_c > level.price) or (isResistance and ltf_c < level.price)
                                storylineConfirmed := true
                                statusMessage := (isSupport ? "Bullish" : "Bearish") + " Confirmed"
                                rejectionTimestamp := na // Reset memory on confirmation
                                break
            else
                // No rejection recorded, so we are awaiting one. Scan for it.
                statusMessage := "Awaiting " + actualTf + " Rejection"
                [htf_opens, htf_closes, htf_times] = request.security(syminfo.tickerid, actualTf, [open, close, time], lookahead=barmerge.lookahead_off)
                
                for i = 0 to rejectionLookback - 1
                    if na(htf_closes[i]) or na(htf_opens[i])
                        continue
                    
                    isRejectedOnThisBar = (isSupport and htf_closes[i] > level.price and htf_opens[i] < level.price) or (isResistance and htf_closes[i] < level.price and htf_opens[i] > level.price)
                    
                    if isRejectedOnThisBar
                        rejectionTimestamp := htf_times[i] // Set memory
                        statusMessage := "Awaiting " + lowerTf + " Breakout"
                        break
    
    [storylineConfirmed, statusMessage, rejectionTimestamp]

// --- Test Execution ---
if barstate.islast
    mockLevel = SnrLevel.new(close, LEVEL_A, TF_HTF1)
    
    // On the first run, htf1_rejection_time is 'na'. The function will look for a rejection.
    // On subsequent runs, if a rejection was found, it will be stored in htf1_rejection_time.
    [isConfirmed, newStatus, newRejectionTime] = checkStorylineConfirmation(mockLevel, htf1_rejection_time)
    
    // Manually update the global variables
    htf1_storyline_status := newStatus
    htf1_rejection_time := newRejectionTime
    
    // Display the results
    label.new(bar_index, high, "Confirmed: " + str.tostring(isConfirmed) + "\nStatus: " + htf1_storyline_status + "\nRejection Time: " + str.tostring(htf1_rejection_time))
