// This Pine Script™ code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © BigBeluga
//
//@version=6
indicator('Price Action Smart Money Concepts [BigBeluga]', 'Price Action Smart Money Concepts [BigBeluga]', overlay = true, max_bars_back = 5000, max_boxes_count = 500, max_labels_count = 500, max_lines_count = 500, max_polylines_count = 100)
plot(na)


_ = ' ------------         ––––––––––––––––––––––––––   INPUTS   –––––––––––––––––––––––––––  ------------                                                                                                                                                                        '
windowsis = input.bool(true, 'Window                                      ', inline = 'kla', group = 'MARKET STRUCTURE')
mswindow = input.int(5000, '', tooltip = 'Limit market structure calculation to improve memory speed time', group = 'MARKET STRUCTURE', inline = 'kla', minval = 1000)
showSwing = input.bool(true, 'Swing                                         ', inline = 'scss', group = 'MARKET STRUCTURE')
swingLimit = input.int(100, '', tooltip = '[INPUT] Limit swing structure to tot bars back', inline = 'scss', group = 'MARKET STRUCTURE', minval = 10, maxval = 200)
swingcssup = input.color(#089981, '', inline = 'scss', group = 'MARKET STRUCTURE')
swingcssdn = input.color(#f23645, '', inline = 'scss', group = 'MARKET STRUCTURE')


showInternal = input.bool(false, 'Internal', inline = 'icss', group = 'MARKET STRUCTURE')
internalDate = input.time(0, '', tooltip = '[INPUT] Start date of the internal structure', inline = 'icss', group = 'MARKET STRUCTURE', confirm = false)
interncssup = input.color(color.blue, '', inline = 'icss', group = 'MARKET STRUCTURE')
interncssdn = input.color(color.purple, '', inline = 'icss', group = 'MARKET STRUCTURE')



showMapping = input.bool(false, 'Mapping Structure                       ', inline = 'mapping', group = 'MARKET STRUCTURE')
mappingStyle = input.string('----', '', options = ['⎯⎯⎯⎯', '----'], inline = 'mapping', group = 'MARKET STRUCTURE')
mappingcss = input.color(color.silver, '', tooltip = 'Display Mapping Structure', inline = 'mapping', group = 'MARKET STRUCTURE')
candlecss = input.bool(false, 'Color Candles                              ', tooltip = 'Color candle based on trend detection system', group = 'MARKET STRUCTURE', inline = 'txt')
mstext = input.string('Tiny', '', options = ['Tiny', 'Small', 'Normal', 'Large', 'Huge'], inline = 'txt', group = 'MARKET STRUCTURE')
showpdzone = input.bool(false, 'Premium/Discount                      ', 'Show Premium discount zone', 'pd', 'MARKET STRUCTURE')
zonelen = input.int(20, '', inline = 'pd', group = 'MARKET STRUCTURE', minval = 5)
fibcss = input.color(#fbc02d, '', inline = 'pd', group = 'MARKET STRUCTURE')
msmode = input.string('Adjusted Points', 'Algorithmic Logic                                ', options = ['Extreme Points', 'Adjusted Points'], inline = 'node', group = 'MARKET STRUCTURE')
mslen = input.int(5, '', inline = 'node', group = 'MARKET STRUCTURE', minval = 2)
showhl = input.bool(false, 'Strong/Weak HL', 'Display Volumetric Strong/Weak High and low', 'hl', 'MARKET STRUCTURE')
buildsweep = input.bool(true, 'Build Sweep (x)', 'Build sweep on market structure', 'znc', 'MARKET STRUCTURE')
msbubble = input.bool(true, 'Bubbles', tooltip = 'Display Circle Bubbles', inline = 'bubbles', group = 'MARKET STRUCTURE')







obshow = input.bool(true, 'Show Last', tooltip = 'Show Last number of orderblock', group = 'VOLUMETRIC ORDER BLOCKS', inline = 'obshow')
oblast = input.int(5, '', group = 'VOLUMETRIC ORDER BLOCKS', inline = 'obshow', minval = 0)
obupcs = input.color(color.new(#089981, 90), '', inline = 'obshow', group = 'VOLUMETRIC ORDER BLOCKS')
obdncs = input.color(color.new(#f23645, 90), '', inline = 'obshow', group = 'VOLUMETRIC ORDER BLOCKS')

obshowactivity = input.bool(true, 'Show Buy/Sell Activity         ', inline = 'act', group = 'VOLUMETRIC ORDER BLOCKS', tooltip = 'Display internal buy and sell activity')
obactup = input.color(color.new(#089981, 50), '', inline = 'act', group = 'VOLUMETRIC ORDER BLOCKS')
obactdn = input.color(color.new(#f23645, 50), '', inline = 'act', group = 'VOLUMETRIC ORDER BLOCKS')

obshowbb = input.bool(false, 'Show Breakers                     ', inline = 'bb', group = 'VOLUMETRIC ORDER BLOCKS', tooltip = 'Display Breakers')
bbup = input.color(color.new(#089981, 100), '', inline = 'bb', group = 'VOLUMETRIC ORDER BLOCKS')
bbdn = input.color(color.new(#f23645, 100), '', inline = 'bb', group = 'VOLUMETRIC ORDER BLOCKS')


obmode = input.string('Length', 'Construction ', options = ['Length', 'Full'], tooltip = '[Length] Use Length to adjust cordinate of the orderblocks\n[Full] Use whole candle body', inline = 'atr', group = 'VOLUMETRIC ORDER BLOCKS')
len = input.int(5, '', inline = 'atr', group = 'VOLUMETRIC ORDER BLOCKS', minval = 1)

obmiti = input.string('Close', 'Mitigation Method                  ', options = ['Close', 'Wick', 'Avg'], tooltip = 'Mitigation method for when to trigger order blocks', group = 'VOLUMETRIC ORDER BLOCKS')
obtxt = input.string('Normal', 'Metric Size                               ', options = ['Tiny', 'Small', 'Normal', 'Large', 'Huge', 'Auto'], tooltip = 'Order block Metrics text size', inline = 'txt', group = 'VOLUMETRIC ORDER BLOCKS')


showmetric = input.bool(true, 'Show Metrics', group = 'VOLUMETRIC ORDER BLOCKS')
showline = input.bool(true, 'Show Mid-Line', group = 'VOLUMETRIC ORDER BLOCKS')
overlap = input.bool(true, 'Hide Overlap', group = 'VOLUMETRIC ORDER BLOCKS', inline = 'ov')
wichlap = input.string('Recent', '', options = ['Recent', 'Old'], inline = 'ov', group = 'VOLUMETRIC ORDER BLOCKS')



fvg_enable = input.bool(false, '        ', inline = '1', group = 'FAIR VALUE GAP', tooltip = 'Display fair value gap')
what_fvg = input.string('FVG', '', inline = '1', group = 'FAIR VALUE GAP', tooltip = 'Display fair value gap', options = ['FVG', 'Breakers'])
fvg_num = input.int(5, 'Show Last  ', inline = '1a', group = 'FAIR VALUE GAP', tooltip = 'Number of fvg to show', minval = 0)
fvg_upcss = input.color(color.new(#089981, 80), '', inline = '1', group = 'FAIR VALUE GAP')
fvg_dncss = input.color(color.new(#f23645, 80), '', inline = '1', group = 'FAIR VALUE GAP')
fvgbbup = input.color(color.new(#089981, 100), '', inline = '1', group = 'FAIR VALUE GAP')
fvgbbdn = input.color(color.new(#f23645, 100), '', inline = '1', group = 'FAIR VALUE GAP')
fvg_src = input.string('Close', 'Mitigation  ', inline = '3', group = 'FAIR VALUE GAP', tooltip = '[Close] Use the close of the body as trigger\n\n[Wick] Use the extreme point of the body as trigger', options = ['Close', 'Wick', 'Avg'])
fvg_tf = input.timeframe('', 'Timeframe ', inline = '4', group = 'FAIR VALUE GAP', tooltip = 'Timeframe of the fair value gap')
fvgthresh = input.float(0, 'Threshold   ', tooltip = 'Filter out non significative FVG', group = 'FAIR VALUE GAP', inline = 'asd', minval = 0, maxval = 2, step = 0.1)
fvgoverlap = input.bool(true, 'Hide Overlap', 'Hide overlapping FVG', group = 'FAIR VALUE GAP')
fvgline = input.bool(true, 'Show Mid-Line', group = 'FAIR VALUE GAP')
fvgextend = input.bool(false, 'Extend FVG', group = 'FAIR VALUE GAP')
dispraid = input.bool(false, 'Display Raids', inline = 'raid', group = 'FAIR VALUE GAP')





dispSFP = input.bool(false, 'Show Last       ', tooltip = 'Display Swing Failure Pattern', inline = 'sfp', group = 'Swing Failure Pattern')
sfpshow = input.int(5, '', inline = 'sfp', group = 'Swing Failure Pattern', minval = 0)

showdev = input.bool(false, 'Deviation Area', inline = 'dev', group = 'Swing Failure Pattern')

brsfpcs = input.color(color.red, '', inline = 'sfp', group = 'Swing Failure Pattern')
blsfpcs = input.color(color.green, '', inline = 'sfp', group = 'Swing Failure Pattern')

spfuselen = input.bool(true, 'Length           ', inline = 'sfx', group = 'Swing Failure Pattern')
sfplen = input.int(5, '', tooltip = 'Adjust SFP', group = 'Swing Failure Pattern', inline = 'sfx', minval = 0)

sfpuse = input.bool(false, 'MTF               ', tooltip = 'MTF SFP', inline = 'sfp4', group = 'Swing Failure Pattern')
sfptf = input.timeframe('', '', inline = 'sfp4', group = 'Swing Failure Pattern')

sfpfilter = input.string('None', 'Filtering                 ', options = ['None', 'Trend Following', 'Counter Trade'], tooltip = 'Filtering for SFP', inline = 'sfp1', group = 'Swing Failure Pattern')


sfpthresh = input.float(1, 'Threshold               ', options = [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1, 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 1.9, 2], tooltip = 'Volume Threshold for SFP', inline = 'sfp2', group = 'Swing Failure Pattern')



devstyle = input.string('⎯⎯⎯⎯', '', options = ['⎯⎯⎯⎯', '----'], inline = 'dev', group = 'Swing Failure Pattern')
devcss = input.color(color.white, '', tooltip = 'Display deviation projection for SFP', inline = 'dev', group = 'Swing Failure Pattern')

// dev1 = input.float(2  , "First Projection       ", group = "Swing Failure Pattern", inline = "d", minval = 0)
// dev2 = input.float(2.5, "Second Projection   ", group = "Swing Failure Pattern", inline = "n", minval = 0)
devsize = input.string('Small', 'Text Size                 ', options = ['Tiny', 'Small', 'Normal', 'Large', 'Huge', 'Auto'], tooltip = 'Text Size', inline = 'ak', group = 'Swing Failure Pattern')

devlvl = input.bool(true, 'Show Level', group = 'Swing Failure Pattern')
devfill = input.bool(true, 'Fill Area', group = 'Swing Failure Pattern')







ehlshow = input.bool(true, 'Equal H&L           ', tooltip = 'Display EQH and EQL', inline = 'ehl', group = 'LIQUIDITY CONCEPTS')
ehlmode = input.string('Short-Term', '', options = ['Short-Term', 'Mid-Term', 'Long-Term'], inline = 'ehl', group = 'LIQUIDITY CONCEPTS')
ehldowncs = input.color(#6fad6f, '', inline = 'ehl', group = 'LIQUIDITY CONCEPTS')
ehlupcs = input.color(#ce636d, '', inline = 'ehl', group = 'LIQUIDITY CONCEPTS')

liqprint = input.bool(true, 'Liquidity Prints                                ', 'Show Liquidity Grabs on the chart', inline = 'print', group = 'LIQUIDITY CONCEPTS')
printupcs = input.color(color.orange, '', inline = 'print', group = 'LIQUIDITY CONCEPTS')
printdowncs = input.color(color.blue, '', inline = 'print', group = 'LIQUIDITY CONCEPTS')

showside = input.bool(false, 'Buyside & Sellside', tooltip = 'Display Liquidity side', inline = 'side', group = 'LIQUIDITY CONCEPTS')
sidemode = input.string('Area', '', ['Area', 'Line'], inline = 'side', group = 'LIQUIDITY CONCEPTS')
buysidecss = input.color(color.new(#009933, 80), '', inline = 'side', group = 'LIQUIDITY CONCEPTS')
sidesidecss = input.color(color.new(#cc3300, 80), '', inline = 'side', group = 'LIQUIDITY CONCEPTS')
showzone = input.bool(false, 'Sweep Area          ', 'Show Sweep liquidation zone', 'sz', 'LIQUIDITY CONCEPTS')
zonethresh = input.int(10, '', inline = 'sz', group = 'LIQUIDITY CONCEPTS', minval = 2)
upzone = input.color(color.new(#089981, 80), '', inline = 'sz', group = 'LIQUIDITY CONCEPTS')
dnzone = input.color(color.new(#f23645, 80), '', inline = 'sz', group = 'LIQUIDITY CONCEPTS')


// lvl_monday                       = input.bool        (false                            , "Monday"                      , inline = "q", group = "HIGHS & LOWS MTF")
lvl_daily = input.bool(false, 'Day        ', inline = '1', group = 'HIGHS & LOWS MTF')
lvl_weekly = input.bool(false, 'Week      ', inline = '2', group = 'HIGHS & LOWS MTF')
lvl_monthly = input.bool(false, 'Month     ', inline = '3', group = 'HIGHS & LOWS MTF')
lvl_quartely = input.bool(false, 'Quarterly', inline = 'c', group = 'HIGHS & LOWS MTF')
lvl_yearly = input.bool(false, 'Year        ', inline = '4', group = 'HIGHS & LOWS MTF')

// css_monday                           = input.color       (color.blue                     , ""                            , inline = "q", group = "HIGHS & LOWS MTF")
css_d = input.color(color.blue, '', inline = '1', group = 'HIGHS & LOWS MTF')
css_w = input.color(color.blue, '', inline = '2', group = 'HIGHS & LOWS MTF')
css_m = input.color(color.blue, '', inline = '3', group = 'HIGHS & LOWS MTF')
css_q = input.color(color.blue, '', inline = 'c', group = 'HIGHS & LOWS MTF')
css_y = input.color(color.blue, '', inline = '4', group = 'HIGHS & LOWS MTF')

// s_monday                             = input.string      ('⎯⎯⎯'                            , ''                            , inline = 'q', group = 'HIGHS & LOWS MTF'                , options = ['⎯⎯⎯', '----', '····'])
s_d = input.string('⎯⎯⎯⎯', '', inline = '1', group = 'HIGHS & LOWS MTF', options = ['⎯⎯⎯⎯', '----', '····'])
s_w = input.string('⎯⎯⎯⎯', '', inline = '2', group = 'HIGHS & LOWS MTF', options = ['⎯⎯⎯⎯', '----', '····'])
s_m = input.string('⎯⎯⎯⎯', '', inline = '3', group = 'HIGHS & LOWS MTF', options = ['⎯⎯⎯⎯', '----', '····'])
s_q = input.string('⎯⎯⎯⎯', '', inline = 'c', group = 'HIGHS & LOWS MTF', options = ['⎯⎯⎯⎯', '----', '····'])
s_y = input.string('⎯⎯⎯⎯', '', inline = '4', group = 'HIGHS & LOWS MTF', options = ['⎯⎯⎯⎯', '----', '····'])


direc = input.string('Bullish', 'Direction', ['Bullish', 'Bearish', 'Both'], group = 'ANY ALERT', inline = 'asd')

var array<bool> alertinputs = array.from(input.bool(false, 'CHoCH', inline = '1', group = 'ANY ALERT'), input.bool(false, 'BOS', inline = '2', group = 'ANY ALERT'), input.bool(false, 'CHoCH Sweep', inline = '3', group = 'ANY ALERT'), input.bool(false, 'BOS Sweep', inline = '4', group = 'ANY ALERT'), input.bool(false, 'OB Mitigated', inline = '5', group = 'ANY ALERT'), input.bool(false, 'Price Inside OB', inline = '6', group = 'ANY ALERT'), input.bool(false, 'FVG Mitigated', inline = '7', group = 'ANY ALERT'), input.bool(false, 'Raid Found', inline = '8', group = 'ANY ALERT'), input.bool(false, 'Pirce Inside FVG', inline = '9', group = 'ANY ALERT'), input.bool(false, 'SFP Created', inline = '10', group = 'ANY ALERT'), input.bool(false, 'Liquidity Print', inline = '11', group = 'ANY ALERT'), input.bool(false, 'Sweep Area', inline = '12', group = 'ANY ALERT'))

type alert
	bool b = false // If condition has been met or not
	string d = na // Bullish or Bearish direction
	string n = na // Name of alert

method check(array<bool> a) =>
    var bool b = false

    if barstate.isfirst
        for i in a
            if i
                b := true
                break

    b

method add(array<alert> a, int i) =>
    alert c = a.get(i)

    c.b ? c.d + ' ' + c.n : na

method buffer(array<bool> inputs) =>
    array<alert> a = array.new<alert>()

    for i = 0 to inputs.size() - 1 by 1
        a.unshift(alert.new())

    a

array<alert> blalerts = alertinputs.buffer()
array<alert> bralerts = alertinputs.buffer()

//}
_ = ' ------------         ––––––––––––––––––––––––––     UDT    –––––––––––––––––––––––––––  ------------                                                                                                                                                                        ' //{                                                           

type alertDetails
	bool bos = false // BOS Created
	bool choch = false // CHoCH Created
	bool bosx = false // BOS Sweep
	bool chochx = false // CHoCH Sweep
	bool obmitigated = false // Order Block Mitigated
	bool bbmitigated = false // Breaker Block Mitigated
	bool priceenteredob = false // Price Enetered Order block
	bool priceenteredbb = false // price enetered breaker block
	bool pricewithinob = false // price within order block
	bool pricewithinbb = false // price within order block
	bool fvgcreated = false // fvg created
	bool fvgmitigated = false // fvg mitigated
	bool bfvgmitigated = false // breaker fvg mitigated
	bool priceenteredfvg = false // price entered fvg
	bool priceenteredbfvg = false // price enetered breaker fvg
	bool pricewithinfvg = false // price within fvg
	bool pricewithinbfvg = false // price within breaker fvg
	bool sfpcreated = false // sfp 
	bool priceenteredarea = false // price enetered area
	bool pricewithinarea = false // price within area 
	bool areamitigated = false // area mitigated
	bool areacreated = false // area created
	bool equal = false // equal
	bool print = false // liquidity print

type hqlzone
	box pbx
	box ebx
	box lbx
	label plb
	label elb
	label lbl

type Zphl
	line top
	line bottom
	label top_label
	label bottom_label
	bool stopcross
	bool sbottomcross
	bool itopcross
	bool ibottomcross
	string txtup
	string txtdn
	float topy
	float bottomy
	float topx
	float bottomx
	float tup
	float tdn
	int tupx
	int tdnx
	float itopy
	float itopx
	float ibottomy
	float ibottomx
	float uV
	float dV

type entered
	bool normal = false
	bool breaker = false

type store
	array<line> ln
	array<label> lb
	array<box> bx
	array<linefill> lf

type structure
	int zn
	float zz
	float bos
	float choch
	int loc
	int temp
	int trend
	int start
	float main
	int xloc
	bool upsweep
	bool dnsweep
	string txt = na

type drawms
	int x1
	int x2
	float y
	string txt
	color css
	string style

type ob
	bool bull
	float top
	float btm
	float avg
	int loc
	color css
	float vol
	int dir
	int move
	int blPOS
	int brPOS
	int xlocbl
	int xlocbr
	bool isbb = false
	int bbloc

type FVG
	float top = na
	float btm = na
	int loc = bar_index
	bool isbb = false
	int bbloc = na
	bool israid = false
	float raidy = na
	int raidloc = na
	int raidx2 = na
	bool active = false
	color raidcs = na

type SFP
	float y
	int loc
	float ancor

type sfpbuildlbl
	int x
	float y
	string style
	color css
	string txt

type sfpbuildline
	int x1
	int x2
	float y
	color css
	float ancor
	int loc

type equalbuild
	int x1
	float y1
	int x2
	float y2
	color css
	string style

type equalname
	int x
	float y
	string txt
	color css
	string style

type ehl
	float pt
	int t
	float pb
	int b

type sellbuyside
	float top
	float btm
	int loc
	color css
	string txt
	float vol

type timer
	bool start = false
	int count = 0

//}
_ = '   ------------         ––––––––––––––––––––––––––    SETUP   –––––––––––––––––––––––––––  ------------                                                                                                                                                                        ' //{                                                     

var store bin = store.new(array.new<line>(), array.new<label>(), array.new<box>(), array.new<linefill>())

alert blalert = alert.new()
alert bralert = alert.new()

var entered blobenter = entered.new()
var entered brobenter = entered.new()

var entered blfvgenter = entered.new()
var entered brfvgenter = entered.new()

var entered blarea = entered.new()
var entered brarea = entered.new()

var timer lc = timer.new()


if barstate.islast
    for obj in bin.ln
        obj.delete()
    for obj in bin.lb
        obj.delete()
    for obj in bin.bx
        obj.delete()
    for obj in bin.lf
        obj.delete()

    bin.ln.clear()
    bin.lb.clear()
    bin.bx.clear()
    bin.lf.clear()




invcol = #ffffff00
hl() =>
    [high, low]

[pdh, pdl] = request.security(syminfo.tickerid, 'D', hl(), lookahead = barmerge.lookahead_on)
[pwh, pwl] = request.security(syminfo.tickerid, 'W', hl(), lookahead = barmerge.lookahead_on)
[pmh, pml] = request.security(syminfo.tickerid, 'M', hl(), lookahead = barmerge.lookahead_on)
[pqh, pql] = request.security(syminfo.tickerid, '3M', hl(), lookahead = barmerge.lookahead_on)
[pyh, pyl] = request.security(syminfo.tickerid, '12M', hl(), lookahead = barmerge.lookahead_on)



float atr = ta.atr(200) / (5 / len)
bool inZone = time >= internalDate
bool Session = inZone and not inZone[1]
//}
_ = '   ------------         ––––––––––––––––––––––––––   UTILITY  –––––––––––––––––––––––––––  ------------                                                                                                                                                                        ' //{                                                  

method txSz(string s) =>
    out = switch s
        'Tiny' => size.tiny
        'Small' => size.small
        'Normal' => size.normal
        'Large' => size.large
        'Huge' => size.huge
        'Auto' => size.auto
    out


method lstyle(string style) =>
    out = switch style
        '⎯⎯⎯⎯' => line.style_solid
        '----' => line.style_dashed
        '····' => line.style_dotted
    out


mtfphl(h, l, tf, css, pdhl_style) =>
    var line hl = line.new(na, na, na, na, xloc = xloc.bar_time, color = css, style = lstyle(pdhl_style))
    var line ll = line.new(na, na, na, na, xloc = xloc.bar_time, color = css, style = lstyle(pdhl_style))
    var label lbl = label.new(na, na, xloc = xloc.bar_time, text = str.format('P{0}L', tf), color = invcol, textcolor = css, size = size.small, style = label.style_label_left)
    var label hlb = label.new(na, na, xloc = xloc.bar_time, text = str.format('P{0}H', tf), color = invcol, textcolor = css, size = size.small, style = label.style_label_left)
    hy = ta.valuewhen(h != h[1], h, 1)
    hx = ta.valuewhen(h == high, time, 1)
    ly = ta.valuewhen(l != l[1], l, 1)
    lx = ta.valuewhen(l == low, time, 1)
    if barstate.islast
        extension = time + (time - time[1]) * 20
        line.set_xy1(hl, hx, hy)
        line.set_xy2(hl, extension, hy)
        label.set_xy(hlb, extension, hy)
        line.set_xy1(ll, lx, ly)
        line.set_xy2(ll, extension, ly)
        label.set_xy(lbl, extension, ly)


ghl() =>
    request.security(syminfo.tickerid, fvg_tf, [high[2], low[2], close[1], open[1], close, open, high, low, high[1], low[1], ta.atr(200)])


method IDMIDX(bool use_max, int loc) =>

    min = 99999999.
    max = 0.
    idx = 0

    if use_max
        for i = 0 to bar_index - loc by 1
            max := math.max(high[i], max)
            min := max == high[i] ? low[i] : min
            idx := max == high[i] ? i : idx
            idx

    else
        for i = 0 to bar_index - loc by 1
            min := math.min(low[i], min)
            max := min == low[i] ? high[i] : max
            idx := min == low[i] ? i : idx
            idx

    idx


SFPcords(string tf) =>

    indexHighTF = barstate.isrealtime ? 1 : 0
    indexCurrTF = barstate.isrealtime ? 0 : 1

    [h, h1, h2, l, l1, l2, c, v, t, n, t1] = request.security('', tf, [high[indexHighTF], high[1 + indexHighTF], high[2 + indexHighTF], low[indexHighTF], low[1 + indexHighTF], low[2 + indexHighTF], close[indexHighTF], volume[indexHighTF], time[indexHighTF], bar_index[indexHighTF], time[1 + indexHighTF]], lookahead = barmerge.lookahead_off)

    [h[indexCurrTF], h1[indexCurrTF], h2[indexCurrTF], l[indexCurrTF], l1[indexCurrTF], l2[indexCurrTF], c[indexCurrTF], v[indexCurrTF], t[indexCurrTF], n[indexCurrTF], t1[indexCurrTF]]


method find(structure ms, bool use_max, bool sweep, bool useob) =>
    min = 99999999.
    max = 0.
    idx = 0
    if not sweep
        if bar_index - ms.loc - 1 > 0
            if use_max
                for i = 0 to bar_index - ms.loc - 1 by 1
                    max := math.max(high[i], max)
                    min := max == high[i] ? low[i] : min
                    idx := max == high[i] ? i : idx
                    idx

                if useob
                    if high[idx + 1] > high[idx]
                        max := high[idx + 1]
                        min := low[idx + 1]
                        idx := idx + 1
                        idx

            else
                for i = 0 to bar_index - ms.loc - 1 by 1
                    min := math.min(low[i], min)
                    max := min == low[i] ? high[i] : max
                    idx := min == low[i] ? i : idx
                    idx

                if useob
                    if low[idx + 1] < low[idx]
                        max := high[idx + 1]
                        min := low[idx + 1]
                        idx := idx + 1
                        idx

        else
            if use_max
                for i = 0 to bar_index - ms.loc by 1
                    max := math.max(high[i], max)
                    min := max == high[i] ? low[i] : min
                    idx := max == high[i] ? i : idx
                    idx

                if useob
                    if high[idx + 1] > high[idx]
                        max := high[idx + 1]
                        min := low[idx + 1]
                        idx := idx + 1
                        idx

            else
                for i = 0 to bar_index - ms.loc by 1
                    min := math.min(low[i], min)
                    max := min == low[i] ? high[i] : max
                    idx := min == low[i] ? i : idx
                    idx

                if useob
                    if low[idx + 1] < low[idx]
                        max := high[idx + 1]
                        min := low[idx + 1]
                        idx := idx + 1
                        idx

    else
        if bar_index - ms.xloc - 1 > 0
            if use_max
                for i = 0 to bar_index - ms.xloc - 1 by 1
                    max := math.max(high[i], max)
                    min := max == high[i] ? low[i] : min
                    idx := max == high[i] ? i : idx
                    idx

                if useob
                    if high[idx + 1] > high[idx]
                        max := high[idx + 1]
                        min := low[idx + 1]
                        idx := idx + 1
                        idx

            else
                for i = 0 to bar_index - ms.xloc - 1 by 1
                    min := math.min(low[i], min)
                    max := min == low[i] ? high[i] : max
                    idx := min == low[i] ? i : idx
                    idx

                if useob
                    if low[idx + 1] < low[idx]
                        max := high[idx + 1]
                        min := low[idx + 1]
                        idx := idx + 1
                        idx

        else
            if use_max
                for i = 0 to bar_index - ms.xloc by 1
                    max := math.max(high[i], max)
                    min := max == high[i] ? low[i] : min
                    idx := max == high[i] ? i : idx
                    idx

                if useob
                    if high[idx + 1] > high[idx]
                        max := high[idx + 1]
                        min := low[idx + 1]
                        idx := idx + 1
                        idx

            else
                for i = 0 to bar_index - ms.xloc by 1
                    min := math.min(low[i], min)
                    max := min == low[i] ? high[i] : max
                    idx := min == low[i] ? i : idx
                    idx

                if useob
                    if low[idx + 1] < low[idx]
                        max := high[idx + 1]
                        min := low[idx + 1]
                        idx := idx + 1
                        idx

    idx


method fnOB(array<ob> block, bool bull, float cords, int idx) =>
    switch bull
        true => 
    	    blobenter.normal := false
    	    blobenter.breaker := false
    	    block.unshift(ob.new(true, cords, low[idx], math.avg(cords, low[idx]), time[idx], obupcs, volume[idx], close[idx] > open[idx] ? 1 : -1, 1, 1, 1, time[idx]))
        false => 

    	    brobenter.normal := false
    	    brobenter.breaker := false
    	    block.unshift(ob.new(false, high[idx], cords, math.avg(cords, high[idx]), time[idx], obdncs, volume[idx], close[idx] > open[idx] ? 1 : -1, 1, 1, 1, time[idx]))


method mitigated(array<ob> block) =>
    if barstate.isconfirmed
        for [i, stuff] in block
            if not stuff.isbb
                switch stuff.bull
                    true => 
                	    if obmiti == 'Close' ? math.min(close, open) < stuff.btm : obmiti == 'Wick' ? low < stuff.btm : obmiti == 'Avg' ? low < stuff.avg : false
                	        blalerts.set(4, alert.new(true, 'Bullish', 'OB Mitigated\n'))
                	        stuff.isbb := true
                	        stuff.bbloc := time
                	        if not obshowbb
                	            block.remove(i)
                    false => 

                	    if obmiti == 'Close' ? math.max(close, open) > stuff.top : obmiti == 'Wick' ? high > stuff.top : obmiti == 'Avg' ? high > stuff.avg : false
                	        bralerts.set(4, alert.new(true, 'Bearish', 'OB Mitigated\n'))
                	        stuff.isbb := true
                	        stuff.bbloc := time
                	        if not obshowbb
                	            block.remove(i)
            else 
                switch stuff.bull
                    true => 
                	    if obmiti == 'Close' ? math.max(close, open) > stuff.top : obmiti == 'Wick' ? high > stuff.top : obmiti == 'Avg' ? high > stuff.avg : false

                	        block.remove(i)
                    false => 

                	    if obmiti == 'Close' ? math.min(close, open) < stuff.btm : obmiti == 'Wick' ? low < stuff.btm : obmiti == 'Avg' ? low < stuff.avg : false

                	        block.remove(i)


overlap(array<ob> bull, array<ob> bear) =>
    if bull.size() > 1
        for i = bull.size() - 1 to 1 by 1
            stuff = bull.get(i)
            current = bull.get(0)
            v = wichlap == 'Recent' ? i : 0
            switch 
                stuff.btm > current.btm and stuff.btm < current.top => bull.remove(v)
                stuff.top < current.top and stuff.btm > current.btm => bull.remove(v)
                stuff.top > current.top and stuff.btm < current.btm => bull.remove(v)
                stuff.top < current.top and stuff.top > current.btm => bull.remove(v)

    if bear.size() > 1
        for i = bear.size() - 1 to 1 by 1
            stuff = bear.get(i)
            current = bear.get(0)
            v = wichlap == 'Recent' ? i : 0
            switch 
                stuff.btm > current.btm and stuff.btm < current.top => bear.remove(v)
                stuff.top < current.top and stuff.btm > current.btm => bear.remove(v)
                stuff.top > current.top and stuff.btm < current.btm => bear.remove(v)
                stuff.top < current.top and stuff.top > current.btm => bear.remove(v)

    if bull.size() > 0 and bear.size() > 0
        for i = bull.size() - 1 to 0 by 1
            stuff = bull.get(i)
            current = bear.get(0)
            v = wichlap == 'Recent' ? 0 : i
            switch 
                stuff.btm > current.btm and stuff.btm < current.top => bull.remove(v)
                stuff.top < current.top and stuff.btm > current.btm => bull.remove(v)
                stuff.top > current.top and stuff.btm < current.btm => bull.remove(v)
                stuff.top < current.top and stuff.top > current.btm => bull.remove(v)

    if bull.size() > 0 and bear.size() > 0
        for i = bear.size() - 1 to 0 by 1
            stuff = bear.get(i)
            current = bull.get(0)
            v = wichlap == 'Recent' ? 0 : i
            switch 
                stuff.btm > current.btm and stuff.btm < current.top => bear.remove(v)
                stuff.top < current.top and stuff.btm > current.btm => bear.remove(v)
                stuff.top > current.top and stuff.btm < current.btm => bear.remove(v)
                stuff.top < current.top and stuff.top > current.btm => bear.remove(v)


overlapFVG(array<FVG> blFVG, array<FVG> brFVG) =>
    if blFVG.size() > 1
        for i = blFVG.size() - 1 to 1 by 1
            stuff = blFVG.get(i)
            current = blFVG.get(0)
            switch 
                stuff.btm > current.btm and stuff.btm < current.top => blFVG.remove(i)
                stuff.top < current.top and stuff.btm > current.btm => blFVG.remove(i)
                stuff.top > current.top and stuff.btm < current.btm => blFVG.remove(i)
                stuff.top < current.top and stuff.top > current.btm => blFVG.remove(i)

    if brFVG.size() > 1
        for i = brFVG.size() - 1 to 1 by 1
            stuff = brFVG.get(i)
            current = brFVG.get(0)
            switch 
                stuff.btm > current.btm and stuff.btm < current.top => brFVG.remove(i)
                stuff.top < current.top and stuff.btm > current.btm => brFVG.remove(i)
                stuff.top > current.top and stuff.btm < current.btm => brFVG.remove(i)
                stuff.top < current.top and stuff.top > current.btm => brFVG.remove(i)

    if blFVG.size() > 0 and brFVG.size() > 0
        for i = blFVG.size() - 1 to 0 by 1
            stuff = blFVG.get(i)
            current = brFVG.get(0)
            switch 
                stuff.btm > current.btm and stuff.btm < current.top => blFVG.remove(i)
                stuff.top < current.top and stuff.btm > current.btm => blFVG.remove(i)
                stuff.top > current.top and stuff.btm < current.btm => blFVG.remove(i)
                stuff.top < current.top and stuff.top > current.btm => blFVG.remove(i)

    if blFVG.size() > 0 and brFVG.size() > 0
        for i = brFVG.size() - 1 to 0 by 1
            stuff = brFVG.get(i)
            current = blFVG.get(0)
            switch 
                stuff.btm > current.btm and stuff.btm < current.top => brFVG.remove(i)
                stuff.top < current.top and stuff.btm > current.btm => brFVG.remove(i)
                stuff.top > current.top and stuff.btm < current.btm => brFVG.remove(i)
                stuff.top < current.top and stuff.top > current.btm => brFVG.remove(i)


method umt(ob metric) =>
    switch metric.dir
        1 => 
    	    switch metric.move
    	        1 => 
    	    	    metric.blPOS := metric.blPOS + 1
    	    	    metric.move := 2
    	    	    metric.move
    	        2 => 
    	    	    metric.blPOS := metric.blPOS + 1
    	    	    metric.move := 3
    	    	    metric.move
    	        3 => 
    	    	    metric.brPOS := metric.brPOS + 1
    	    	    metric.move := 1
    	    	    metric.move
        -1 => 

    	    switch metric.move
    	        1 => 
    	    	    metric.brPOS := metric.brPOS + 1
    	    	    metric.move := 2
    	    	    metric.move
    	        2 => 
    	    	    metric.brPOS := metric.brPOS + 1
    	    	    metric.move := 3
    	    	    metric.move
    	        3 => 
    	    	    metric.blPOS := metric.blPOS + 1
    	    	    metric.move := 1
    	    	    metric.move

    if time - time[1] == time[1] - time[2]
        metric.xlocbl := metric.loc + (time - time[1]) * metric.blPOS
        metric.xlocbr := metric.loc + (time - time[1]) * metric.brPOS
        metric.xlocbr


method display(ob id, array<ob> full, int i) =>
    if not id.isbb
        bin.bx.unshift(box.new(top = id.top, bottom = id.btm, left = id.loc, right = time, border_color = na, bgcolor = id.css, xloc = xloc.bar_time))
        bin.bx.unshift(box.new(top = id.top, bottom = id.btm, left = time, right = time + 1, border_color = na, bgcolor = id.css, xloc = xloc.bar_time, extend = extend.right))

    else
        bin.bx.unshift(box.new(top = id.top, bottom = id.btm, left = id.loc, right = id.bbloc, border_color = na, bgcolor = id.css, xloc = xloc.bar_time))
        bin.bx.unshift(box.new(top = id.top, bottom = id.btm, left = id.bbloc, right = time, border_color = id.css, bgcolor = id.bull ? bbup : bbdn, xloc = xloc.bar_time, border_width = 2))
        bin.bx.unshift(box.new(top = id.top, bottom = id.btm, left = time, right = time + 1, border_color = id.css, bgcolor = id.bull ? bbup : bbdn, xloc = xloc.bar_time, extend = extend.right))

    if obshowactivity
        bin.bx.unshift(box.new(top = id.top, bottom = id.avg, left = id.loc, right = id.xlocbl, border_color = na, bgcolor = obactup, xloc = xloc.bar_time))
        bin.bx.unshift(box.new(top = id.avg, bottom = id.btm, left = id.loc, right = id.xlocbr, border_color = na, bgcolor = obactdn, xloc = xloc.bar_time))

    if showline
        bin.ln.unshift(line.new(x1 = id.loc, x2 = time, y1 = id.avg, y2 = id.avg, color = color.new(id.css, 0), xloc = xloc.bar_time, style = line.style_dashed))

    if showmetric
        if i == math.min(oblast - 1, full.size() - 1)
            float tV = 0
            array<float> dV = array.new<float>()
            seq = math.min(oblast - 1, full.size() - 1)
            for j = 0 to seq by 1
                cV = full.get(j)
                tV := tV + cV.vol
                if j == seq
                    for y = 0 to seq by 1
                        dV.push(math.floor(full.get(y).vol / tV * 100))
                        ids = full.get(y)
                        bin.lb.unshift(label.new(bar_index - 1, ids.avg, textcolor = color.new(ids.css, 0), style = label.style_label_left, size = obtxt.txSz(), color = #ffffff00, text = str.tostring(math.round(full.get(y).vol, 3), format = format.volume) + ' (' + str.tostring(dV.get(y)) + '%)'))


method dispFVG(FVG fvg, int i, bool bull) =>
    ext = fvgextend ? extend.right : extend.none
    if not fvg.isbb
        bin.bx.unshift(box.new(top = fvg.top, bottom = fvg.btm, left = fvg.loc, right = time, border_color = na, bgcolor = bull ? fvg_upcss : fvg_dncss, xloc = xloc.bar_time, extend = ext))
        if fvgline
            bin.ln.unshift(line.new(x1 = fvg.loc, x2 = time, y1 = math.avg(fvg.top, fvg.btm), y2 = math.avg(fvg.top, fvg.btm), xloc = xloc.bar_time, color = color.new(bull ? fvg_upcss : fvg_dncss, 0), extend = ext))
        if dispraid
            bin.ln.unshift(line.new(x1 = fvg.raidloc, x2 = fvg.raidx2, y1 = fvg.raidy, y2 = fvg.raidy, xloc = xloc.bar_time, color = fvg.raidcs))
            bin.lb.unshift(label.new(x = int(math.avg(fvg.raidloc, fvg.raidx2)), y = fvg.raidy, text = 'x', xloc = xloc.bar_time, textcolor = fvg.raidcs, style = bull ? label.style_label_up : label.style_label_down, size = size.small, color = #ffffff00))

    else
        bin.bx.unshift(box.new(top = fvg.top, bottom = fvg.btm, left = fvg.loc, right = fvg.bbloc, border_color = na, bgcolor = bull ? fvg_upcss : fvg_dncss, xloc = xloc.bar_time))
        bin.bx.unshift(box.new(top = fvg.top, bottom = fvg.btm, left = fvg.bbloc, right = time, border_color = bull ? fvg_dncss : fvg_upcss, bgcolor = bull ? fvg_dncss : fvg_upcss, xloc = xloc.bar_time, extend = ext))
        if fvgline
            bin.ln.unshift(line.new(x1 = fvg.loc, x2 = fvg.bbloc, y1 = math.avg(fvg.top, fvg.btm), y2 = math.avg(fvg.top, fvg.btm), color = color.new(bull ? fvg_upcss : fvg_dncss, 0), xloc = xloc.bar_time))
            bin.ln.unshift(line.new(x1 = fvg.bbloc, x2 = time, y1 = math.avg(fvg.top, fvg.btm), y2 = math.avg(fvg.top, fvg.btm), color = color.new(bull ? fvg_dncss : fvg_upcss, 0), xloc = xloc.bar_time, extend = ext, style = line.style_dashed))
            //}
_ = '   ------------         ––––––––––––––––––––––––––  FUNCTION  –––––––––––––––––––––––––––  ------------                                                                                                                                                                        ' //{                                                         

mapping() =>
    var float up = na
    var float dn = na
    var float point = na
    var int trend = 0
    var int idx = na
    var int sum = na
    var int project = na
    var array<chart.point> charts = array.new<chart.point>()

    if na(up)
        up := high
        idx := bar_index
        idx

    if na(dn)
        dn := low
        idx := bar_index
        idx

    if high > up
        if trend == -1
            id = IDMIDX(false, idx)
            charts.unshift(chart.point.from_time(time[id], low[id]))
            idx := bar_index
            point := low[id]
            sum := time[id]
            sum

        up := high
        dn := low
        project := time
        trend := 1
        trend

    if low < dn
        if trend == 1
            id = IDMIDX(true, idx)
            charts.unshift(chart.point.from_time(time[id], high[id]))
            idx := bar_index
            point := high[id]
            sum := time[id]
            sum

        up := high
        dn := low
        project := time
        trend := -1
        trend

    if barstate.islast
        var line ln = na
        var polyline pl = na
        ln.delete()
        pl.delete()
        ln := na
        pl := na
        ln := line.new(x1 = sum, x2 = project, y1 = point, y2 = trend == 1 ? up : dn, xloc = xloc.bar_time, color = color.red)
        pl := polyline.new(charts, line_color = mappingcss, xloc = xloc.bar_time, line_style = mappingStyle.lstyle())
        pl

posLIQ() =>
    ph = ta.pivothigh(high, 5, 5)
    pl = ta.pivotlow(low, 5, 5)

    var array<float> lUp = array.new<float>(1, ph)
    var array<float> lDn = array.new<float>(1, pl)

    var bool upallow = true
    var bool dnallow = true

    bool printup = false
    bool printdn = false

    if bool(ph)
        lUp.set(0, ph)

    if bool(pl)
        lDn.set(0, pl)


    if high > lUp.get(0) and close < lUp.get(0) and upallow and high > high[1]
        bralerts.set(10, alert.new(true, 'Bearish', 'Liquidity Print\n'))
        upallow := false
        printup := true
        printup


    if low < lDn.get(0) and close > lDn.get(0) and dnallow and low < low[1]
        blalerts.set(10, alert.new(true, 'Bullish', 'Liquidity Print\n'))
        dnallow := false
        printdn := true
        printdn


    if bool(ph) and upallow == false
        upallow := true
        upallow

    if bool(pl) and dnallow == false
        dnallow := true
        dnallow



    [printup, printdn]



dEHL() =>
    var array<equalbuild> bleqal = array.new<equalbuild>()
    var array<equalbuild> breqal = array.new<equalbuild>()
    var array<equalname> blname = array.new<equalname>()
    var array<equalname> brname = array.new<equalname>()

    int pos = 2
    float thresh = 0.1
    switch ehlmode
        'Short-Term' => 
    	    pos := 2
    	    thresh := 0.1
    	    thresh
        'Mid-Term' => 
    	    pos := 6
    	    thresh := 0.25
    	    thresh
        'Long-Term' => 
    	    pos := 10
    	    thresh := 0.5
    	    thresh

    var ehl w = ehl.new(0, 0, 0, 0)
    float top = ta.pivothigh(high, pos, pos)
    float btm = ta.pivotlow(low, pos, pos)
    float atr = ta.atr(200)
    switch  
        bool(top) => 
    	    mx = math.max(top, w.pt)
    	    mn = math.min(top, w.pt)
    	    switch 
    	        mx < mn + atr * thresh => 
    	    	    bleqal.unshift(equalbuild.new(w.t, w.pt, bar_index - pos, top, ehlupcs, line.style_dotted))
    	    	    blname.unshift(equalname.new(int(math.avg(bar_index - pos, w.t)), top, 'EQH', ehlupcs, label.style_label_down))
    	    w.pt := top
    	    w.t := bar_index - pos
    	    w.t
        bool(btm) => 



    	    mx = math.max(btm, w.pb)
    	    mn = math.min(btm, w.pb)
    	    switch 
    	        mn > mx - atr * thresh => 
    	    	    breqal.unshift(equalbuild.new(w.b, w.pb, bar_index - pos, btm, ehldowncs, line.style_dotted))
    	    	    brname.unshift(equalname.new(int(math.avg(bar_index - pos, w.b)), btm, 'EQL', ehldowncs, label.style_label_up))
    	    w.pb := btm
    	    w.b := bar_index - pos
    	    w.b



    if barstate.islast
        if bleqal.size() > 0
            for i = 0 to bleqal.size() - 1 by 1
                draw = bleqal.get(i)
                labe = blname.get(i)
                if i < 10
                    bin.ln.unshift(line.new(draw.x1, draw.y1, draw.x2, draw.y2, color = draw.css, style = draw.style))
                    bin.lb.unshift(label.new(x = labe.x, y = labe.y, color = #ffffff00, style = labe.style, textcolor = labe.css, size = mstext.txSz(), text = labe.txt))

        if breqal.size() > 0
            for i = 0 to breqal.size() - 1 by 1
                draw = breqal.get(i)
                labe = brname.get(i)
                if i < 10
                    bin.ln.unshift(line.new(draw.x1, draw.y1, draw.x2, draw.y2, color = draw.css, style = draw.style))
                    bin.lb.unshift(label.new(x = labe.x, y = labe.y, color = #ffffff00, style = labe.style, textcolor = labe.css, size = mstext.txSz(), text = labe.txt))


method SFP(structure ms) =>

    [h, h1, h2, l, l1, l2, c, v, t, n, t1] = SFPcords(sfptf)

    if not sfpuse
        h := high
        h1 := high[1]
        h2 := high[2]
        l := low
        l1 := low[1]
        l2 := low[2]
        c := close
        v := volume
        t := time
        n := bar_index
        t1 := time[1]
        t1

    string size = devsize.txSz()
    bool vol = ta.sma(v, 21) * sfpthresh < v ? true : false
    bool blPrint = true
    bool brPrint = true
    switch sfpfilter 
        'Trend Following' => 
    	    switch ms.trend
    	        1 => 
    	    	    blPrint := true
    	    	    brPrint := false
    	    	    brPrint
    	        -1 => 
    	    	    blPrint := false
    	    	    brPrint := true
    	    	    brPrint
        'Counter Trade' => 

    	    switch ms.trend
    	        1 => 
    	    	    blPrint := false
    	    	    brPrint := true
    	    	    brPrint
    	        -1 => 
    	    	    blPrint := true
    	    	    brPrint := false
    	    	    brPrint

        => na

    var array<SFP> BLsfp = array.new<SFP>()
    var array<SFP> BRsfp = array.new<SFP>()
    array<float> lg = array.new<float>()
    array<int> tn = array.new<int>()
    array<float> ac = array.new<float>()
    var array<sfpbuildlbl> BLlb = array.new<sfpbuildlbl>()
    var array<sfpbuildlbl> BRlb = array.new<sfpbuildlbl>()
    var array<sfpbuildline> BLln = array.new<sfpbuildline>()
    var array<sfpbuildline> BRln = array.new<sfpbuildline>()

    if l > l1 and l1 < l2 and vol[1]
        BLsfp.clear()
        if spfuselen
            for i = 1 to sfplen by 1
                lg.unshift(l[i])
                tn.unshift(t[i])
                ac.unshift(h[i])

            BLsfp.unshift(SFP.new(lg.min(), tn.get(lg.indexof(lg.min())), ac.get(lg.indexof(lg.min()))))

            if lg.min() != l1
                BLsfp.unshift(SFP.new(l1, t1, h1))

        else
            BLsfp.unshift(SFP.new(l1, t1, h1))

    if h < h1 and h1 > h2 and vol[1]
        BRsfp.clear()
        if spfuselen
            for i = 1 to sfplen by 1
                lg.unshift(h[i])
                tn.unshift(t[i])
                ac.unshift(l[i])

            BRsfp.unshift(SFP.new(lg.max(), tn.get(lg.indexof(lg.max())), ac.get(lg.indexof(lg.max()))))

            if lg.max() != h1
                BRsfp.unshift(SFP.new(h1, t1, l1))

        else
            BRsfp.unshift(SFP.new(h1, t1, l1))

    if BLsfp.size() > 0 and blPrint
        for i = BLsfp.size() - 1 to 0 by 1
            sfp = BLsfp.get(i)
            switch 
                c < sfp.y => BLsfp.remove(i)

                l < sfp.y and c > sfp.y => 
            	    if vol
            	        blalerts.set(9, alert.new(true, 'Bullish', 'SFP Created\n'))
            	        BLlb.unshift(sfpbuildlbl.new(time, low < sfp.y ? low : sfp.y, label.style_label_up, blsfpcs, '▲\nSFP'))
            	        BLln.unshift(sfpbuildline.new(sfp.loc, time, sfp.y, blsfpcs, sfp.ancor, bar_index))

            	        BLsfp.remove(i)



            	    else
            	        BLsfp.remove(i)

    if BRsfp.size() > 0 and brPrint
        for i = BRsfp.size() - 1 to 0 by 1
            sfp = BRsfp.get(i)
            switch 
                c > sfp.y => BRsfp.remove(i)

                h > sfp.y and c < sfp.y => 
            	    if vol
            	        bralerts.set(9, alert.new(true, 'Bearish', 'SFP Created\n'))
            	        BRlb.unshift(sfpbuildlbl.new(time, high > sfp.y ? high : sfp.y, label.style_label_down, brsfpcs, 'SFP\n▼'))
            	        BRln.unshift(sfpbuildline.new(sfp.loc, time, sfp.y, brsfpcs, sfp.ancor, bar_index))

            	        BRsfp.remove(i)



            	    else
            	        BRsfp.remove(i)

    if barstate.islast
        if sfpshow > 0 and BLlb.size() > 0
            for i = 0 to math.min(sfpshow - 1, BLlb.size() - 1) by 1
                x = BLlb.get(i)
                bin.lb.unshift(label.new(x = x.x, y = x.y, text = x.txt, color = #ffffff00, style = x.style, size = size, textcolor = x.css, xloc = xloc.bar_time))

        if sfpshow > 0 and BRlb.size() > 0
            for i = 0 to math.min(sfpshow - 1, BRlb.size() - 1) by 1
                x = BRlb.get(i)
                bin.lb.unshift(label.new(x = x.x, y = x.y, text = x.txt, color = #ffffff00, style = x.style, size = size, textcolor = x.css, xloc = xloc.bar_time))

        if sfpshow > 0 and BLln.size() > 0
            for i = 0 to math.min(sfpshow - 1, BLln.size() - 1) by 1
                x = BLln.get(i)
                bin.ln.unshift(line.new(x1 = x.x1, x2 = x.x2, y1 = x.y, y2 = x.y, color = x.css, style = line.style_solid, xloc = xloc.bar_time))

        if sfpshow > 0 and BRln.size() > 0
            for i = 0 to math.min(sfpshow - 1, BRln.size() - 1) by 1
                x = BRln.get(i)
                bin.ln.unshift(line.new(x1 = x.x1, x2 = x.x2, y1 = x.y, y2 = x.y, color = x.css, style = line.style_solid, xloc = xloc.bar_time))

        if sfpshow > 0 and BLln.size() > 0 and BRln.size() > 0 and showdev
            max = math.max(BLln.get(0).loc, BRln.get(0).loc)
            use = math.max(BLln.get(0).loc, BRln.get(0).loc) == BLln.get(0).loc ? BLln.get(0) : BRln.get(0)
            bull = math.max(BLln.get(0).loc, BRln.get(0).loc) == BLln.get(0).loc ? true : false
            switch bull
                false => 
            	    bin.ln.unshift(line.new(x1 = use.x1, x2 = time, y1 = use.ancor - 4.0 * (use.y - use.ancor), y2 = use.ancor - 4.0 * (use.y - use.ancor), xloc = xloc.bar_time, color = chart.fg_color, style = devstyle.lstyle()))
            	    bin.ln.unshift(line.new(x1 = use.x1, x2 = time, y1 = use.ancor - 2.0 * (use.y - use.ancor), y2 = use.ancor - 2.0 * (use.y - use.ancor), xloc = xloc.bar_time, color = devcss, style = devstyle.lstyle()))
            	    bin.ln.unshift(line.new(x1 = use.x1, x2 = time, y1 = use.ancor - 2.5 * (use.y - use.ancor), y2 = use.ancor - 2.5 * (use.y - use.ancor), xloc = xloc.bar_time, color = devcss, style = devstyle.lstyle()))
            	    if devfill
            	        bin.lf.unshift(linefill.new(bin.ln.get(0), bin.ln.get(1), color = color.new(devcss, 90)))

            	    if devlvl
            	        bin.lb.unshift(label.new(x = use.x1, y = use.ancor + 4.0 * (use.ancor - use.y), text = str.tostring(4.0 * 100) + '%' + ' (' + str.tostring(math.round(use.ancor + 4.0 * (use.ancor - use.y), 2)) + ')', style = label.style_label_right, textcolor = chart.fg_color, color = #ffffff00, size = devsize.txSz(), xloc = xloc.bar_time))
            	        bin.lb.unshift(label.new(x = use.x1, y = use.ancor + 2.0 * (use.ancor - use.y), text = str.tostring(2.0 * 100) + '%' + ' (' + str.tostring(math.round(use.ancor + 2.0 * (use.ancor - use.y), 2)) + ')', style = label.style_label_right, textcolor = devcss, color = #ffffff00, size = devsize.txSz(), xloc = xloc.bar_time))
            	        bin.lb.unshift(label.new(x = use.x1, y = use.ancor + 2.5 * (use.ancor - use.y), text = str.tostring(2.5 * 100) + '%' + ' (' + str.tostring(math.round(use.ancor + 2.5 * (use.ancor - use.y), 2)) + ')', style = label.style_label_right, textcolor = devcss, color = #ffffff00, size = devsize.txSz(), xloc = xloc.bar_time))
                true => 

            	    bin.ln.unshift(line.new(x1 = use.x1, x2 = time, y1 = use.ancor + 4.0 * (use.ancor - use.y), y2 = use.ancor + 4.0 * (use.ancor - use.y), xloc = xloc.bar_time, color = chart.fg_color, style = devstyle.lstyle()))
            	    bin.ln.unshift(line.new(x1 = use.x1, x2 = time, y1 = use.ancor + 2.0 * (use.ancor - use.y), y2 = use.ancor + 2.0 * (use.ancor - use.y), xloc = xloc.bar_time, color = devcss, style = devstyle.lstyle()))
            	    bin.ln.unshift(line.new(x1 = use.x1, x2 = time, y1 = use.ancor + 2.5 * (use.ancor - use.y), y2 = use.ancor + 2.5 * (use.ancor - use.y), xloc = xloc.bar_time, color = devcss, style = devstyle.lstyle()))
            	    if devfill
            	        bin.lf.unshift(linefill.new(bin.ln.get(0), bin.ln.get(1), color = color.new(devcss, 90)))

            	    if devlvl
            	        bin.lb.unshift(label.new(x = use.x1, y = use.ancor + 4.0 * (use.ancor - use.y), text = str.tostring(4.0 * 100) + '%' + ' (' + str.tostring(math.round(use.ancor + 4.0 * (use.ancor - use.y), 2)) + ')', style = label.style_label_right, textcolor = chart.fg_color, color = #ffffff00, size = devsize.txSz(), xloc = xloc.bar_time))
            	        bin.lb.unshift(label.new(x = use.x1, y = use.ancor + 2.0 * (use.ancor - use.y), text = str.tostring(2.0 * 100) + '%' + ' (' + str.tostring(math.round(use.ancor + 2.0 * (use.ancor - use.y), 2)) + ')', style = label.style_label_right, textcolor = devcss, color = #ffffff00, size = devsize.txSz(), xloc = xloc.bar_time))
            	        bin.lb.unshift(label.new(x = use.x1, y = use.ancor + 2.5 * (use.ancor - use.y), text = str.tostring(2.5 * 100) + '%' + ' (' + str.tostring(math.round(use.ancor + 2.5 * (use.ancor - use.y), 2)) + ')', style = label.style_label_right, textcolor = devcss, color = #ffffff00, size = devsize.txSz(), xloc = xloc.bar_time))


dFVG() =>
    [h2, l2, c1, o1, c, o, h, l, h1, l1, fvatr] = ghl()
    var array<FVG> blFVG = array.new<FVG>()
    var array<FVG> brFVG = array.new<FVG>()
    bool upfvg = false
    bool dnfvg = false
    float blth = l1 + fvatr[1] * fvgthresh
    float brth = h1 - fvatr[1] * fvgthresh

    cc = timeframe.change(fvg_tf)

    switch 
        what_fvg == 'FVG' or what_fvg == 'Breakers' => 
    	    if l > h2 and cc and c1 > blth
    	        upfvg := true
    	        upfvg

    	    if l2 > h and cc and c1 < brth
    	        dnfvg := true
    	        dnfvg


    if upfvg[1]
        if blFVG.size() > 0
            fvg = blFVG.get(0)
            if fvg.israid == true and fvg.active == false
                fvg.active := true
                fvg.raidloc := na
                fvg.raidx2 := na
                fvg.raidy := na
                fvg.raidcs := #ffffff00
                fvg.raidcs

        blFVG.unshift(FVG.new(l[1], h2[1], time[3], false, na))



    if dnfvg[1]
        if brFVG.size() > 0
            fvg = brFVG.get(0)
            if fvg.israid == true and fvg.active == false
                fvg = brFVG.get(0)
                fvg.active := true
                fvg.active := true
                fvg.raidloc := na
                fvg.raidx2 := na
                fvg.raidy := na
                fvg.raidcs := #ffffff00
                fvg.raidcs

        brFVG.unshift(FVG.new(l2[1], h[1], time[3], false, na))



    if blFVG.size() > 0
        for [i, fvg] in blFVG
            if not fvg.isbb
                if fvg_src == 'Close' ? math.min(c, o) < fvg.btm : fvg_src == 'Wick' ? l < fvg.btm : fvg_src == 'Avg' ? l < math.avg(fvg.top, fvg.btm) : false
                    blalerts.set(6, alert.new(true, 'Bullish', 'FVG Mitigated\n'))
                    fvg.isbb := true
                    fvg.bbloc := time

                    if what_fvg == 'FVG'
                        blFVG.remove(i)

            else
                if (fvg_src == 'Close' ? math.max(c, o) > fvg.top : fvg_src == 'Wick' ? h > fvg.top : fvg_src == 'Avg' ? h > math.avg(fvg.top, fvg.btm) : false) and what_fvg == 'Breakers'
                    blFVG.remove(i)


    if brFVG.size() > 0
        for [i, fvg] in brFVG
            if not fvg.isbb
                if fvg_src == 'Close' ? math.max(c, o) > fvg.top : fvg_src == 'Wick' ? h > fvg.top : fvg_src == 'Avg' ? h > math.avg(fvg.top, fvg.btm) : false
                    bralerts.set(6, alert.new(true, 'Bearish', 'FVG Mitigated\n'))
                    fvg.isbb := true
                    fvg.bbloc := time

                    if what_fvg == 'FVG'
                        brFVG.remove(i)
            else
                if (fvg_src == 'Close' ? math.min(c, o) < fvg.btm : fvg_src == 'Wick' ? l < fvg.btm : fvg_src == 'Avg' ? l < math.avg(fvg.top, fvg.btm) : false) and what_fvg == 'Breakers'
                    brFVG.remove(i)


    if fvgoverlap
        overlapFVG(blFVG, brFVG)

    if blFVG.size() > 0
        fvg = blFVG.get(0)
        if not fvg.isbb
            if low < fvg.top
                blalerts.set(8, alert.new(true, 'Bullish', 'Price Inside FVG\n'))


    if brFVG.size() > 0
        fvg = brFVG.get(0)
        if not fvg.isbb
            if high > fvg.btm
                bralerts.set(8, alert.new(true, 'Bearish', 'Price Inside FVG\n'))
    if dispraid
        for [i, fvg] in blFVG
            if not fvg.israid and not fvg.isbb
                if low < fvg.top and close > fvg.top
                    fvg.israid := true
                    fvg.raidloc := time
                    fvg.raidx2 := time
                    fvg.raidy := low
                    fvg.raidcs := chart.fg_color
                    fvg.raidcs
            else
                if low <= fvg.raidy and fvg.active == false and not fvg.isbb
                    blalerts.set(7, alert.new(true, 'Bullish', 'Raid Found\n'))
                    fvg.active := true
                    fvg.raidx2 := time
                    fvg.raidx2
                else
                    if fvg.active == false and not fvg.isbb
                        fvg.raidx2 := time
                        fvg.raidx2

        for [i, fvg] in brFVG
            if not fvg.israid and not fvg.isbb
                if high > fvg.btm and close < fvg.btm and not fvg.isbb
                    fvg.israid := true
                    fvg.raidloc := time
                    fvg.raidy := high
                    fvg.raidx2 := time
                    fvg.raidcs := chart.fg_color
                    fvg.raidcs
            else
                if high >= fvg.raidy and fvg.active == false and not fvg.isbb
                    bralerts.set(7, alert.new(true, 'Bearish', 'Raid Found\n'))
                    fvg.active := true
                    fvg.raidx2 := time
                    fvg.raidx2
                else
                    if fvg.active == false and not fvg.isbb
                        fvg.raidx2 := time
                        fvg.raidx2


    if barstate.islast
        if blFVG.size() > 0 and fvg_num > 0
            for i = 0 to math.min(fvg_num - 1, blFVG.size() - 1) by 1
                fvg = blFVG.get(i)
                dispFVG(fvg, i, true)

        if brFVG.size() > 0 and fvg_num > 0
            for i = 0 to math.min(fvg_num - 1, brFVG.size() - 1) by 1
                fvg = brFVG.get(i)
                dispFVG(fvg, i, false)


structure(color upcss, color dncss, bool draw, bool internal, int limit) =>
    var structure ms = structure.new(start = 0)
    var array<ob> blob = array.new<ob>()
    var array<ob> brob = array.new<ob>()
    var array<drawms> bldw = array.new<drawms>()
    var array<drawms> brdw = array.new<drawms>()
    var array<sellbuyside> sellside = array.new<sellbuyside>()
    var array<sellbuyside> buyside = array.new<sellbuyside>()
    bool crossup = false
    bool crossdn = false
    var float up = na
    var float dn = na
    idbull = ms.find(false, false, true)
    idbear = ms.find(true, false, true)
    btmP = obmode == 'Length' ? high[idbear] - 1 * atr[idbear] < low[idbear] ? low[idbear] : high[idbear] - 1 * atr[idbear] : low[idbear]
    topP = obmode == 'Length' ? low[idbull] + 1 * atr[idbull] > high[idbull] ? high[idbull] : low[idbull] + 1 * atr[idbull] : high[idbull]
    atr = ta.atr(200)
    buy = low + atr
    sel = high - atr
    ph = ta.pivothigh(high, mslen, mslen)
    pl = ta.pivotlow(low, mslen, mslen)
    var array<int> phn = array.new<int>(1, na)
    var array<int> pln = array.new<int>(1, na)
    var array<float> php = array.new<float>(1, na)
    var array<float> plp = array.new<float>(1, na)

    if internal
        blob.clear()
        brob.clear()

    if bool(ph)
        phn.unshift(bar_index[mslen])
        php.unshift(high[mslen])

    if bool(pl)
        pln.unshift(bar_index[mslen])
        plp.unshift(low[mslen])

    if php.size() > 0
        if high > php.get(0)
            php.clear()
            phn.clear()

    if plp.size() > 0
        if low < plp.get(0)
            plp.clear()
            pln.clear()

    if na(up)
        up := high
        up

    if na(dn)
        dn := low
        dn

    if high > up
        up := high
        dn := low
        crossup := true
        crossup

    if low < dn
        up := high
        dn := low
        crossdn := true
        crossdn

    if ms.start == 0
        ms := structure.new(bar_index, na, high, low, bar_index, bar_index, 0, 1, na, bar_index)
        if draw
            bldw.unshift(drawms.new(time, time, high, 'CHoCH', upcss, line.style_dashed))
            brdw.unshift(drawms.new(time, time, low, 'CHoCH', dncss, line.style_dashed))

    ms.upsweep := false
    ms.dnsweep := false

    if ms.start == 1
        switch 
            low <= ms.choch and close >= ms.choch and buildsweep => 
        	    bralerts.set(2, alert.new(true, 'Bearish', 'CHoCH Sweep\n'))
        	    ms.dnsweep := true
        	    ms.choch := low
        	    ms.xloc := bar_index
        	    if draw
        	        dw = brdw.get(0)
        	        dw.x2 := time
        	        dw.style := line.style_dotted
        	        dw.txt := 'x'
        	        brdw.unshift(drawms.new(time, time, low, 'CHoCH', dncss, line.style_dashed))



            high >= ms.bos and close <= ms.bos and buildsweep => 
        	    blalerts.set(2, alert.new(true, 'Bullish', 'CHoCH Sweep\n'))
        	    ms.upsweep := true
        	    ms.bos := high
        	    ms.xloc := bar_index
        	    if draw
        	        dw = bldw.get(0)
        	        dw.x2 := time
        	        dw.style := line.style_dotted
        	        dw.txt := 'x'
        	        bldw.unshift(drawms.new(time, time, high, 'CHoCH', upcss, line.style_dashed))



            close <= ms.choch => 
        	    bralerts.set(0, alert.new(true, 'Bearish', 'CHoCH\n'))
        	    ms.txt := 'choch'
        	    lc.start := true
        	    lc.count := 0
        	    blob.fnOB(true, topP, idbull)
        	    ms.trend := -1
        	    ms.choch := ms.bos
        	    ms.bos := na
        	    ms.start := 2
        	    ms.loc := bar_index
        	    ms.main := low
        	    ms.temp := ms.loc
        	    ms.xloc := bar_index
        	    if draw
        	        dw = brdw.get(0)
        	        dw.x2 := time
        	        dw.style := internal ? line.style_dashed : line.style_solid
        	        dw.style



            close >= ms.bos => 
        	    blalerts.set(0, alert.new(true, 'Bullish', 'CHoCH\n'))
        	    ms.txt := 'choch'
        	    lc.start := true
        	    lc.count := 0
        	    brob.fnOB(false, btmP, idbear)
        	    ms.trend := 1
        	    ms.choch := ms.choch
        	    ms.bos := na
        	    ms.start := 2
        	    ms.loc := bar_index
        	    ms.main := high
        	    ms.temp := ms.loc
        	    ms.xloc := bar_index
        	    if draw
        	        dw = bldw.get(0)
        	        dw.x2 := time
        	        dw.style := internal ? line.style_dashed : line.style_solid
        	        dw.style



    if ms.start == 2
        switch ms.trend
            -1 => 
        	    if low <= ms.main
        	        ms.main := low
        	        ms.temp := bar_index
        	        ms.temp

        	    if bar_index % mslen * 2 == 0
        	        if not na(ms.bos) and msmode == 'Adjusted Points' and php.size() > 0
        	            if php.get(0) < ms.choch
        	                // ms.xloc  := phn.get(0)
        	                ms.choch := php.get(0)
        	                ms.loc := phn.get(0)
        	                ms.xloc := phn.get(0)
        	                ms.temp := phn.get(0)
        	                if draw
        	                    choch = bldw.get(0)
        	                    choch.x1 := time[bar_index - phn.get(0)]
        	                    choch.x2 := time
        	                    choch.y := php.get(0)
        	                    choch.y

        	    if na(ms.bos)
        	        if crossup and close > open and close[1] > open[1]
        	            ms.bos := ms.main
        	            ms.loc := ms.temp
        	            ms.xloc := ms.loc
        	            if draw
        	                brdw.unshift(drawms.new(time[bar_index - ms.loc], time, low[bar_index - ms.loc], 'BOS', dncss, line.style_dashed))

        	    if not na(ms.bos) and draw
        	        dw = brdw.get(0)
        	        dw.x2 := time
        	        dw.x2

        	    if draw
        	        choch = bldw.get(0)
        	        choch.x2 := time
        	        choch.x2

        	    switch 
        	        low <= ms.bos and close >= ms.bos and not na(ms.bos) and buildsweep => 
        	    	    bralerts.set(3, alert.new(true, 'Bearish', 'BOS Sweep\n'))
        	    	    ms.dnsweep := true
        	    	    ms.bos := low
        	    	    if draw
        	    	        dw = brdw.get(0)
        	    	        dw.x2 := time
        	    	        dw.style := line.style_dotted
        	    	        dw.txt := 'x'
        	    	        brdw.unshift(drawms.new(time, time, low, 'BOS', dncss, line.style_dashed))



        	    	    if showside
        	    	        id = ms.find(true, true, true)
        	    	        btm = high[id] - atr[id] > low[id] ? high[id] - atr[id] : low[id]
        	    	        sellside.unshift(sellbuyside.new(high[id], btm, time[id], sidesidecss, 'Sell Side', volume[id]))

        	    	        brarea.normal := false
        	    	        brarea.normal

        	    	    ms.xloc := bar_index
        	    	    ms.xloc

        	        close <= ms.bos and not na(ms.bos) => 
        	    	    bralerts.set(1, alert.new(true, 'Bearish', 'BOS\n'))
        	    	    ms.txt := 'bos'
        	    	    ms.zz := ms.bos
        	    	    ms.zn := bar_index
        	    	    lc.start := true
        	    	    lc.count := 0
        	    	    brob.fnOB(false, btmP, idbear)
        	    	    id = ms.find(true, false, false)
        	    	    ms.xloc := bar_index
        	    	    ms.bos := na
        	    	    ms.choch := high[id]
        	    	    ms.loc := bar_index[id]
        	    	    if draw
        	    	        dw = brdw.get(0)
        	    	        dw.x2 := time
        	    	        dw.style := internal ? line.style_dashed : line.style_solid
        	    	        choch = bldw.get(0)
        	    	        choch.x1 := time[id]
        	    	        choch.x2 := time
        	    	        choch.y := high[id]
        	    	        choch.y

        	    	    bralert

        	    switch 
        	        high >= ms.choch and close <= ms.choch and buildsweep => 
        	    	    blalerts.set(2, alert.new(true, 'Bullish', 'CHoCH Sweep\n'))
        	    	    ms.upsweep := true
        	    	    ms.choch := high
        	    	    ms.xloc := bar_index
        	    	    if draw
        	    	        dw = bldw.get(0)
        	    	        dw.x2 := time
        	    	        dw.style := line.style_dotted
        	    	        dw.txt := 'x'
        	    	        bldw.unshift(drawms.new(time, time, high, 'CHoCH', upcss, line.style_dashed))



        	        close >= ms.choch => 
        	    	    blalerts.set(0, alert.new(true, 'Bullish', 'CHoCH\n'))
        	    	    ms.txt := 'choch'
        	    	    ms.zz := ms.choch
        	    	    ms.zn := bar_index
        	    	    lc.start := true
        	    	    lc.count := 0
        	    	    blob.fnOB(true, topP, idbull)
        	    	    id = ms.find(false, false, false)
        	    	    switch 
        	    	        na(ms.bos) => 
        	    	    	    ms.choch := low[id]
        	    	    	    if draw
        	    	    	        brdw.unshift(drawms.new(time, time, low, 'BOS', dncss, line.style_dashed))
        	    	    	        choch = brdw.get(0)
        	    	    	        choch.x1 := time[bar_index - ms.temp]
        	    	    	        choch.x1
        	    	        => 
        	    	    	    ms.choch := ms.bos //low[id + 1] < low[id] ? low[id + 1] : low[id]
        	    	    	    ms.choch
        	    	    ms.bos := na
        	    	    ms.main := high
        	    	    ms.trend := 1
        	    	    ms.loc := bar_index
        	    	    ms.xloc := bar_index
        	    	    ms.temp := ms.loc
        	    	    if draw
        	    	        dw = bldw.get(0)
        	    	        dw.x2 := time
        	    	        dw.txt := 'CHoCH'
        	    	        dw.style := internal ? line.style_dashed : line.style_solid
        	    	        choch = brdw.get(0)
        	    	        choch.x2 := time
        	    	        choch.y := ms.choch
        	    	        choch.txt := 'CHoCH'
        	    	        choch.txt

        	    	    if showside
        	    	        id = ms.find(false, true, true)
        	    	        top = low[id] + atr[id] < high[id] ? low[id] + atr[id] : high[id]
        	    	        buyside.unshift(sellbuyside.new(low[id], top, time[id], buysidecss, 'Buy Side', volume[id]))


        	    	    ms.xloc := bar_index

        	    	    blarea.normal := false
        	    	    blarea.normal
            1 => 

        	    if high >= ms.main
        	        ms.main := high
        	        ms.temp := bar_index
        	        ms.temp

        	    if na(ms.bos)
        	        if crossdn and close < open and close[1] < open[1]
        	            ms.bos := ms.main
        	            ms.loc := ms.temp
        	            ms.xloc := ms.loc
        	            if draw
        	                bldw.unshift(drawms.new(time[bar_index - ms.loc], time, high[bar_index - ms.loc], 'BOS', upcss, line.style_dashed))

        	    if bar_index % mslen * 2 == 0
        	        if not na(ms.bos) and msmode == 'Adjusted Points' and plp.size() > 0
        	            if plp.get(0) > ms.choch
        	                // ms.xloc  := pln.get(0)
        	                ms.choch := plp.get(0)
        	                ms.loc := pln.get(0)
        	                ms.xloc := pln.get(0)
        	                ms.temp := pln.get(0)
        	                // ms.loc   := pln.get(0)
        	                if draw
        	                    choch = brdw.get(0)
        	                    choch.x1 := time[bar_index - pln.get(0)]
        	                    choch.x2 := time
        	                    choch.y := plp.get(0)
        	                    choch.y

        	    if not na(ms.bos) and draw
        	        dw = bldw.get(0)
        	        dw.x2 := time
        	        dw.x2

        	    if draw
        	        choch = brdw.get(0)
        	        choch.x2 := time
        	        choch.x2

        	    switch 
        	        high >= ms.bos and close <= ms.bos and not na(ms.bos) and buildsweep => 
        	    	    blalerts.set(3, alert.new(true, 'Bullish', 'BOS Sweep\n'))
        	    	    ms.upsweep := true
        	    	    ms.bos := high
        	    	    if draw
        	    	        dw = bldw.get(0)
        	    	        dw.x2 := time
        	    	        dw.style := line.style_dotted
        	    	        dw.txt := 'x'
        	    	        bldw.unshift(drawms.new(time, time, high, 'BOS', upcss, line.style_dashed))

        	    	    if showside
        	    	        id = ms.find(false, true, true)
        	    	        top = low[id] + atr[id] < high[id] ? low[id] + atr[id] : high[id]
        	    	        buyside.unshift(sellbuyside.new(low[id], top, time[id], buysidecss, 'Buy Side', volume[id]))

        	    	        blarea.normal := false
        	    	        blarea.normal

        	    	    ms.xloc := bar_index
        	    	    ms.xloc


        	        close >= ms.bos and not na(ms.bos) => 
        	    	    blalerts.set(1, alert.new(true, 'Bullish', 'BOS\n'))
        	    	    ms.txt := 'bos'
        	    	    ms.zz := ms.bos
        	    	    ms.zn := bar_index
        	    	    lc.start := true
        	    	    lc.count := 0
        	    	    blob.fnOB(true, topP, idbull)
        	    	    id = ms.find(false, false, false)
        	    	    ms.xloc := bar_index
        	    	    ms.bos := na
        	    	    ms.choch := low[id]
        	    	    ms.loc := bar_index[id]
        	    	    if draw
        	    	        dw = bldw.get(0)
        	    	        dw.x2 := time
        	    	        dw.style := internal ? line.style_dashed : line.style_solid
        	    	        choch = brdw.get(0)
        	    	        choch.x1 := time[id]
        	    	        choch.x2 := time
        	    	        choch.y := low[id]
        	    	        choch.y

        	    	    blalert

        	    switch 
        	        low <= ms.choch and close >= ms.choch and buildsweep => 
        	    	    bralerts.set(2, alert.new(true, 'Bearish', 'CHoCH Sweep\n'))
        	    	    ms.dnsweep := true
        	    	    ms.choch := low
        	    	    ms.xloc := bar_index
        	    	    if draw
        	    	        dw = brdw.get(0)
        	    	        dw.x2 := time
        	    	        dw.style := line.style_dotted
        	    	        dw.txt := 'x'
        	    	        brdw.unshift(drawms.new(time, time, low, 'CHoCH', dncss, line.style_dashed))



        	        close <= ms.choch => 
        	    	    bralerts.set(0, alert.new(true, 'Bearish', 'CHoCH\n'))
        	    	    ms.txt := 'choch'
        	    	    ms.zz := ms.choch
        	    	    ms.zn := bar_index
        	    	    lc.start := true
        	    	    lc.count := 0
        	    	    brob.fnOB(false, btmP, idbear)
        	    	    id = ms.find(true, false, false)
        	    	    switch 
        	    	        na(ms.bos) => 
        	    	    	    ms.choch := high[id]
        	    	    	    if draw
        	    	    	        bldw.unshift(drawms.new(time, time, high, 'BOS', upcss, line.style_dashed))
        	    	    	        choch = bldw.get(0)
        	    	    	        choch.x1 := time[bar_index - ms.temp]
        	    	    	        choch.x1
        	    	        => 
        	    	    	    ms.choch := ms.bos //high[id + 1] > high[id] ? high[id + 1] : high[id]
        	    	    	    ms.choch
        	    	    ms.bos := na
        	    	    ms.main := low
        	    	    ms.trend := -1
        	    	    ms.loc := bar_index
        	    	    ms.temp := ms.loc
        	    	    if draw
        	    	        dw = brdw.get(0)
        	    	        dw.x2 := time
        	    	        dw.txt := 'CHoCH'
        	    	        dw.style := internal ? line.style_dashed : line.style_solid
        	    	        choch = bldw.get(0)
        	    	        choch.y := ms.choch
        	    	        choch.x2 := time
        	    	        choch.txt := 'CHoCH'
        	    	        choch.txt

        	    	    if showside
        	    	        id = ms.find(true, true, true)
        	    	        btm = high[id] - atr[id] > low[id] ? high[id] - atr[id] : low[id]
        	    	        sellside.unshift(sellbuyside.new(high[id], btm, time[id], sidesidecss, 'Sell Side', volume[id]))

        	    	        brarea.normal := false
        	    	        brarea.normal

        	    	    ms.xloc := bar_index
        	    	    ms.xloc

    if blob.size() > 0
        ob = blob.get(0)
        if low < ob.top
            blalerts.set(5, alert.new(true, 'Bullish', 'Price Inside OB\n'))

    if brob.size() > 0
        ob = brob.get(0)
        if high > ob.btm
            bralerts.set(5, alert.new(true, 'Bearish', 'Price Inside OB\n'))


    if showside
        if buyside.size() > 0
            side = buyside.get(0)
            if low < side.btm

                if blarea.normal == false

                    blarea.normal := true
                    blarea.normal

            if close < side.top
                buyside.remove(0)


        if sellside.size() > 0
            side = sellside.get(0)
            if high > side.btm

                if brarea.normal == false

                    brarea.normal := true
                    brarea.normal

            if close > side.top
                sellside.remove(0)


    if barstate.islast and showside
        if buyside.size() > 0
            side = buyside.get(0)
            float sum = side.vol
            if buyside.size() > 0 and sellside.size() > 0
                sum := buyside.get(0).vol + sellside.get(0).vol
                sum

            if sidemode == 'Area'
                bin.bx.unshift(box.new(top = side.top, bottom = side.btm, left = side.loc, right = time, border_color = na, bgcolor = side.css, xloc = xloc.bar_time))


            bin.ln.unshift(line.new(x1 = side.loc, x2 = time, y1 = side.top, y2 = side.top, xloc = xloc.bar_time, color = color.new(side.css, 0), style = line.style_solid))
            bin.ln.unshift(line.new(x1 = bar_index, x2 = bar_index + 10, y1 = side.top, y2 = side.top, xloc = xloc.bar_index, color = color.new(side.css, 0), style = line.style_dotted))


            bin.lb.unshift(label.new(x = bar_index, y = side.top, color = #ffffff00, style = label.style_label_up, textcolor = color.new(side.css, 0), size = mstext.txSz(), text = 'Buyside - ' + str.tostring(math.floor(side.vol / sum * 100)) + '%'))

        if sellside.size() > 0
            side = sellside.get(0)
            float sum = side.vol
            if buyside.size() > 0 and sellside.size() > 0
                sum := buyside.get(0).vol + sellside.get(0).vol
                sum

            if sidemode == 'Area'
                bin.bx.unshift(box.new(top = side.top, bottom = side.btm, left = side.loc, right = time, border_color = na, bgcolor = side.css, xloc = xloc.bar_time))

            bin.ln.unshift(line.new(x1 = side.loc, x2 = time, y1 = side.top, y2 = side.top, xloc = xloc.bar_time, color = color.new(side.css, 0), style = line.style_solid))
            bin.ln.unshift(line.new(x1 = bar_index, x2 = bar_index + 10, y1 = side.top, y2 = side.top, xloc = xloc.bar_index, color = color.new(side.css, 0), style = line.style_dotted))

            bin.lb.unshift(label.new(x = bar_index, y = side.top, color = #ffffff00, style = label.style_label_down, textcolor = color.new(side.css, 0), size = mstext.txSz(), text = 'Sellside - ' + str.tostring(math.floor(side.vol / sum * 100)) + '%'))

    if blob.size() > 0
        ob = blob.get(0)
        if not ob.isbb
            if low < ob.top
                blalert
                if blobenter.normal == false

                    blobenter.normal := true
                    blobenter.normal
        else
            if high > ob.btm
                blalert
                if blobenter.breaker == false

                    blobenter.breaker := true
                    blobenter.breaker

    if brob.size() > 0
        ob = brob.get(0)
        if not ob.isbb
            if high > ob.btm
                bralert
                if brobenter.normal == false

                    brobenter.normal := true
                    brobenter.normal
        else
            if low < ob.top
                bralert
                if brobenter.breaker == false

                    brobenter.breaker := true
                    brobenter.breaker


    if obshow and oblast > 0
        if barstate.isconfirmed
            blob.mitigated()
            brob.mitigated()
            if overlap
                overlap(blob, brob)

        if blob.size() > 0
            for [i, metric] in blob
                metric.umt()

        if brob.size() > 0
            for [i, metric] in brob
                metric.umt()

        if barstate.islast
            if blob.size() > 0
                for i = 0 to math.min(oblast - 1, blob.size() - 1) by 1
                    obs = blob.get(i)
                    display(obs, blob, i)

            if brob.size() > 0
                for i = 0 to math.min(oblast - 1, brob.size() - 1) by 1
                    obs = brob.get(i)
                    display(obs, brob, i)

    if barstate.islast and draw and bldw.size() > 0 and brdw.size() > 0
        for i = 0 to bldw.size() - 1 by 1
            obj = bldw.get(i)
            if i <= limit
                bin.ln.unshift(line.new(x1 = obj.x1, x2 = obj.x2, y1 = obj.y, y2 = obj.y, color = obj.css, style = obj.style, xloc = xloc.bar_time))
                bin.lb.unshift(label.new(x = int(math.avg(bin.ln.get(0).get_x1(), bin.ln.get(0).get_x2())), y = obj.y, xloc = xloc.bar_time, color = #ffffff00, style = label.style_label_down, textcolor = obj.css, size = mstext.txSz(), text = obj.txt))

                if msbubble
                    bin.lb.unshift(label.new(x = obj.x1, y = obj.y, xloc = xloc.bar_time, color = color.new(obj.css, 80), style = label.style_circle, size = size.tiny))

        for i = 0 to brdw.size() - 1 by 1
            obj = brdw.get(i)
            if i <= limit
                bin.ln.unshift(line.new(x1 = obj.x1, x2 = obj.x2, y1 = obj.y, y2 = obj.y, color = obj.css, style = obj.style, xloc = xloc.bar_time))
                bin.lb.unshift(label.new(x = int(math.avg(bin.ln.get(0).get_x1(), bin.ln.get(0).get_x2())), y = obj.y, xloc = xloc.bar_time, color = #ffffff00, style = label.style_label_up, textcolor = obj.css, size = mstext.txSz(), text = obj.txt))

                if msbubble
                    bin.lb.unshift(label.new(x = obj.x1, y = obj.y, xloc = xloc.bar_time, color = color.new(obj.css, 80), style = label.style_circle, size = size.tiny))

    ms

//}
_ = '   ------------         ––––––––––––––––––––––––––  EXECUTION –––––––––––––––––––––––––––  ------------                                                                                                                                                                        ' //{                                                         

structure ms = na

if windowsis
    if bar_index > last_bar_index - mswindow
        ms := structure(swingcssup, swingcssdn, showSwing, false, swingLimit)
        ms
if windowsis == false
    ms := structure(swingcssup, swingcssdn, showSwing, false, swingLimit)
    ms

if showInternal and inZone
    structure ims = structure(interncssup, interncssdn, showInternal, true, swingLimit)
    ims


color css = na

method darkcss(color css, float factor) =>

    blue = color.b(css) * (1 - factor)
    red = color.r(css) * (1 - factor)
    green = color.g(css) * (1 - factor)

    color.rgb(red, green, blue, 0)

if windowsis ? bar_index > last_bar_index - mswindow : true
    css := ms.trend == 1 ? swingcssup : swingcssdn
    css := ms.txt == 'bos' ? css : css.darkcss(0.3)
    css


barcolor(candlecss ? css : na)


if lc.start and showzone and (windowsis ? bar_index > last_bar_index - mswindow : true)

    v = close > open ? volume : -volume
    switch ms.trend 
        -1 => 
    	    if lc.count <= zonethresh
    	        if high > ms.zz and close < ms.zz
    	            lc.count := 0
    	            lc.count
    	        if close > ms.zz
    	            blalerts.set(11, alert.new(true, 'Bullish', 'Sweep Area\n'))
    	            lc.start := false
    	            lc.count := 0
    	            array<float> pp = array.new<float>()
    	            for i = 0 to bar_index - ms.zn by 1
    	                pp.unshift(low[i])

    	            box.new(top = ms.zz, bottom = pp.min(), left = ms.zn - 1, right = bar_index + 1, border_color = na, bgcolor = dnzone)
    	            line.new(x1 = ms.zn, x2 = bar_index, y1 = ms.zz, y2 = ms.zz, color = swingcssdn, style = line.style_dotted)
        1 => 

    	    if lc.count <= zonethresh
    	        if low < ms.zz and close > ms.zz
    	            lc.count := 0
    	            lc.count
    	        if close < ms.zz
    	            bralerts.set(11, alert.new(true, 'Bearish', 'Sweep Area\n'))
    	            lc.start := false
    	            lc.count := 0
    	            array<float> pp = array.new<float>()
    	            for i = 0 to bar_index - ms.zn by 1
    	                pp.unshift(high[i])

    	            box.new(top = pp.max(), bottom = ms.zz, left = ms.zn - 1, right = bar_index + 1, border_color = na, bgcolor = upzone)
    	            line.new(x1 = ms.zn, x2 = bar_index, y1 = ms.zz, y2 = ms.zz, color = swingcssup, style = line.style_dotted)

    if lc.count > zonethresh
        lc.start := false
        lc.count := 0
        lc.count


    lc.count := lc.count + 1
    lc.count




if fvg_enable
    dFVG()

if windowsis ? bar_index > last_bar_index - mswindow : true
    if dispSFP
        ms.SFP()

if ehlshow
    dEHL()

if showMapping
    mapping()

[uLIQ, dLIQ] = posLIQ()

plotcandle(high, high, math.max(close, open), math.max(close, open), color = na, wickcolor = na, bordercolor = uLIQ and liqprint ? printupcs : na)
plotcandle(low, low, math.min(close, open), math.min(close, open), color = na, wickcolor = na, bordercolor = dLIQ and liqprint ? printdowncs : na)

if lvl_daily
    mtfphl(pdh, pdl, 'D', css_d, s_d)

if lvl_weekly
    mtfphl(pwh, pwl, 'W', css_w, s_w)

if lvl_monthly
    mtfphl(pmh, pml, 'M', css_m, s_m)

if lvl_quartely
    mtfphl(pqh, pql, 'Q', css_q, s_q)

if lvl_yearly
    mtfphl(pyh, pyl, 'Y', css_y, s_y)




var phl = Zphl.new(na, na, label.new(na, na, color = invcol, textcolor = swingcssdn, style = label.style_label_down, size = size.tiny, text = ''), label.new(na, na, color = invcol, textcolor = swingcssup, style = label.style_label_up, size = size.tiny, text = ''), true, true, true, true, '', '', 0, 0, 0, 0, high, low, 0, 0, 0, 0, 0, 0, na, na)

zhl(len) =>

    upper = ta.highest(len)
    lower = ta.lowest(len)

    var float out = 0
    out := high[len] > upper ? 0 : low[len] < lower ? 1 : out[1]

    top = out == 0 and out[1] != 0 ? high[len] : 0
    btm = out == 1 and out[1] != 1 ? low[len] : 0

    [top, btm]

[top, btm] = zhl(zonelen)

// upphl(trend) =>

//     var label lbl = label.new(
//        na
//      , na
//      , color     = invcol
//      , textcolor = swingcssdn
//      , style     = label.style_label_down
//      , size      = size.small
//      )

//     if top

//         phl.stopcross := true
//         phl.txtup     := top > phl.topy ? "HH" : "HL"
//         phl.uV        := volume[zonelen]

//         if showhl
//             line.delete(phl.top[1])

//             phl.top := line.new(
//                  bar_index- zonelen
//                  , top
//                  , bar_index
//                  , top
//                  , color = swingcssdn)

//         phl.topy      := top
//         phl.topx      := bar_index - zonelen
//         phl.tup       := top
//         phl.tupx      := bar_index - zonelen

//     if high > phl.tup
//         phl.uV := volume

//     phl.tup           := math.max(high, phl.tup)
//     phl.tupx          := phl.tup == high ? bar_index: phl.tupx

//     if barstate.islast and showhl

//         line.set_xy1(
//                phl.top
//              , phl.tupx
//              , phl.tup
//              )

//         line.set_xy2(
//                phl.top
//              , bar_index+ 20
//              , phl.tup
//              )

//         label.set_x(
//                lbl
//              , bar_index+ 20
//              )

//         label.set_y(
//                lbl
//              , phl.tup
//              )

//         dist = math.abs(phl.uV / (phl.uV + phl.dV)) * 100
//         label.set_text (lbl, trend < 0 
//              ? "Strong High | " + str.tostring(phl.uV, format.volume) + " (" + str.tostring(math.round(dist,0)) + "%)" 
//              : "Weak High | "   + str.tostring(phl.uV, format.volume) + " (" + str.tostring(math.round(dist,0)) + "%)")

// dnphl(trend) =>

//     var label lbl = label.new(
//        na
//      , na
//      , color     = invcol
//      , textcolor = swingcssup
//      , style     = label.style_label_up
//      , size      = size.small
//      )

//     if btm

//         phl.sbottomcross := true
//         phl.txtdn        := btm > phl.bottomy ? "LH" : "LL"
//         phl.dV           := volume[zonelen]

//         if showhl
//             line.delete(phl.bottom[1])

//             phl.bottom := line.new(
//              bar_index- 50
//              , btm
//              , bar_index
//              , btm
//              , color = swingcssup
//              )

//         phl.bottomy      := btm
//         phl.bottomx      := bar_index- zonelen
//         phl.tdn          := btm
//         phl.tdnx         := bar_index- zonelen

//     if low < phl.tdn
//         phl.dV := volume

//     phl.tdn              := math.min(low, phl.tdn)
//     phl.tdnx             := phl.tdn == low ? bar_index: phl.tdnx


//     if barstate.islast and showhl

//         line.set_xy1(
//            phl.bottom
//          , phl.tdnx
//          , phl.tdn
//          )

//         line.set_xy2(
//            phl.bottom
//          , bar_index+ 20
//          , phl.tdn
//          )

//         label.set_x(
//            lbl
//          , bar_index+ 20
//          )

//         label.set_y(
//            lbl
//          , phl.tdn
//          )

//         dist = math.abs(phl.dV / (phl.uV + phl.dV)) * 100
//         label.set_text (lbl, trend > 0 
//              ? "Strong Low | " + str.tostring(phl.dV, format.volume) + " (" + str.tostring(math.round(dist,0)) + "%)" 
//              : "Weak Low | "   + str.tostring(phl.dV, format.volume) + " (" + str.tostring(math.round(dist,0)) + "%)")

// midphl() =>
//     if showpdzone
//         avg = math.avg(phl.tdn, phl.tup)

//         var line l = line.new(
//          y1 = avg
//          , y2 = avg
//          , x1 = bar_index- zonelen
//          , x2 = bar_index+ 20
//          , color = color.yellow
//          , style = line.style_solid
//          )

//         var label lbl = label.new(
//          x = bar_index+ 20
//          , y = avg
//          , text = "0.5"
//          , style = label.style_label_left
//          , color = invcol
//          , textcolor = fibcss
//          , size = size.small
//          )

//         if barstate.islast and showpdzone

//             more = (phl.tdnx + bar_index+ 20) > (phl.tupx + bar_index+ 20) ? phl.tupx : phl.tdnx
//             line.set_xy1(l   , more    , avg)
//             line.set_xy2(l   , bar_index+ 20, avg)
//             label.set_x (lbl , bar_index+ 20     )
//             label.set_y (lbl , avg          )
//             dist = math.abs((l.get_y2() - close) / close) * 100
//             label.set_text (lbl, "0.5")

// hqlzone() =>

//     if barstate.islast and showpdzone

//         var hqlzone dZone = hqlzone.new(
//            box.new(
//                na
//              , na
//              , na
//              , na
//              , bgcolor = color.new(swingcssdn, 70)
//              , border_color = na
//              )
//          , na
//          , box.new(
//                na
//              , na
//              , na
//              , na
//              , bgcolor = color.new(swingcssup, 70)
//              , border_color = na
//              )

//          , label.new(na, na, text = "Premium"    , color = invcol, textcolor = swingcssdn, style = label.style_label_down, size = size.small)
//          , na
//          , label.new(na, na, text = "Discount"   , color = invcol, textcolor = swingcssup, style = label.style_label_up  , size = size.small)
//          )

//         dZone.pbx.set_lefttop(int(math.max(phl.topx, phl.bottomx))                          , phl.tup)
//         dZone.pbx.set_rightbottom(bar_index+ 20                        , 0.95  * phl.tup + 0.05  * phl.tdn)

//         dZone.lbx.set_lefttop(int(math.max(phl.topx, phl.bottomx)), 0.95  * phl.tdn + 0.05  * phl.tup)
//         dZone.lbx.set_rightbottom(bar_index+ 20                                                  , phl.tdn)


//         dZone.plb.set_xy( int(math.avg(math.max(phl.topx, phl.bottomx), int(bar_index+ 20))) , phl.tup)
//         dZone.lbl.set_xy( int(math.avg(math.max(phl.topx, phl.bottomx), int(bar_index+ 20))) , phl.tdn)

// if windowsis ? (bar_index > last_bar_index - mswindow) : true
//     upphl (ms.trend)
//     dnphl (ms.trend)
//     midphl()
//     hqlzone()

//}

// // _                                                                                                                                                                                                                                        ='  
// //                                                      ------------        
// //                            ––––––––––––––––––––––––––  ANYALERT  ––––––––––––––––––––––––––– 
// //                                                      ------------                                                                                                                                                                        '//{
bool allow = last_bar_index - bar_index < 400

// LEVELS {
bool display_lvl = input.bool(true, '', group = 'Levels', inline = '1')
int length = input(20, 'Period', group = 'Levels', inline = '1')
string style1 = input.string('Solid', '', options = ['Solid', 'Dashed'], group = 'Levels', inline = '1')
float filter = 20

color up = input.color(color.rgb(35, 184, 80, 30), '', group = 'Levels', inline = '1')
color dn = input.color(color.rgb(255, 82, 82, 30), '', group = 'Levels', inline = '1')

series float ph = ta.pivothigh(open, length, length)
series float pl = ta.pivotlow(close, length, length)

array<float> vol1 = array.new<float>()
var array<line> lines = array.new<line>(10)

array<box> boxes = array.new<box>()

// Method to normalize values between 0 and 100
method normalize(float src) =>
    int coef = 100
    float value = (src - ta.lowest(src, 200)) / (ta.highest(src, 200) - ta.lowest(src, 200)) * coef
    value

// Calculating normalized volatility       
series float volatility = ta.sma(high - low, 200)
series float n_volatility = (high - low).normalize()

high_ = close > open ? close : open
low_ = close > open ? open : close

if allow and display_lvl

    style = switch style1
        'Solid' => line.style_solid
        'Dashed' => line.style_dashed
        => line.style_solid


    if not na(ph)

        int x1 = bar_index[length]
        int x2 = bar_index[length]
        float y1 = high_[length] + volatility * 2.5
        float y2 = high_[length] - volatility * 2.5
        float y = high_[length]

        if n_volatility[length] > filter
            lines.push(line.new(x1, y, x2, y, color = up, width = 1, style = style, force_overlay = true))
            boxes.push(box.new(x1, y1, x2, y2, border_width = 0, bgcolor = color(na)))

    if not na(pl)

        int x1 = bar_index[length]
        int x2 = bar_index[length]
        float y1 = low_[length] + volatility * 1.5
        float y2 = low_[length] - volatility * 1.5
        float y = low_[length]

        if n_volatility[length] > filter
            lines.push(line.new(x1, y, x2, y, color = dn, width = 1, style = style))
            boxes.push(box.new(x1, y1, x2, y2, border_width = 0, bgcolor = color(na)))



for i = 0 to lines.size() - 1 by 1
    for line_id in lines
        float line_y = line_id.get_y1()

        if close > line_y
            line_id.set_color(up)
        else
            line_id.set_color(dn)

        if line_id.get_x1() != line_id.get_x2()
            line_id.set_extend(extend.right)


for box_id in boxes
    float top = box_id.get_top()
    float bottom = box_id.get_bottom()

    for line_id in lines
        float line_y = line_id.get_y2()


        if line_y > bottom and line_y < top
            line_id.set_x2(bar_index + 500)


if lines.size() >= 15
    line.delete(lines.shift())

line.new(na, na, na, na)
// }


// ORDER BLOCKS -- {
int box_amount = 20
bool display_ob = input.bool(false, '', inline = '2', group = 'OrderBlocks')
color col_bull = input.color(color.rgb(20, 190, 147, 70), '', inline = '2', group = 'OrderBlocks')
color col_bear = input.color(color.rgb(194, 25, 25, 70), '', inline = '2', group = 'OrderBlocks')


bool bear_candle = close < open
bool bear_candles = bear_candle or bear_candle[1] or bear_candle[2] or bear_candle[3] or bear_candle[4]
bool bull_candle = close > open
bool bull_candles = bull_candle or bull_candle[1] or bull_candle[2] or bull_candle[3] or bull_candle[4]

bool isBull_gap = high[2] < low and high[2] < high[1] and low[2] < low and low - high[2] > atr * 0.5 and bear_candles
bool isBear_gap = low[2] > high and low[2] > low[1] and high[2] > high and low[2] - high > atr * 0.5 and bull_candles

// Logic for Bullish Imbalance
method draw_ob(bool gap, candle_type) =>

    var boxes1 = array.new<box>(box_amount, box(na))
    var candle_ = bool(na)
    src_l = close
    src_h = open

    if candle_type
        candle_ := close < open
        src_l := close
        src_h := open
        src_h
    else
        candle_ := close > open
        src_l := open
        src_h := close
        src_h


    if gap
        low_1 = candle_[1] ? src_l[1] : candle_[2] ? src_l[2] : candle_[3] ? src_l[3] : candle_[4] ? src_l[4] : src_l[2]
        high_1 = candle_[1] ? src_h[1] : candle_[2] ? src_h[2] : candle_[3] ? src_h[3] : candle_[4] ? src_h[4] : src_h[2]

        index = candle_[1] ? 1 : candle_[2] ? 2 : candle_[3] ? 3 : candle_[4] ? 4 : 2
        box1 = box.new(bar_index - index, high_1, last_bar_index, low_1, chart.fg_color, 1, bgcolor = candle_type ? col_bull : col_bear, text_size = size.small, text_color = chart.fg_color, text_halign = text.align_right)
        boxes1.push(box1)

    for box_id in boxes1
        box.set_right(box_id, bar_index + 25)

        if candle_type ? high < box.get_bottom(box_id) : low > box.get_top(box_id)
            box.delete(box_id)
            boxes1.set(boxes1.indexof(box_id), box(na))

        for box_id1 in boxes1
            top1 = box_id1.get_top()
            top = box_id.get_top()
            bottom1 = box_id1.get_bottom()
            bottom = box_id.get_bottom()

            if top1 < top and top1 > bottom
                box.delete(box_id)
                boxes1.set(boxes1.indexof(box_id), box(na))

            if top - bottom < atr * 0.5
                box.delete(box_id)
                boxes1.set(boxes1.indexof(box_id), box(na))

    if boxes1.size() >= box_amount
        box.delete(boxes1.shift())

if display_ob and allow
    isBull_gap.draw_ob(true)
    isBear_gap.draw_ob(false)
    // }


// TrendLines {
bool display_tren = input.bool(false, '', group = 'TrendLine', inline = '3')
int Period = input.int(10, 'Period', group = 'TrendLine', inline = '3')
string style2 = input.string('Solid', '', options = ['Solid', 'Dashed'], group = 'TrendLine', inline = '3')
int extend_ = input.int(10, 'Extend', group = 'TrendLine', inline = '3')

color LineCol1 = input.color(#1bd663, '', group = 'TrendLine', inline = '4')
color LineCol2 = input.color(#d61b7f, '', group = 'TrendLine', inline = '4')

var array<line> Lines = array.new<line>(5)

PH = ta.pivothigh(source = high, leftbars = Period, rightbars = Period)
PL = ta.pivotlow(source = low, leftbars = Period, rightbars = Period)

method draw_trendLine(float pivot, bool line_slope) =>
    var int Start = 0
    var int end = 0
    var int TIME = 1
    var float YEnd = 0
    var float YStart = 0
    var float Slope = 0
    var float src = 0

    style = switch style2
        'Solid' => line.style_solid
        'Dashed' => line.style_dashed
        => line.style_solid

    if not na(pivot)
        BarTIME = time - time[1]
        src := pivot
        TIME := time[Period]
        YStart := src[1]
        Start := TIME[1]
        Slope := (src - YStart) / (TIME - Start)

        extend = BarTIME * extend_ * 2
        end := TIME + extend
        YEnd := src + extend * Slope
        LineCond = Slope * time < 0 ? line_slope ? na : LineCol1 : line_slope ? LineCol2 : na

        if not na(LineCond)
            Lines.push(line.new(x1 = Start, y1 = YStart, x2 = end, y2 = YEnd, xloc = xloc.bar_time, color = LineCond, width = 1, style = style))

if display_tren and allow
    PH.draw_trendLine(false)
    PL.draw_trendLine(true)

if Lines.size() > 1
    line.delete(Lines.shift())
// }
