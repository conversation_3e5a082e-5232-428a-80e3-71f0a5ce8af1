//@version=6
indicator("Function Syntax Test v6 Final", overlay=true)

// This script uses the definitive syntax learned from PAH.pine (a v6 script).
// 1. Functions are assigned to variables.
// 2. The `=>` operator is used.
// 3. Parameter types ARE DECLARED inside the parentheses.
// 4. The last expression is the implicit return value.

// --- Test Function with correct v6 syntax ---
testFunction = (int inputNumber) =>
    prefix = "Input was: "
    result = prefix + str.tostring(inputNumber)
    result // Implicit return

// --- Main Logic ---
if barstate.islastconfirmed
    // Call the function via the variable.
    string result = testFunction(123)
    
    // This must compile.
    label.new(bar_index, high, result, color=color.green, textcolor=color.white)
