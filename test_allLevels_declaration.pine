//@version=5
indicator("Test Case 1: Array Persistence ('allLevels')", overlay=true)

// --- ERROR REPRODUCTION ---
// This array is declared WITHOUT 'var'. It will be reset on every bar.
float[] levels_error = array.new<float>()

// On the first bar only, we add a value to the array.
if bar_index == 1
    array.push(levels_error, 100.0)

// We expect the size to be 1 on all subsequent bars, but it will be 0 because the array is reset.
// This label will show "Array Size: 0" on every bar after the first, proving state is lost.
label.new(bar_index, high, "ERROR VERSION\nArray Size: " + str.tostring(array.size(levels_error)), 
          color=color.red, textcolor=color.white, style=label.style_label_down)


// --- CORRECTED VERSION ---
// This array is declared WITH 'var'. Its state will persist across bars.
var float[] levels_correct = array.new<float>()

// On the first bar only, we add a value to the array.
if bar_index == 1
    array.push(levels_correct, 100.0)

// The size will correctly remain 1 on all subsequent bars because the state is preserved.
// This label will show "Array Size: 1", proving the fix works.
label.new(bar_index, low, "CORRECTED VERSION\nArray Size: " + str.tostring(array.size(levels_correct)), 
          color=color.green, textcolor=color.white, style=label.style_label_up)
