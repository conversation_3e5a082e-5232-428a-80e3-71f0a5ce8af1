//@version=6
indicator("Manual Array Sort Test", overlay=true)

// --- User-Defined Type ---
type Level
    int priority
    float price
    string name

// --- Manual Sort Function (Insertion Sort) ---
// This function takes an array of 'Level' objects and returns a new, sorted array.
// It sorts based on the 'priority' field in ascending order.
sortLevelsByPriority(array<Level> unsortedArray) =>
    array<Level> sortedArray = array.new<Level>()
    if array.size(unsortedArray) > 0
        for level_to_insert in unsortedArray
            bool inserted = false
            if array.size(sortedArray) > 0
                for i = 0 to array.size(sortedArray) - 1
                    sorted_level = array.get(sortedArray, i)
                    if level_to_insert.priority < sorted_level.priority
                        array.insert(sortedArray, i, level_to_insert)
                        inserted := true
                        break
            if not inserted
                array.push(sortedArray, level_to_insert)
    sortedArray

// --- Array Initialization ---
var levelArray = array.new<Level>()

// Populate the array only on the first bar
if barstate.isfirst
    array.push(levelArray, Level.new(2, 105.5, "Level A"))
    array.push(levelArray, Level.new(1, 102.0, "Level B"))
    array.push(levelArray, Level.new(3, 102.0, "Level C"))
    array.push(levelArray, Level.new(1, 108.0, "Level D"))

// --- Sorting Logic (v6 Compliant) ---
// We call our manual sort function and assign the new, sorted array back to our variable.
levelArray := sortLevelsByPriority(levelArray)

// --- Visualization ---
// Draw labels on the last bar to display the final, sorted order of the array.
if barstate.islast
    string labelText = "Manually Sorted Array by Priority:\n"
    for i = 0 to array.size(levelArray) - 1
        level = array.get(levelArray, i)
        labelText += "Name: " + level.name + ", Prio: " + str.tostring(level.priority) + ", Price: " + str.tostring(level.price) + "\n"
    
    label.new(bar_index, high, labelText, yloc = yloc.abovebar, style=label.style_label_left)
